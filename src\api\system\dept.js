import request from '@/utils/request';

// 查询机构列表
export function listDept(query) {
    return request({
        url: '/system/dept/list',
        method: 'get',
        params: query,
    });
}
// 查询机构列表（排除节点）
export function listDeptExcludeChild(deptId) {
    return request({
        url: '/system/dept/list/exclude/' + deptId,
        method: 'get',
    });
}

// 查询机构类型
export function getDeptType(deptType, showOwner) {
    return request({
        url: '/system/dept/getDeptType?deptType=' + deptType + '&showOwner=' + showOwner,
        method: 'get',
    });
}

// 查询机构详细
export function getDept(deptId) {
    return request({
        url: '/system/dept/' + deptId,
        method: 'get',
    });
}

// 新增机构
export function addDept(data) {
    return request({
        url: '/system/dept',
        method: 'post',
        data: data,
    });
}

// 修改机构
export function updateDept(data) {
    return request({
        url: '/system/dept',
        method: 'put',
        data: data,
    });
}

// 删除机构
export function delDept(deptId) {
    return request({
        url: '/system/dept/' + deptId,
        method: 'delete',
    });
}
