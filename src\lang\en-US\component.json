{"components.Crontab.day.304304-0": "Day, allowed wildcard characters [, - *?/L W]", "components.Crontab.day.304304-1": "Not specified", "components.Crontab.day.304304-2": "Cycle from", "components.Crontab.day.304304-3": "day", "components.Crontab.day.304304-4": "from", "components.Crontab.day.304304-5": "Starting from number, every", "components.Crontab.day.304304-6": "Execute once a day", "components.Crontab.day.304304-7": "monthly", "components.Crontab.day.304304-8": "The most recent working day", "components.Crontab.day.304304-9": "On the last day of this month", "components.Crontab.day.304304-10": "appoint", "components.Crontab.day.304304-11": "Multiple options available", "components.Crontab.hour.304304-0": "Hours, allowed wildcards [, - */]", "components.Crontab.hour.304304-1": "Cycle from", "components.Crontab.hour.304304-2": "hour", "components.Crontab.hour.304304-3": "from", "components.Crontab.hour.304304-4": "Starting from the hour, every", "components.Crontab.hour.304304-5": "Execute every hour", "components.Crontab.hour.304304-6": "appoint", "components.Crontab.index.464657-0": "second", "components.Crontab.index.464657-1": "minute", "components.Crontab.index.464657-2": "hour", "components.Crontab.index.464657-3": "day", "components.Crontab.index.464657-4": "month", "components.Crontab.index.464657-5": "week", "components.Crontab.index.464657-6": "year", "components.Crontab.index.464657-7": "Time expression", "components.Crontab.index.464657-8": "Cron expression", "components.Crontab.index.464657-9": "determine", "components.Crontab.index.464657-10": "Reset", "components.Crontab.index.464657-11": "cancel", "components.Crontab.min.411657-0": "Minutes, allowed wildcards [, - */]", "components.Crontab.min.411657-1": "Cycle from", "components.Crontab.min.411657-2": "minute", "components.Crontab.min.411657-3": "from", "components.Crontab.min.411657-4": "Starting from minutes, every", "components.Crontab.min.411657-5": "Execute every minute", "components.Crontab.min.411657-6": "appoint", "components.Crontab.month.382453-0": "Month, allowed wildcard characters [, - */]", "components.Crontab.month.382453-1": "Cycle from", "components.Crontab.month.382453-2": "month", "components.Crontab.month.382453-3": "from", "components.Crontab.month.382453-4": "Starting from the month, every", "components.Crontab.month.382453-5": "Execute once a month", "components.Crontab.month.382453-6": "appoint", "components.Crontab.month.382453-7": "Multiple options available", "components.Crontab.second.452546-0": "Seconds, allowed wildcards [, - */]", "components.Crontab.second.452546-1": "Cycle from", "components.Crontab.second.452546-2": "second", "components.Crontab.second.452546-3": "from", "components.Crontab.second.452546-4": "Starting from seconds, every", "components.Crontab.second.452546-5": "Execute every second", "components.Crontab.second.452546-6": "appoint", "components.Crontab.second.452546-7": "Multiple options available", "components.Crontab.week.903494-0": "Week, allowed wildcard characters [, - */]", "components.Crontab.week.903494-1": "Not specified", "components.Crontab.week.903494-2": "Cycle from week", "components.Crontab.week.903494-3": "Section", "components.Crontab.week.903494-4": "The week of the week", "components.Crontab.week.903494-5": "Last week of this month", "components.Crontab.week.903494-6": "appoint", "components.Crontab.week.903494-7": "Monday", "components.Crontab.week.903494-8": "Tuesday", "components.Crontab.week.903494-9": "Wednesday", "components.Crontab.week.903494-10": "Thursday", "components.Crontab.week.903494-11": "Friday", "components.Crontab.week.903494-12": "Saturday", "components.Crontab.week.903494-13": "Sunday", "components.Crontab.year.999034-0": "Not filled in, allowed wildcard characters [, - */]", "components.Crontab.year.999034-1": "annually", "components.Crontab.year.999034-2": "Cycle from", "components.Crontab.year.999034-3": "from", "components.Crontab.year.999034-4": "Starting from the year, every", "components.Crontab.year.999034-5": "Once a year", "components.Crontab.year.999034-6": "appoint", "components.Crontab.year.999034-7": "Multiple options available", "components.FileUpload.index.232435-0": "Select file", "components.FileUpload.index.232435-1": "Please upload", "components.FileUpload.index.232435-2": "Size not exceeding", "components.FileUpload.index.232435-3": "Format is", "components.FileUpload.index.232435-4": "Files for", "components.FileUpload.index.232435-5": "delete", "components.FileUpload.index.232435-6": "The file format is incorrect, please upload it", "components.FileUpload.index.232435-7": "Format file", "components.FileUpload.index.232435-8": "The size of the uploaded file cannot exceed", "components.FileUpload.index.232435-9": "Uploading file, please wait", "components.FileUpload.index.232435-10": "The number of uploaded files cannot exceed", "components.FileUpload.index.232435-11": "Uploading file failed, please try again", "components.IconSelect.index.540409-0": "Please enter the icon name", "components.ImageUpload.384733-0": "preview", "components.ImageUpload.384733-1": "The size of the uploaded avatar image cannot exceed", "components.ImageUpload.384733-2": "Uploading image, please wait", "components.ImageUpload.384733-3": "The number of uploaded files cannot exceed", "components.ImageUpload.384733-4": "A!", "components.ImageUpload.384733-5": "Uploading image failed, please try again", "components.RightToolbar.390493-0": "Hide Search", "components.RightToolbar.390493-1": "Show Search", "components.RightToolbar.390493-2": "Refresh", "components.RightToolbar.390493-3": "Explicit implicit column", "components.RightToolbar.390493-4": "display", "components.RightToolbar.390493-5": "hide", "components.RightToolbar.390493-6": "Show/Hide", "components.result.893023-0": "Last 5 run times", "components.result.893023-1": "In the calculation results", "components.result.893023-2": "The result did not meet the conditions!", "components.result.893023-3": "In the past 100 years, only the top", "components.result.893023-4": "Results!"}