<template>
    <div>
        <video :id="detail.identifier" controls autoplay muted :width="this.detail.style.position.w" :height="this.detail.style.position.h" style="background-color: black"></video>
        <div v-show="false">{{ videoUrl }}</div>
    </div>
</template>

<script>
import flvjs from 'flv.js';

import BaseView from './View';

export default {
    name: 'view-text',
    extends: BaseView,
    props: {},
    data() {
        return {
            flvPlayer: null,
        };
    },
    computed: {
        videoUrl() {
            this.$nextTick(function () {
                if (flvjs.isSupported()) {
                    var videoElement = document.getElementById(this.detail.identifier);
                    this.flvPlayer = flvjs.createPlayer({
                        type: 'flv',
                        isLive: true,
                        hasAudio: false,
                        url: this.detail.videoUrl,
                    });
                    this.flvPlayer.attachMediaElement(videoElement);
                    this.flvPlayer.load();
                    this.flvPlayer.play();
                }
            });
            return this.detail.style.position.h;
        },
    },
    mounted() {
        if (flvjs.isSupported()) {
            var videoElement = document.getElementById(this.detail.identifier);
            this.flvPlayer = flvjs.createPlayer({
                type: 'flv',
                isLive: true,
                hasAudio: false,
                url: this.detail.videoUrl,
            });
            this.flvPlayer.attachMediaElement(videoElement);
            this.flvPlayer.load();
            this.flvPlayer.play();
        }
    },
    methods: {
        play() {
            this.flvPlayer.play();
        },
    },
};
</script>

<style lang="scss">
.view-text {
    height: 100%;
    width: 100%;
}
.demo {
    padding: 5px;
    background-color: rgba(0, 206, 209, 0);
}
</style>
