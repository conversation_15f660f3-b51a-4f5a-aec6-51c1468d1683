{"build.index.2090840-0": "Input type components", "build.index.2090840-1": "Selective component", "build.index.2090840-2": "Layout type components", "build.index.2090840-3": "Export Vue file", "build.index.2090840-4": "Copy Code", "build.index.2090840-5": "empty", "build.index.2090840-6": "Drag or click on components from the left for form design", "build.index.2090840-7": "Select generation type", "build.index.2090840-8": "The code has been copied to the clipboard and can be pasted.", "build.index.2090840-9": "Code copying failed", "build.index.2090840-10": "Are you sure you want to clear all components?", "build.index.2090840-11": "prompt", "build.index.2090840-12": "Generate type", "build.index.2090840-13": "file name", "build.index.2090840-14": "Please enter a file name", "build.index.2090840-15": "The generation type cannot be empty", "build.index.2090840-16": "page", "build.index.2090840-17": "pop-up notification", "build.index.2090840-18": "Select icon", "build.index.2090840-19": "Please enter the icon name", "build.index.2090840-20": "Component Properties", "build.index.2090840-21": "form properties ", "build.index.2090840-22": "View component documentation", "build.index.2090840-23": "Component type", "build.index.2090840-24": "Please select component type", "build.index.2090840-25": "Field Name", "build.index.2090840-26": "Please enter the field name (v-model)", "build.index.2090840-27": "Component Name", "build.index.2090840-28": "title", "build.index.2090840-29": "Please enter a title", "build.index.2090840-30": "Placeholder prompt", "build.index.2090840-31": "Please enter a placeholder prompt", "build.index.2090840-32": "Start occupying space", "build.index.2090840-33": "End placeholder", "build.index.2090840-34": "Form Grid", "build.index.2090840-35": "Grid spacing", "build.index.2090840-36": "Layout mode", "build.index.2090840-37": "Horizontal arrangement", "build.index.2090840-38": "Please select horizontal arrangement", "build.index.2090840-39": "Label width", "build.index.2090840-40": "Please enter the label width", "build.index.2090840-41": "Component width", "build.index.2090840-42": "Please enter the width of the component", "build.index.2090840-43": "Default value", "build.index.2090840-44": "Please enter default values", "build.index.2090840-45": "At least one should be selected", "build.index.2090840-46": "Up to optional", "build.index.2090840-47": "prefix", "build.index.2090840-48": "Please enter a prefix", "build.index.2090840-49": "suffix", "build.index.2090840-50": "Please enter the suffix", "build.index.2090840-51": "Front icon", "build.index.2090840-52": "Please enter the name of the front icon", "build.index.2090840-53": "Rear icon", "build.index.2090840-54": "Please enter the name of the rear icon", "build.index.2090840-55": "Option delimiter", "build.index.2090840-56": "Please enter option delimiter", "build.index.2090840-57": "Minimum number of rows", "build.index.2090840-58": "Maximum number of rows", "build.index.2090840-59": "minimum value", "build.index.2090840-60": "Maximum value", "build.index.2090840-61": "step", "build.index.2090840-62": "Steps", "build.index.2090840-63": "accuracy", "build.index.2090840-64": "Button position", "build.index.2090840-65": "default", "build.index.2090840-66": "Right side", "build.index.2090840-67": "Maximum Input", "build.index.2090840-68": "Please enter the character length", "build.index.2090840-69": "Characters", "build.index.2090840-70": "Enable prompt", "build.index.2090840-71": "Please enter the activation prompt", "build.index.2090840-72": "Turn off prompts", "build.index.2090840-73": "Please enter the shutdown prompt", "build.index.2090840-74": "Open value", "build.index.2090840-75": "Please enter the activation value", "build.index.2090840-76": "Close value", "build.index.2090840-77": "Please enter a shutdown value", "build.index.2090840-78": "Time type", "build.index.2090840-79": "Please select a time type", "build.index.2090840-80": "File Field Name", "build.index.2090840-81": "Please enter the field name for uploading the file", "build.index.2090840-82": "file type", "build.index.2090840-83": "Please select a file type", "build.index.2090840-84": "picture", "build.index.2090840-85": "video", "build.index.2090840-86": "audio frequency", "build.index.2090840-87": "file size", "build.index.2090840-88": "Please enter the file size", "build.index.2090840-89": "Upload address", "build.index.2090840-90": "Please enter the upload address", "build.index.2090840-91": "List Type", "build.index.2090840-92": "Button Text", "build.index.2090840-93": "Please enter the button text", "build.index.2090840-94": "Delimiter", "build.index.2090840-95": "Please enter a separator character", "build.index.2090840-96": "Time period", "build.index.2090840-97": "Please enter a time period", "build.index.2090840-98": "Time format", "build.index.2090840-99": "Please enter the time format", "build.index.2090840-100": "option", "build.index.2090840-101": "Option Name", "build.index.2090840-102": "Option value", "build.index.2090840-103": "Add Options", "build.index.2090840-104": "data type", "build.index.2090840-105": "dynamic data ", "build.index.2090840-106": "Static data", "build.index.2090840-107": "Label key name", "build.index.2090840-108": "Please enter the label key name", "build.index.2090840-109": "Value key name", "build.index.2090840-110": "Please enter the value key name", "build.index.2090840-111": "Child Key Names", "build.index.2090840-112": "Please enter the child key name", "build.index.2090840-113": "Add Parent", "build.index.2090840-114": "Option Style", "build.index.2090840-115": "default", "build.index.2090840-116": "button", "build.index.2090840-117": "Turn on color", "build.index.2090840-118": "Turn off colors", "build.index.2090840-119": "Allow half selection", "build.index.2090840-120": "Auxiliary text", "build.index.2090840-121": "Display scores", "build.index.2090840-122": "Display breakpoints", "build.index.2090840-123": "Range selection", "build.index.2090840-124": "Is it bordered", "build.index.2090840-125": "Color Format", "build.index.2090840-126": "Please choose a color format", "build.index.2090840-127": "Option size", "build.index.2090840-128": "secondary", "build.index.2090840-129": "less", "build.index.2090840-130": "Mini", "build.index.2090840-131": "Input statistics", "build.index.2090840-132": "Strict Steps", "build.index.2090840-133": "Do you want to select multiple options", "build.index.2090840-134": "Show full path", "build.index.2090840-135": "Can we filter it out", "build.index.2090840-136": "Can we clear it", "build.index.2090840-137": "Display prompts", "build.index.2090840-138": "Multiple file selection", "build.index.2090840-139": "Automatic upload", "build.index.2090840-140": "Is it read-only", "build.index.2090840-141": "Is it disabled", "build.index.2090840-142": "Is it searchable", "build.index.2090840-143": "Do you want to select multiple options", "build.index.2090840-144": "Is it mandatory to fill in", "build.index.2090840-145": "Layout Structure Tree", "build.index.2090840-146": "Regular verification", "build.index.2090840-147": "expression", "build.index.2090840-148": "Please enter regularization", "build.index.2090840-149": "Error prompt", "build.index.2090840-150": "Please enter an error message", "build.index.2090840-151": "Add Rule", "build.index.2090840-152": "Form Name", "build.index.2090840-153": "Please enter the form name (ref)", "build.index.2090840-154": "form model ", "build.index.2090840-155": "Please enter the data model", "build.index.2090840-156": "Model Verify ", "build.index.2090840-157": "Please input the validation model", "build.index.2090840-158": "Label alignment", "build.index.2090840-159": "Left aligned", "build.index.2090840-160": "Right aligned", "build.index.2090840-161": "Align Top ", "build.index.2090840-162": "Label width", "build.index.2090840-163": "Grid spacing", "build.index.2090840-164": "Disable forms", "build.index.2090840-165": "Form button", "build.index.2090840-166": "Show unselected component borders", "build.index.2090840-167": "Add Options", "build.index.2090840-168": "Form size", "build.tree.897735-0": "Please enter the option name", "build.tree.897735-1": "Please enter the option value", "build.tree.897735-2": "character string", "build.tree.897735-3": "number", "gen.basicInfoForm.235609-0": "Table Name", "gen.basicInfoForm.235609-1": "Please enter the warehouse name", "gen.basicInfoForm.235609-2": "Table Description", "gen.basicInfoForm.235609-3": "Please enter", "gen.basicInfoForm.235609-4": "Entity class name", "gen.basicInfoForm.235609-5": "author", "gen.basicInfoForm.235609-6": "Please enter the table name", "gen.basicInfoForm.235609-7": "Please enter a table description", "gen.basicInfoForm.235609-8": "Please enter the entity class name", "gen.basicInfoForm.235609-9": "Please enter the author", "gen.editTable.650980-0": "Basic information", "gen.editTable.650980-1": "Field information", "gen.editTable.650980-2": "Number", "gen.editTable.650980-3": "Field column names", "gen.editTable.650980-4": "Field Description", "gen.editTable.650980-5": "Physical type", "gen.editTable.650980-6": "Java type", "gen.editTable.650980-7": "Java Properties", "gen.editTable.650980-8": "insert", "gen.editTable.650980-9": "edit", "gen.editTable.650980-10": "list", "gen.editTable.650980-11": "query", "gen.editTable.650980-12": "Query method", "gen.editTable.650980-13": "Required", "gen.editTable.650980-14": "Display type", "gen.editTable.650980-15": "Text box", "gen.editTable.650980-16": "Text Field", "gen.editTable.650980-17": "Dropdown box", "gen.editTable.650980-18": "Radio ", "gen.editTable.650980-19": "check box", "gen.editTable.650980-20": "Date control", "gen.editTable.650980-21": "Image upload", "gen.editTable.650980-22": "File upload", "gen.editTable.650980-23": "Rich Text Control", "gen.editTable.650980-24": "Dictionary type", "gen.editTable.650980-25": "Generate information", "gen.editTable.650980-26": "The form verification did not pass, please recheck the submitted content", "gen.editTable.650980-27": "return", "gen.genInfoForm.432422-0": "Generate Template", "gen.genInfoForm.432422-1": "Single table (adding, deleting, modifying, and querying)", "gen.genInfoForm.432422-2": "Tree table (adding, deleting, modifying, and querying)", "gen.genInfoForm.432422-3": "Main sub table (add, delete, modify, query)", "gen.genInfoForm.432422-4": "Generate package path", "gen.genInfoForm.432422-5": "Generate in which Java package, such as com.ruoyi.system", "gen.genInfoForm.432422-6": "Generate Module Name", "gen.genInfoForm.432422-7": "Can be understood as a subsystem name, such as system", "gen.genInfoForm.432422-8": "Generate Business Name", "gen.genInfoForm.432422-9": "Can be understood as the English name of the function, such as user", "gen.genInfoForm.432422-10": "Generate Function Name", "gen.genInfoForm.432422-11": "Used as a class description, such as user", "gen.genInfoForm.432422-12": "Superior menu", "gen.genInfoForm.432422-13": "Assign to a specified menu, such as System Management", "gen.genInfoForm.432422-14": "Please select the system menu", "gen.genInfoForm.432422-15": "Generate Code Method", "gen.genInfoForm.432422-16": "The default is to download a zip file, and you can also customize the generated path", "gen.genInfoForm.432422-17": "Zip compressed package", "gen.genInfoForm.432422-18": "Custom Path", "gen.genInfoForm.432422-19": "Custom Path", "gen.genInfoForm.432422-20": "Fill in the absolute path of the disk. If not filled in, it will be generated under the current web project", "gen.genInfoForm.432422-21": "Quick selection of the nearest path", "gen.genInfoForm.432422-22": "Restore default generation base path", "gen.genInfoForm.432422-23": "Other information", "gen.genInfoForm.432422-24": "Tree encoding field", "gen.genInfoForm.432422-25": "The name of the encoding field displayed in the tree, such as dept_id", "gen.genInfoForm.432422-26": "Tree parent code field", "gen.genInfoForm.432422-27": "The name of the parent code field displayed in the tree, for example: parentId", "gen.genInfoForm.432422-28": "Tree name field", "gen.genInfoForm.432422-29": "The display name field name of the tree node, for example: deptyname", "gen.genInfoForm.432422-30": "Related information", "gen.genInfoForm.432422-31": "The table name of the associated sub table", "gen.genInfoForm.432422-32": "The table name of the associated sub table, such as sys_user", "gen.genInfoForm.432422-33": "Foreign key names associated with sub tables", "gen.genInfoForm.432422-34": "The foreign key name associated with the sub table, such as user_id", "gen.genInfoForm.432422-35": "Please select a generation template", "gen.genInfoForm.432422-36": "Please enter the path to generate the package", "gen.genInfoForm.432422-37": "Please enter the name of the generation module", "gen.genInfoForm.432422-38": "Please enter the generated business name", "gen.genInfoForm.432422-39": "Please enter the name of the generation function", "gen.import.832346-0": "Import Table", "gen.import.832346-1": "Table Name", "gen.import.832346-2": "Please enter the table name", "gen.import.832346-3": "Table Description", "gen.import.832346-4": "Please enter a table description", "gen.import.832346-5": "Update time", "gen.import.832346-6": "Please select the table to import", "gen.index.467583-0": "generate", "gen.index.467583-1": "entity", "gen.index.467583-2": "preview", "gen.index.467583-3": "synchronization", "gen.index.467583-4": "Generate Code", "gen.index.467583-5": "Code Preview", "gen.index.467583-6": "Please select the data to be generated", "gen.index.467583-7": "Successfully generated to custom path:", "gen.index.467583-8": "Confirm to force synchronization“", "gen.index.467583-9": "Is it a table structure?", "gen.index.467583-10": "Sync successful", "gen.index.467583-11": "Copy successful", "gen.index.467583-12": "Are you sure to delete the data item with table number {0}?"}