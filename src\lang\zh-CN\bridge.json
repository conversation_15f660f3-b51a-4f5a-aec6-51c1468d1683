{"views.iot.bridge.index.525282-0": "连接器名称", "views.iot.bridge.index.525282-1": "请输入连接器名称", "views.iot.bridge.index.525282-2": "搜索", "views.iot.bridge.index.525282-3": "重置", "views.iot.bridge.index.525282-4": "新增", "views.iot.bridge.index.525282-5": "修改", "views.iot.bridge.index.525282-6": "删除", "views.iot.bridge.index.525282-7": "导出", "views.iot.bridge.index.525282-8": "是否生效", "views.iot.bridge.index.525282-9": "状态", "views.iot.bridge.index.525282-10": "未连接", "views.iot.bridge.index.525282-11": "连接中", "views.iot.bridge.index.525282-12": "桥接类型", "views.iot.bridge.index.525282-13": "Http推送", "views.iot.bridge.index.525282-14": "Mqtt桥接", "views.iot.bridge.index.525282-15": "数据库存储", "views.iot.bridge.index.525282-16": "桥接方向", "views.iot.bridge.index.525282-17": "输入", "views.iot.bridge.index.525282-18": "输出", "views.iot.bridge.index.525282-19": "备注", "views.iot.bridge.index.525282-20": "操作", "views.iot.bridge.index.525282-21": "连接测试", "views.iot.bridge.index.525282-22": "桥接名称", "views.iot.bridge.index.525282-23": "请输入桥接名称", "views.iot.bridge.index.525282-24": "请选择桥接方向", "views.iot.bridge.index.525282-25": "请求地址", "views.iot.bridge.index.525282-26": "请输入请求地址", "views.iot.bridge.index.525282-27": "请选择", "views.iot.bridge.index.525282-28": "请求方法", "views.iot.bridge.index.525282-29": "请求头", "views.iot.bridge.index.525282-30": "请输入参数名", "views.iot.bridge.index.525282-31": "请输入参数值", "views.iot.bridge.index.525282-32": "键", "views.iot.bridge.index.525282-33": "值", "views.iot.bridge.index.525282-34": "添加参数", "views.iot.bridge.index.525282-35": "请求参数", "views.iot.bridge.index.525282-36": "请求体", "views.iot.bridge.index.525282-37": "请输入内容", "views.iot.bridge.index.525282-38": "MQTT服务地址", "views.iot.bridge.index.525282-39": "输入例：mqtt.example.com:1883", "views.iot.bridge.index.525282-40": "客户端ID", "views.iot.bridge.index.525282-41": "请输入客户端ID", "views.iot.bridge.index.525282-42": "用户名", "views.iot.bridge.index.525282-43": "请输入用户名", "views.iot.bridge.index.525282-44": "密码", "views.iot.bridge.index.525282-45": "请输入密码", "views.iot.bridge.index.525282-46": "桥接路由", "views.iot.bridge.index.525282-47": "请输入桥接路由", "views.iot.bridge.index.525282-48": "数据库类型", "views.iot.bridge.index.525282-49": "请选择数据库类型", "views.iot.bridge.index.525282-50": "数据源", "views.iot.bridge.index.525282-51": "请选择数据源", "views.iot.bridge.index.525282-52": "连接地址", "views.iot.bridge.index.525282-53": "数据库名称", "views.iot.bridge.index.525282-54": "请输入数据库名称", "views.iot.bridge.index.525282-55": "输入相关SQL语句", "views.iot.bridge.index.525282-56": "连 通", "views.iot.bridge.index.525282-57": "确 定", "views.iot.bridge.index.525282-58": "取 消", "views.iot.bridge.index.525282-59": "关系型数据库", "views.iot.bridge.index.525282-60": "敬请期待", "views.iot.bridge.index.525282-61": "连接器名称不能为空", "views.iot.bridge.index.525282-62": "桥接类型(3=Http推送，4=Mqtt桥接，5=数据库存储)不能为空", "views.iot.bridge.index.525282-63": "桥接方向(1=输入，2=输出)不能为空", "views.iot.bridge.index.525282-64": "请求方法不能为空", "views.iot.bridge.index.525282-65": "桥接地址不能为空", "views.iot.bridge.index.525282-66": "创建时间不能为空", "views.iot.bridge.index.525282-67": "更新时间不能为空", "views.iot.bridge.index.525282-68": "MQTT服务地址不能为空", "views.iot.bridge.index.525282-69": "输入格式不正确", "views.iot.bridge.index.525282-70": "客户端ID不能为空", "views.iot.bridge.index.525282-71": "用户名不能为空", "views.iot.bridge.index.525282-72": "密码不能为空", "views.iot.bridge.index.525282-73": "路由不能为空", "views.iot.bridge.index.525282-74": "数据源不能为空", "views.iot.bridge.index.525282-75": "数据库类型不能为空", "views.iot.bridge.index.525282-76": "端口不能为空", "views.iot.bridge.index.525282-77": "数据库名称不能为空", "views.iot.bridge.index.525282-78": "请输入sql语句", "views.iot.bridge.index.525282-79": "连接地址不能为空", "views.iot.bridge.index.525282-80": "格式不正确", "views.iot.bridge.index.525282-81": "输入例：localhost:3306", "views.iot.bridge.index.525282-82": "桥接入口", "views.iot.bridge.index.525282-83": "Http桥接GET", "views.iot.bridge.index.525282-84": "Http桥接PUT", "views.iot.bridge.index.525282-85": "Http桥接POST", "views.iot.bridge.index.525282-86": "添加数据桥接", "views.iot.bridge.index.525282-87": "连接器已在连接中", "views.iot.bridge.index.525282-88": "连接器未连接，请检查相关配置", "views.iot.bridge.index.525282-89": "修改数据桥接", "views.iot.bridge.index.525282-90": "连接器已在连接中", "views.iot.bridge.index.525282-91": "连接器未连接，请检查相关配置", "views.iot.bridge.index.525282-92": "配置错误，请检查配置信息", "views.iot.bridge.index.525282-93": "修改成功", "views.iot.bridge.index.525282-94": "新增成功", "views.iot.bridge.index.525282-95": "删除成功", "views.iot.bridge.index.525282-96": "是否确认删除数据桥接编号为{0}的数据项？", "views.iot.bridge.index.525282-97": "测试结果："}