{"role.index.094567-0": "Role Name", "role.index.094567-1": "Please enter the role name", "role.index.094567-2": "Character status", "role.index.094567-3": "Add a new role", "role.index.094567-4": "Display subordinate institution data", "role.index.094567-5": "After selection, this level can view the data of subordinates", "role.index.094567-6": "Permission Characters", "role.index.094567-7": "Institution name", "role.index.094567-8": "Display order", "role.index.094567-9": "Edit Role", "role.index.094567-10": "Delete Role", "role.index.094567-11": "More configurations", "role.index.094567-12": "Assign Users", "role.index.094567-13": "Please enter the role name", "role.index.094567-14": "The permission characters defined in the controller, such as @ PreAuthorize (` @ ss. hasRole ('admin ') `)", "role.index.094567-15": "Please enter permission characters", "role.index.094567-16": "Role order", "role.index.094567-17": "Menu permissions", "role.index.094567-18": "Expand/Fold", "role.index.094567-19": "Select All/Select None", "role.index.094567-20": "Father son linkage", "role.index.094567-21": "Loading, please wait", "role.index.094567-22": "Scope of permissions", "role.index.094567-23": "Data permissions", "role.index.094567-24": "All data permissions", "role.index.094567-25": "Custom data permissions", "role.index.094567-26": "Data permissions for this department", "role.index.094567-27": "Data permissions for this department and below", "role.index.094567-28": "Only personal data permissions", "role.index.094567-29": "Belonging institution cannot be empty", "role.index.094567-30": "Role name cannot be empty", "role.index.094567-31": "The permission character cannot be empty", "role.index.094567-32": "The role order cannot be empty", "role.index.094567-33": "Add Role", "role.index.094567-34": "Modify Role", "role.index.094567-35": "Assign data permissions", "role.index.094567-36": "Are you sure to delete the data item with role number {0}?", "role.index.094567-37": "role ?", "role.auth-user.876234-0": "Please enter the user name", "role.auth-user.876234-1": "Add user", "role.auth-user.876234-2": "Batch cancellation of authorization", "role.auth-user.876234-3": "Cancel authorization", "role.auth-user.876234-4": "Confirm to cancel the user", "role.auth-user.876234-5": "Cancel authorization successful", "role.auth-user.876234-6": "Do you want to uncheck the user authorization data item?", "role.selectUser.093468-0": "Select Users", "role.selectUser.093468-1": "Please select the user to be assigned", "role.selectUser.093468-2": "User Password", "role.selectUser.093468-3": "Role", "role.selectUser.093468-4": "Please select a role"}