{"product.index.091251-0": "Product Name", "product.index.091251-1": "Please enter the product name", "product.index.091251-2": "Classification name", "product.index.091251-3": "Please enter the product category name", "product.index.091251-4": "Status", "product.index.091251-5": "Please select a status", "product.index.091251-6": "Search", "product.index.091251-7": "Reset", "product.index.091251-8": "Display superior institution products", "product.index.091251-9": "After selection, this level can view the superior's products", "product.index.091251-10": "Add", "product.index.091251-11": "private", "product.index.091251-12": "Unpublish", "product.index.091251-13": "Published", "product.index.091251-14": "Now release", "product.index.091251-15": "Unpublished", "product.index.091251-16": "Classification", "product.index.091251-17": "product type", "product.index.091251-18": "Networking methods", "product.index.091251-19": "Device Authorization", "product.index.091251-20": "Enabled", "product.index.091251-21": "not enabled", "product.index.091251-22": "View Details", "product.index.091251-23": "Delete", "product.index.091251-24": "view Devices", "product.index.091251-25": "There is currently no data available, please add the product", "product.index.091251-26": "This feature has not been implemented yet. Please refer to the tutorials and SDK examples for the project", "product.index.091251-27": "Equipment type", "product.index.091251-28": "Download", "product.index.091251-29": "Cancel", "product.index.091251-30": "Are you sure you want to cancel publishing?", "product.index.091251-31": "No operation permission", "product.index.091251-32": "After product release, corresponding devices can be created", "product.index.091251-33": "Important reminder: There are already {0} devices under the product. Unpublishing can modify the product information and model. After republishing, the corresponding device status will be reset!", "product.index.091251-34": "Tips", "product.index.091251-35": "Confirm", "product.index.091251-38": "Generate SDK", "product.index.091251-39": "Are you sure to delete the data item with product number {0}?", "product.index.091251-40": "Scada Design", "product.product-app.045891-0": "Refresh", "product.product-app.045891-1": "This feature is currently unavailable and will be released in a later version", "product.product-app.045891-2": "Name", "product.product-app.045891-3": "identifier ", "product.product-app.045891-4": "Object model category", "product.product-app.045891-5": "data type", "product.product-app.045891-6": "parts", "product.product-app.045891-7": "System defined components", "product.product-app.045891-8": "Advanced features", "product.product-app.045891-9": "Custom Page", "product.product-app.045891-10": "Please enter a custom page", "product.product-app.045891-11": "Interface display", "product.product-authorize.314975-0": "Equipment number", "product.product-authorize.314975-1": "Please enter the device number", "product.product-authorize.314975-2": "Authorization code", "product.product-authorize.314975-3": "Please enter the authorization code", "product.product-authorize.314975-4": "state", "product.product-authorize.314975-5": "Please select a status", "product.product-authorize.314975-6": "search", "product.product-authorize.314975-7": "Reset", "product.product-authorize.314975-8": "Generate authorization code", "product.product-authorize.314975-9": "Batch deletion", "product.product-authorize.314975-10": "export", "product.product-authorize.314975-11": "Tips: Double click to copy the authorization code.", "product.product-authorize.314975-12": "Authorization time", "product.product-authorize.314975-13": "Remarks", "product.product-authorize.314975-14": "Opation", "product.product-authorize.314975-15": "Delete", "product.product-authorize.314975-17": "Device Name", "product.product-authorize.314975-18": "Please enter the device name", "product.product-authorize.314975-19": "choice", "product.product-authorize.314975-20": "Device ID", "product.product-authorize.314975-21": "User Name", "product.product-authorize.314975-22": "Device status", "product.product-authorize.314975-23": "Please enter the content", "product.product-authorize.314975-24": "confirm", "product.product-authorize.314975-25": "cancel", "product.product-authorize.314975-26": "device details ", "product.product-authorize.314975-27": "Tip: Unable to find device, it may have been deleted", "product.product-authorize.314975-28": "not active", "product.product-authorize.314975-29": "Disabled", "product.product-authorize.314975-30": "on line", "product.product-authorize.314975-31": "off-line", "product.product-authorize.314975-32": "<PERSON><PERSON>", "product.product-authorize.314975-33": "Enable", "product.product-authorize.314975-35": "Positioning method", "product.product-authorize.314975-36": "Automatic positioning", "product.product-authorize.314975-37": "Equipment positioning", "product.product-authorize.314975-38": "Custom Location", "product.product-authorize.314975-39": "Product Name", "product.product-authorize.314975-40": "Tenant Name", "product.product-authorize.314975-41": "Firmware version", "product.product-authorize.314975-42": "Address", "product.product-authorize.314975-43": "Equipment longitude", "product.product-authorize.314975-44": "Device latitude", "product.product-authorize.314975-45": "Network access IP", "product.product-authorize.314975-46": "Equipment signal", "product.product-authorize.314975-47": "Create time", "product.product-authorize.314975-48": "Activation time", "product.product-authorize.314975-49": "Remarks", "product.product-authorize.314975-50": "View devices", "product.product-authorize.314975-51": "Close", "product.product-authorize.314975-52": "Enter the number of authorization codes", "product.product-authorize.314975-53": "Confirm", "product.product-authorize.314975-54": "Cancel", "product.product-authorize.314975-55": "Incorrect quantity content", "product.product-authorize.314975-56": "Successfully added authorization code", "product.product-authorize.314975-57": "Cancel addition", "product.product-authorize.314975-58": "Select device", "product.product-authorize.314975-59": "Device authorization successful", "product.product-authorize.314975-60": "Please select the device you want to authorize", "product.product-authorize.314975-61": "Remark successful", "product.product-authorize.314975-62": "Are you sure to delete the data item with product authorization code number {0}?", "product.product-authorize.314975-63": "Delete successful", "product.product-authorize.314975-64": "Successful", "product.product-authorize.314975-66": "Copy successful!", "product.product-authorize.314975-67": "Fail", "product.product-authorize.314975-68": "Copy failed!", "product.product-edit.473153-0": "Basic information", "product.product-edit.473153-1": "Product Name", "product.product-edit.473153-2": "Please enter the product name", "product.product-edit.473153-3": "Product classification", "product.product-edit.473153-4": "Please select a category", "product.product-edit.473153-5": "Communication protocol", "product.product-edit.473153-6": "After the product is established, it is not allowed to modify the communication protocol, as modification may cause dirty data issues in the device under the product", "product.product-edit.473153-7": "Please select the protocol", "product.product-edit.473153-8": "The current communication protocol is Modbus protocol. Please select a collection point template. The default added device is the gateway device", "product.product-edit.473153-9": "Collection point template", "product.product-edit.473153-10": "Delete", "product.product-edit.473153-11": "Select Template", "product.product-edit.473153-13": "Please select the device type", "product.product-edit.473153-14": "transport protocol", "product.product-edit.473153-15": "Please select a transmission protocol", "product.product-edit.473153-16": "Networking methods", "product.product-edit.473153-17": "Please select the networking method", "product.product-edit.473153-18": "Private products", "product.product-edit.473153-19": "After setting it as a private product, subordinates cannot view it", "product.product-edit.473153-20": "Enable authorization", "product.product-edit.473153-21": "Authentication method", "product.product-edit.473153-22": "Please choose the authentication method", "product.product-edit.473153-23": "Positioning method", "product.product-edit.473153-24": "Please select the device status", "product.product-edit.473153-25": "Product number", "product.product-edit.473153-26": "Automatic generation", "product.product-edit.473153-27": "Mqtt account", "product.product-edit.473153-28": "Automatically generated without filling in", "product.product-edit.473153-29": "Mqtt password", "product.product-edit.473153-30": "Automatically generate if not filled in", "product.product-edit.473153-31": "Product Key", "product.product-edit.473153-32": "Remarks", "product.product-edit.473153-33": "Please enter the content", "product.product-edit.473153-34": "Product images", "product.product-edit.473153-35": "Tip: After uploading, you need to submit and save", "product.product-edit.473153-36": "Update", "product.product-edit.473153-37": "Add", "product.product-edit.473153-38": "product model ", "product.product-edit.473153-39": "Firmware management", "product.product-edit.473153-40": "Device Authorization", "product.product-edit.473153-41": "SIP configuration", "product.product-edit.473153-42": "Product release", "product.product-edit.473153-43": "Unpublish", "product.product-edit.473153-44": "Return to List", "product.product-edit.473153-45": "Template Name", "product.product-edit.473153-46": "Search", "product.product-edit.473153-47": "Reset", "product.product-edit.473153-48": "Select collection point template", "product.product-edit.473153-49": "List of physical models", "product.product-edit.473153-50": "Number of Slaves:", "product.product-edit.473153-51": "Number of variables:", "product.product-edit.473153-52": "Collection method:", "product.product-edit.473153-53": "Model Name", "product.product-edit.473153-54": "register", "product.product-edit.473153-55": "value type", "product.product-edit.473153-56": "Cancel", "product.product-edit.473153-57": "Confirm", "product.product-edit.473153-58": "The product name cannot be empty", "product.product-edit.473153-59": "Product classification ID cannot be empty", "product.product-edit.473153-60": "The device protocol cannot be empty", "product.product-edit.473153-61": "The transmission protocol cannot be empty", "product.product-edit.473153-62": "update Successful", "product.product-edit.473153-63": "Please select a collection point template", "product.product-edit.473153-64": "Successfully added, can start defining object models or configurations", "product.product-edit.473153-65": "The physical model has been synchronized from the collection point template to the product", "product.product-edit.473153-66": "Are you sure you want to cancel publishing?", "product.product-edit.473153-67": "After product release, corresponding devices can be created", "product.product-edit.473153-68": "Important reminder: There are already {0} devices under the product. Unpublishing can modify the product information and model. After republishing, the corresponding device status will be reset!", "product.product-edit.473153-69": "Tips", "product.product-edit.473153-70": "confirm", "product.product-edit.473153-71": "cancel", "product.product-edit.473153-72": "Enable", "product.product-edit.473153-74": "Deactivate", "product.product-edit.473153-75": "Are you sure you want the {0} authorization code?", "product.product-edit.473153-77": "The authorization code has been changed", "product.product-edit.473153-78": "Select Template", "product.product-edit.473153-79": "Configuration application", "product.product-edit.473153-80": "Modbus configuration", "product.product-edit.473153-81": "Communication protocol", "product.product-edit.473153-82": "Please select the protocol", "product.product-firmware.420545-0": "Add", "product.product-firmware.420545-1": "Refresh", "product.product-firmware.420545-2": "Firmware Name", "product.product-firmware.420545-3": "Firmware version", "product.product-firmware.420545-4": "Status", "product.product-firmware.420545-5": "Latest", "product.product-firmware.420545-6": "default", "product.product-firmware.420545-7": "Create time", "product.product-firmware.420545-8": "Download address", "product.product-firmware.420545-9": "Firmware Description", "product.product-firmware.420545-10": "opation", "product.product-firmware.420545-11": "view", "product.product-firmware.420545-12": "Delete", "product.product-firmware.420545-13": "Please enter the firmware name", "product.product-firmware.420545-14": "Please enter the firmware version", "product.product-firmware.420545-15": "Latest firmware", "product.product-firmware.420545-16": "Tip: There can only be one latest firmware in the product", "product.product-firmware.420545-17": "Firmware upload", "product.product-firmware.420545-18": "Please enter firmware information", "product.product-firmware.420545-19": "Add", "product.product-firmware.420545-20": "Update", "product.product-firmware.420545-21": "Cancel", "product.product-firmware.420545-24": "Firmware name cannot be empty", "product.product-firmware.420545-25": "The firmware version cannot be empty", "product.product-firmware.420545-26": "The file path cannot be empty", "product.product-firmware.420545-27": "Add product firmware", "product.product-firmware.420545-28": "Modifying product firmware", "product.product-firmware.420545-29": "update Successful", "product.product-firmware.420545-30": "add Successful", "product.product-firmware.420545-31": "Are you sure to delete the data item with product firmware number {0}?", "product.product-firmware.420545-32": "Delete Successful", "product.product-select-template.318012-0": "Name", "product.product-select-template.318012-1": "Please enter the name of the object model", "product.product-select-template.318012-2": "category", "product.product-select-template.318012-3": "Please select a model category", "product.product-select-template.318012-4": "Search", "product.product-select-template.318012-5": "reset", "product.product-select-template.318012-6": "identifier ", "product.product-select-template.318012-7": "Object model category", "product.product-select-template.318012-8": "Chart display", "product.product-select-template.318012-9": "Real time monitoring", "product.product-select-template.318012-10": "read-only", "product.product-select-template.318012-11": "Historical storage", "product.product-select-template.318012-12": "data type", "product.product-things-model.142341-0": "Add", "product.product-things-model.142341-1": "Import a universal object model", "product.product-things-model.142341-2": "Refresh", "product.product-things-model.142341-3": "View object models", "product.product-things-model.142341-4": "Note: Identifiers cannot be duplicated", "product.product-things-model.142341-5": "Please select a device slave:", "product.product-things-model.142341-6": "Please select a device slave", "product.product-things-model.142341-7": "Gateway object model", "product.product-things-model.142341-8": "Name", "product.product-things-model.142341-9": "Identifier ", "product.product-things-model.142341-10": "Register Address(10 base)", "product.product-things-model.142341-12": "Chart display", "product.product-things-model.142341-13": "Real time monitoring", "product.product-things-model.142341-14": "read-only", "product.product-things-model.142341-15": "Historical storage", "product.product-things-model.142341-16": "Object model category", "product.product-things-model.142341-17": "data type", "product.product-things-model.142341-18": "Data definition", "product.product-things-model.142341-19": "Calculation formula", "product.product-things-model.142341-20": "sort", "product.product-things-model.142341-21": "opation", "product.product-things-model.142341-22": "view", "product.product-things-model.142341-23": "Delete", "product.product-things-model.142341-24": "Model Name", "product.product-things-model.142341-25": "Please enter the name of the object model, for example: temperature", "product.product-things-model.142341-26": "Model identification", "product.product-things-model.142341-27": "Please enter an identifier, for example: temperature", "product.product-things-model.142341-28": "Model sorting", "product.product-things-model.142341-29": "Please enter sorting", "product.product-things-model.142341-30": "Model category", "product.product-things-model.142341-31": "attribute", "product.product-things-model.142341-32": "function", "product.product-things-model.142341-33": "event", "product.product-things-model.142341-34": "Model characteristics", "product.product-things-model.142341-35": "Read only data", "product.product-things-model.142341-36": "Share permissions", "product.product-things-model.142341-37": "Please select a data type", "product.product-things-model.142341-38": "integer", "product.product-things-model.142341-39": "decimal", "product.product-things-model.142341-40": "Boolean", "product.product-things-model.142341-41": "enumeration", "product.product-things-model.142341-42": "character string", "product.product-things-model.142341-43": "array", "product.product-things-model.142341-44": "object", "product.product-things-model.142341-45": "Value range", "product.product-things-model.142341-46": "minimum value", "product.product-things-model.142341-47": "reach", "product.product-things-model.142341-48": "Maximum value", "product.product-things-model.142341-49": "unit", "product.product-things-model.142341-50": "Please enter the unit, for example: ℃", "product.product-things-model.142341-51": "step", "product.product-things-model.142341-52": "Please enter the step size, for example: 1", "product.product-things-model.142341-53": "The upstream data of the device is displayed after being calculated using a formula.", "product.product-things-model.142341-54": "The% s in the formula is a placeholder and a fixed field.", "product.product-things-model.142341-55": "For example:", "product.product-things-model.142341-56": "Add:% s+10", "product.product-things-model.142341-57": "Subtract:% s-10", "product.product-things-model.142341-58": "Multiply:% s * 10", "product.product-things-model.142341-59": "Except for:% s/10", "product.product-things-model.142341-60": "except(Retain decimals)for:% s/10", "product.product-things-model.142341-61": "Retain decimals", "product.product-things-model.142341-62": "Control Formula", "product.product-things-model.142341-63": "Boolean value", "product.product-things-model.142341-64": "For example: Close", "product.product-things-model.142341-65": "(0 value corresponds to text)", "product.product-things-model.142341-66": "For example: Open", "product.product-things-model.142341-67": "(1 value corresponds to text)", "product.product-things-model.142341-68": "Display method", "product.product-things-model.142341-69": "Please choose the display method", "product.product-things-model.142341-70": "Dropdown box", "product.product-things-model.142341-71": "button", "product.product-things-model.142341-72": "Enumeration item", "product.product-things-model.142341-73": "Parameter value, for example: 0", "product.product-things-model.142341-74": "Parameter description, such as medium speed gear", "product.product-things-model.142341-75": "Add enumeration item", "product.product-things-model.142341-76": "Maximum length", "product.product-things-model.142341-77": "For example: 1024", "product.product-things-model.142341-78": "(Maximum length of string)", "product.product-things-model.142341-79": "Number of elements", "product.product-things-model.142341-80": "For example: 5", "product.product-things-model.142341-81": "Array type", "product.product-things-model.142341-82": "Object parameters", "product.product-things-model.142341-83": "Please select a device", "product.product-things-model.142341-84": "edit", "product.product-things-model.142341-85": "Add parameters", "product.product-things-model.142341-86": "Delete", "product.product-things-model.142341-87": "Object parameters", "product.product-things-model.142341-88": "Please select a device", "product.product-things-model.142341-89": "update", "product.product-things-model.142341-90": "add", "product.product-things-model.142341-91": "Cancel", "product.product-things-model.142341-92": "copy", "product.product-things-model.142341-94": "The object model name cannot be empty", "product.product-things-model.142341-95": "Identifier, unique under the product cannot be empty", "product.product-things-model.142341-96": "Model sorting cannot be empty", "product.product-things-model.142341-97": "Model category cannot be empty", "product.product-things-model.142341-98": "The data type cannot be empty", "product.product-things-model.142341-99": "Additive model", "product.product-things-model.142341-100": "Modify the object model", "product.product-things-model.142341-101": "Physical model", "product.product-things-model.142341-102": "The parameters of an object cannot be empty", "product.product-things-model.142341-103": "The input for object type model identification cannot contain an underline. Please fill in the model identification again!", "product.product-things-model.142341-104": "Parameter identification", "product.product-things-model.142341-105": "repeat", "product.product-things-model.142341-106": "Please reselect the data type!", "product.product-things-model.142341-107": "update Successful", "product.product-things-model.142341-108": "New successfully added", "product.product-things-model.142341-109": "Are you sure to delete the data item with object model number {0}?", "product.product-things-model.142341-111": "Delete successful", "product.product-things-model.142341-112": "Delete collection point, please modify the collection point template", "product.product-things-model.142341-113": "Close", "product.product-things-model.142341-114": "Open", "product.product-things-model.142341-115": "low", "product.product-things-model.142341-116": "high", "product.product-things-model.142341-117": "Maximum value:", "product.product-things-model.142341-118": "Minimum value:", "product.product-things-model.142341-119": "Step size:", "product.product-things-model.142341-120": "Unit:", "product.product-things-model.142341-121": "Maximum length:", "product.product-things-model.142341-122": "Array type:", "product.product-things-model.142341-123": "Number of elements:", "product.product-things-model.142341-124": "Register Address", "product.product-things-model.142341-125": "Delete", "product.product-things-model.142341-126": "Export", "product.product-things-model.142341-127": "APP display", "product.product-things-model.142341-128": "Please choose whether to display on the app", "product.product-things-model.142341-129": "Object model type", "product.product-things-model.142341-130": "Please select the type of object model", "product.product-things-model.142341-131": "Is it historical storage", "product.product-things-model.142341-132": "Please choose whether to store history or not", "product.product-things-model.142341-133": "search", "product.product-things-model.142341-134": "Reset", "product.product-things-model.142341-135": "grouping", "product.product-modbus.562372-0": "Configuration of gateway sub devices", "product.product-modbus.562372-1": "Configure default sub device addresses and polling methods for gateway sub devices", "product.product-modbus.562372-2": "Edit device configuration", "product.product-modbus.562372-3": "preservation", "product.product-modbus.562372-4": "cancel", "product.product-modbus.562372-5": "Device status:", "product.product-modbus.562372-6": "Configure whether the device status comes from the gateway or whether the variable communication under the device is normal;", "product.product-modbus.562372-7": "If a gateway is selected, the offline status of the device is taken from the offline status of the gateway. If device data is selected, the device is considered offline if there is no variable communication within the selected duration.", "product.product-modbus.562372-8": "When selecting device data, it is recommended that the duration be greater than the minimum collection frequency within the template, otherwise the device will frequently go offline passively.", "product.product-modbus.562372-9": "Select duration:", "product.product-modbus.562372-10": "To use the device data judgment method, it is necessary to confirm that there is data reported during the rotation time", "product.product-modbus.562372-11": "Please select the default duration", "product.product-modbus.562372-12": "How long is the device offline if there is no data", "product.product-modbus.562372-13": "Collection method:", "product.product-modbus.562372-14": "Default address:", "product.product-modbus.562372-15": "Slave address", "product.product-modbus.562372-16": "The default address of the sub device. If the sub device address is set in the sub device of the device details, the default value will be overwritten", "product.product-modbus.562372-17": "IO register", "product.product-modbus.562372-18": "IO registers include coils and discrete registers, which can be bound to switch properties", "product.product-modbus.562372-19": "Edit Register", "product.product-modbus.562372-20": "Batch import", "product.product-modbus.562372-21": "export", "product.product-modbus.562372-22": "preservation", "product.product-modbus.562372-23": "cancel", "product.product-modbus.562372-24": "Number", "product.product-modbus.562372-25": "attribute", "product.product-modbus.562372-26": "Associated attributes", "product.product-modbus.562372-27": "Physical model", "product.product-modbus.562372-28": "identifier ", "product.product-modbus.562372-29": "Slave address", "product.product-modbus.562372-30": "Register Address", "product.product-modbus.562372-31": "Is it read-only", "product.product-modbus.562372-32": "read-only", "product.product-modbus.562372-33": "Reading and writing", "product.product-modbus.562372-34": "operation", "product.product-modbus.562372-35": "delete", "product.product-modbus.562372-36": "Add an IO register", "product.product-modbus.562372-37": "Add multiple IO registers", "product.product-modbus.562372-38": "Data register", "product.product-modbus.562372-39": "Data registers include input registers and hold registers, which can be bound to numerical attributes", "product.product-modbus.562372-40": "Batch import", "product.product-modbus.562372-41": "export", "product.product-modbus.562372-42": "data type", "product.product-modbus.562372-43": "Number of reads", "product.product-modbus.562372-44": "Add a single data register", "product.product-modbus.562372-45": "Add multiple data registers", "product.product-modbus.562372-46": "The associated attribute cannot be empty", "product.product-modbus.562372-47": "Register address cannot be empty", "product.product-modbus.562372-48": "Read only (0- No, 1- Yes) cannot be empty", "product.product-modbus.562372-49": "Modbus data type cannot be empty", "product.product-modbus.562372-50": "The number of reads cannot be empty", "product.product-modbus.562372-51": "Register type 1-IO register 2-data register cannot be empty", "product.product-modbus.562372-52": "Save successful", "product.product-modbus.562372-53": "Modified successfully", "product.product-modbus.562372-311": "Positional offset", "product.product-scada.638785-0": "No configuration currently available", "product.thimgs-mopdel-list.738493-0": "Select object model", "product.thimgs-mopdel-list.738493-1": "name", "product.thimgs-mopdel-list.738493-2": "Please enter the name of the object model", "product.thimgs-mopdel-list.738493-3": "search", "product.thimgs-mopdel-list.738493-4": "Reset", "product.thimgs-mopdel-list.738493-5": "Physical model", "product.thimgs-mopdel-list.738493-6": "identifier ", "product.thimgs-mopdel-list.738493-7": "determine", "product.thimgs-mopdel-list.738493-8": "close", "product.components.batchImportModbus.745343-0": "IO register template", "product.components.batchImportModbus.745343-1": "Data Register Template", "product.category.142342-0": "Product classification name", "product.category.142342-1": "Display order", "product.category.142342-2": "Please enter the display order", "product.category.142342-3": "Please enter the content", "product.category.142342-4": "The product category name cannot be empty", "product.category.142342-5": "Is the system universal and cannot be empty", "product.category.142342-6": "Add product category", "product.category.142342-7": "Modify product classification", "product.category.142342-8": "Are you sure to delete the data item with product category number {0}?", "product.product-scada.034908-0": "Create configuration"}