{"views.iot.bridge.index.525282-0": "connector name", "views.iot.bridge.index.525282-1": "Please enter the connector name.", "views.iot.bridge.index.525282-2": "Search", "views.iot.bridge.index.525282-3": "Reset", "views.iot.bridge.index.525282-4": "Add", "views.iot.bridge.index.525282-5": "Update", "views.iot.bridge.index.525282-6": "Delete", "views.iot.bridge.index.525282-7": "Export", "views.iot.bridge.index.525282-8": "Is it effective", "views.iot.bridge.index.525282-9": "Status", "views.iot.bridge.index.525282-10": "Not Connected", "views.iot.bridge.index.525282-11": "Connecting", "views.iot.bridge.index.525282-12": "Bridging Type", "views.iot.bridge.index.525282-13": "HTTP Push", "views.iot.bridge.index.525282-14": "MQTT Bridge", "views.iot.bridge.index.525282-15": "Database Storage", "views.iot.bridge.index.525282-16": "Bridging Direction", "views.iot.bridge.index.525282-17": "Input", "views.iot.bridge.index.525282-18": "Output", "views.iot.bridge.index.525282-19": "Notes", "views.iot.bridge.index.525282-20": "Action", "views.iot.bridge.index.525282-21": "Connection Test", "views.iot.bridge.index.525282-22": "Bridging Name", "views.iot.bridge.index.525282-23": "Please enter the bridging name", "views.iot.bridge.index.525282-24": "Please select the bridging direction", "views.iot.bridge.index.525282-25": "Request URL", "views.iot.bridge.index.525282-26": "Please enter the request URL", "views.iot.bridge.index.525282-27": "Please select", "views.iot.bridge.index.525282-28": "Request Method", "views.iot.bridge.index.525282-29": "Request Header", "views.iot.bridge.index.525282-30": "Please enter the parameter name", "views.iot.bridge.index.525282-31": "Please enter the parameter value", "views.iot.bridge.index.525282-32": "Key", "views.iot.bridge.index.525282-33": "Value", "views.iot.bridge.index.525282-34": "Add Parameter", "views.iot.bridge.index.525282-35": "Request Parameters", "views.iot.bridge.index.525282-36": "Request Body", "views.iot.bridge.index.525282-37": "Please enter the content", "views.iot.bridge.index.525282-38": "MQTT Service Address", "views.iot.bridge.index.525282-39": "Input example: mqtt.example.com:1883", "views.iot.bridge.index.525282-40": "Client ID", "views.iot.bridge.index.525282-41": "Please enter the Client ID", "views.iot.bridge.index.525282-42": "Username", "views.iot.bridge.index.525282-43": "Please enter the Username", "views.iot.bridge.index.525282-44": "Password", "views.iot.bridge.index.525282-45": "Please enter the Password", "views.iot.bridge.index.525282-46": "Bridging Route", "views.iot.bridge.index.525282-47": "Please enter the Bridging Route", "views.iot.bridge.index.525282-48": "Database Type", "views.iot.bridge.index.525282-49": "Please select a database type", "views.iot.bridge.index.525282-50": "Data Source", "views.iot.bridge.index.525282-51": "Please select a data source", "views.iot.bridge.index.525282-52": "Connection Address", "views.iot.bridge.index.525282-53": "Database Name", "views.iot.bridge.index.525282-54": "Please enter the database name", "views.iot.bridge.index.525282-55": "Enter relevant SQL statements", "views.iot.bridge.index.525282-56": "Connect", "views.iot.bridge.index.525282-57": "Confirm", "views.iot.bridge.index.525282-58": "Cancel", "views.iot.bridge.index.525282-59": "Relational Database", "views.iot.bridge.index.525282-60": "Stay tuned", "views.iot.bridge.index.525282-61": "Connector name cannot be empty", "views.iot.bridge.index.525282-62": "Bridging type (3=HTTP Push, 4=MQTT Bridge, 5=Database Storage) cannot be empty", "views.iot.bridge.index.525282-63": "Bridging direction (1=Input, 2=Output) cannot be empty", "views.iot.bridge.index.525282-64": "Request method cannot be empty", "views.iot.bridge.index.525282-65": "Bridging address cannot be empty", "views.iot.bridge.index.525282-66": "Creation time cannot be empty", "views.iot.bridge.index.525282-67": "Update time cannot be empty", "views.iot.bridge.index.525282-68": "MQTT service address cannot be empty", "views.iot.bridge.index.525282-69": "Input format is incorrect", "views.iot.bridge.index.525282-70": "Client ID cannot be empty", "views.iot.bridge.index.525282-71": "Username cannot be empty", "views.iot.bridge.index.525282-72": "Password cannot be empty", "views.iot.bridge.index.525282-73": "Route cannot be empty", "views.iot.bridge.index.525282-74": "Data source cannot be empty", "views.iot.bridge.index.525282-75": "Database type cannot be empty", "views.iot.bridge.index.525282-76": "Port cannot be empty", "views.iot.bridge.index.525282-77": "Database name cannot be empty", "views.iot.bridge.index.525282-78": "Please enter the SQL statement", "views.iot.bridge.index.525282-79": "Connection address cannot be empty", "views.iot.bridge.index.525282-80": "Format is incorrect", "views.iot.bridge.index.525282-81": "Input example: localhost:3306", "views.iot.bridge.index.525282-82": "Bridge Entry", "views.iot.bridge.index.525282-83": "Http Bridge GET", "views.iot.bridge.index.525282-84": "Http Bridge PUT", "views.iot.bridge.index.525282-85": "Http Bridge POST", "views.iot.bridge.index.525282-86": "Add data bridge", "views.iot.bridge.index.525282-87": "Connector is in the process of connecting", "views.iot.bridge.index.525282-88": "Connector is not connected, please check the relevant configuration", "views.iot.bridge.index.525282-89": "Update data bridge", "views.iot.bridge.index.525282-90": "Connector is in the process of connecting", "views.iot.bridge.index.525282-91": "Connector is not connected, please check the relevant configuration", "views.iot.bridge.index.525282-92": "Configuration error, please check the configuration information", "views.iot.bridge.index.525282-93": "Update successful", "views.iot.bridge.index.525282-94": "Addition successful", "views.iot.bridge.index.525282-95": "Deletion successful", "views.iot.bridge.index.525282-96": "Are you sure you want to delete the data item with the data bridge number {0}?", "views.iot.bridge.index.525282-97": "Test results:"}