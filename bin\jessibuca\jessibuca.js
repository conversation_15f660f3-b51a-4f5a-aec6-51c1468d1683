!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).jessibuca=t()}(this,(function(){"use strict";const e=0,t=1,i="player",o="playbackTF",s="flv",r="m7s",a={url:"",playbackConfig:{},playType:i,playbackForwardMaxRateDecodeIFrame:4},n={playType:i,videoBuffer:1e3,isResize:!0,isFullResize:!1,isFlv:!1,debug:!1,hotKey:!1,loadingTimeout:10,heartTimeout:5,timeout:10,loadingTimeoutReplay:!1,heartTimeoutReplay:!1,loadingTimeoutReplayTimes:3,heartTimeoutReplayTimes:3,supportDblclickFullscreen:!1,showBandwidth:!1,keepScreenOn:!1,isNotMute:!1,hasAudio:!0,hasVideo:!0,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1},controlAutoHide:!1,hasControl:!1,loadingText:"",background:"",decoder:"decoder.js",url:"",rotate:0,playbackConfig:{},forceNoOffscreen:!0,hiddenAutoPause:!1,protocol:t,demuxType:s,useWCS:!1,wcsUseVideoRender:!1,useMSE:!1,useOffscreen:!1,autoWasm:!0,wasmDecodeErrorReplay:!0,openWebglAlignment:!1,playbackDelayTime:1e3,playbackFps:20,playbackForwardMaxRateDecodeIFrame:4,playbackCurrentTimeMove:!0},A="init",c="initVideo",l="render",d="playAudio",u="initAudio",h="audioCode",p="videoCode",m="wasmError",f="Invalid NAL unit size",g=1,b=2,y=8,v=9,w="init",k="decode",S="audioDecode",B="videoDecode",E="close",C="updateConfig",R={fullscreen:"fullscreen$2",webFullscreen:"webFullscreen",decoderWorkerInit:"decoderWorkerInit",play:"play",playing:"playing",pause:"pause",mute:"mute",load:"load",loading:"loading",videoInfo:"videoInfo",timeUpdate:"timeUpdate",audioInfo:"audioInfo",log:"log",error:"error",kBps:"kBps",timeout:"timeout",delayTimeout:"delayTimeout",loadingTimeout:"loadingTimeout",stats:"stats",performance:"performance",record:"record",recording:"recording",recordingTimestamp:"recordingTimestamp",recordStart:"recordStart",recordEnd:"recordEnd",recordCreateError:"recordCreateError",buffer:"buffer",videoFrame:"videoFrame",start:"start",metadata:"metadata",resize:"resize",streamEnd:"streamEnd",streamSuccess:"streamSuccess",streamMessage:"streamMessage",streamError:"streamError",volumechange:"volumechange",destroy:"destroy",mseSourceOpen:"mseSourceOpen",mseSourceClose:"mseSourceClose",mseSourceBufferError:"mseSourceBufferError",mseSourceBufferBusy:"mseSourceBufferBusy",videoWaiting:"videoWaiting",videoTimeUpdate:"videoTimeUpdate",videoSyncAudio:"videoSyncAudio",playToRenderTimes:"playToRenderTimes",playbackTime:"playbackTime",playbackTimestamp:"playbackTimestamp",playbackPrecision:"playbackPrecision",playbackStats:"playbackStats",playbackSeek:"playbackSeek"},T={load:R.load,timeUpdate:R.timeUpdate,videoInfo:R.videoInfo,audioInfo:R.audioInfo,error:R.error,kBps:R.kBps,log:R.log,start:R.start,timeout:R.timeout,loadingTimeout:R.loadingTimeout,delayTimeout:R.delayTimeout,fullscreen:"fullscreen",play:R.play,pause:R.pause,mute:R.mute,stats:R.stats,performance:R.performance,recordingTimestamp:R.recordingTimestamp,recordStart:R.recordStart,recordEnd:R.recordEnd,playToRenderTimes:R.playToRenderTimes,playbackSeek:R.playbackSeek,playbackStats:R.playbackStats,playbackTimestamp:R.playbackTimestamp},I={playError:"playIsNotPauseOrUrlIsNull",fetchError:"fetchError",websocketError:"websocketError",webcodecsH265NotSupport:"webcodecsH265NotSupport",mediaSourceH265NotSupport:"mediaSourceH265NotSupport",wasmDecodeError:"wasmDecodeError"},j="notConnect",x="open",D="close",L="error",M={download:"download",base64:"base64",blob:"blob"},F={7:"H264(AVC)",12:"H265(HEVC)"},O=7,U=12,V={10:"AAC",7:"ALAW",8:"MULAW"},P={vps:32,sps:33,pps:34},Q=0,W=1,N=2,J={mp4:"mp4",webm:"webm"},z="webcodecs",G="webgl",H="offscreen",q="key",Y="delta",X={avc:'video/mp4; codecs="avc1.64002A"',hev:'video/mp4; codecs="hev1.1.6.L123.b0"'},Z="ended",K="open",_="closed",$=1e3,ee=2e3,te=27,ie=38,oe=40,se="oneHour",re="halfHour",ae="tenMin",ne="fiveMin",Ae={oneHour:"one-hour",halfHour:"half-hour",tenMin:"ten-min",fiveMin:"five-min"},ce=["oneHour","halfHour","tenMin","fiveMin"];class le{constructor(e){this.log=function(t){if(e._opt.debug){for(var i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];console.log(`Jessibuca: [${t}]`,...o)}},this.warn=function(t){if(e._opt.debug){for(var i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];console.warn(`Jessibuca: [${t}]`,...o)}},this.error=function(t){if(e._opt.debug){for(var i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];console.error(`Jessibuca: [${t}]`,...o)}}}}class de{constructor(e){this.destroys=[],this.proxy=this.proxy.bind(this),this.master=e}proxy(e,t,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(!e)return;if(Array.isArray(t))return t.map((t=>this.proxy(e,t,i,o)));e.addEventListener(t,i,o);const s=()=>e.removeEventListener(t,i,o);return this.destroys.push(s),s}destroy(){this.master.debug&&this.master.debug.log("Events","destroy"),this.destroys.forEach((e=>e()))}}var ue="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function he(e,t){return e(t={exports:{}},t.exports),t.exports}var pe=he((function(e){!function(){var t="undefined"!=typeof window&&void 0!==window.document?window.document:{},i=e.exports,o=function(){for(var e,i=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],o=0,s=i.length,r={};o<s;o++)if((e=i[o])&&e[1]in t){for(o=0;o<e.length;o++)r[i[0][o]]=e[o];return r}return!1}(),s={change:o.fullscreenchange,error:o.fullscreenerror},r={request:function(e,i){return new Promise(function(s,r){var a=function(){this.off("change",a),s()}.bind(this);this.on("change",a);var n=(e=e||t.documentElement)[o.requestFullscreen](i);n instanceof Promise&&n.then(a).catch(r)}.bind(this))},exit:function(){return new Promise(function(e,i){if(this.isFullscreen){var s=function(){this.off("change",s),e()}.bind(this);this.on("change",s);var r=t[o.exitFullscreen]();r instanceof Promise&&r.then(s).catch(i)}else e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,i){var o=s[e];o&&t.addEventListener(o,i,!1)},off:function(e,i){var o=s[e];o&&t.removeEventListener(o,i,!1)},raw:o};o?(Object.defineProperties(r,{isFullscreen:{get:function(){return Boolean(t[o.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[o.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(t[o.fullscreenEnabled])}}}),i?e.exports=r:window.screenfull=r):i?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}()}));function me(){}function fe(){const e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").split(","),t=atob(e[1]),i=e[0].replace("data:","").replace(";base64","");let o=t.length,s=new Uint8Array(o);for(;o--;)s[o]=t.charCodeAt(o);return new File([s],"file",{type:i})}function ge(e,t){const i=document.createElement("a");i.download=t,i.href=URL.createObjectURL(e),i.click(),setTimeout((()=>{URL.revokeObjectURL(e)}),Ee()?1e3:0)}function be(){return(new Date).getTime()}function ye(e,t,i){return Math.max(Math.min(e,Math.max(t,i)),Math.min(t,i))}function ve(e,t,i){if(e)return"object"==typeof t&&Object.keys(t).forEach((i=>{ve(e,i,t[i])})),e.style[t]=i,e}function we(e,t){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!e)return 0;const o=getComputedStyle(e,null).getPropertyValue(t);return i?parseFloat(o):o}function ke(){return performance&&"function"==typeof performance.now?performance.now():Date.now()}function Se(e){let t=0,i=ke();return o=>{t+=o;const s=ke(),r=s-i;r>=1e3&&(e(t/r*1e3),i=s,t=0)}}function Be(){return/iphone|ipod|android.*mobile|windows.*phone|blackberry.*mobile/i.test(window.navigator.userAgent.toLowerCase())}function Ee(){const e=window.navigator.userAgent.toLowerCase();return e&&/iphone|ipad|ipod|ios/.test(e)}function Ce(e,t){if(0===arguments.length)return null;var i,o=t||"{y}-{m}-{d} {h}:{i}:{s}";"object"==typeof e?i=e:(10===(""+e).length&&(e=1e3*parseInt(e)),e=+e,i=new Date(e));var s={y:i.getFullYear(),m:i.getMonth()+1,d:i.getDate(),h:i.getHours(),i:i.getMinutes(),s:i.getSeconds(),a:i.getDay()};return o.replace(/{(y|m|d|h|i|s|a)+}/g,((e,t)=>{var i=s[t];return"a"===t?["一","二","三","四","五","六","日"][i-1]:(e.length>0&&i<10&&(i="0"+i),i||0)}))}function Re(e){if(null==e||""===e)return"0 KB/S";let t=parseFloat(e);return t=t.toFixed(2),t+"KB/S"}function Te(e){return null==e}function Ie(e){return!Te(e)}function je(e,t){let i="";if(e>-1){const o=Math.floor(e/60)%60;let s=e%60;s=Math.round(s),i=o<10?"0"+o+":":o+":",s<10&&(i+="0"),i+=s,Te(t)||(t<10&&(t="0"+t),i+=":"+t)}return i}function xe(e){let t="";if(e>-1){const i=Math.floor(e/60/60)%60;let o=Math.floor(e/60)%60,s=e%60;o=Math.round(o),t=i<10?"0"+i+":":i+":",o<10&&(t+="0"),t+=o+":",s<10&&(t+="0"),t+=s}return t}function De(e,t){const i=Math.floor(t/60)%60,o=Math.floor(t%60);return new Date(e).setHours(i,o,0,0)}function Le(e,t){const i=Math.floor(t/60/60)%60,o=Math.floor(t/60)%60,s=t%60;return new Date(e).setHours(i,o,s,0)}function Me(e){return(""+e).length}function Fe(e){const t=e||window.event;return t.target||t.srcElement}pe.isEnabled,(()=>{try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(e instanceof WebAssembly.Module)return new WebAssembly.Instance(e)instanceof WebAssembly.Instance}}catch(e){}})();class Oe{on(e,t,i){const o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:i}),this}once(e,t,i){const o=this;function s(){o.off(e,s);for(var r=arguments.length,a=new Array(r),n=0;n<r;n++)a[n]=arguments[n];t.apply(i,a)}return s._=t,this.on(e,s,i)}emit(e){const t=((this.e||(this.e={}))[e]||[]).slice();for(var i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];for(let e=0;e<t.length;e+=1)t[e].fn.apply(t[e].ctx,o);return this}off(e,t){const i=this.e||(this.e={});if(!e)return Object.keys(i).forEach((e=>{delete i[e]})),void delete this.e;const o=i[e],s=[];if(o&&t)for(let e=0,i=o.length;e<i;e+=1)o[e].fn!==t&&o[e].fn._!==t&&s.push(o[e]);return s.length?i[e]=s:delete i[e],this}}class Ue extends Oe{constructor(){super(),this.init=!1}destroy(){}updateVideoInfo(e){e.encTypeCode&&(this.videoInfo.encType=VIDEO_ENC_TYPE[e.encTypeCode]),e.width&&(this.videoInfo.width=e.width),e.height&&(this.videoInfo.height=e.height),this.videoInfo.encType&&this.videoInfo.height&&this.videoInfo.width&&!this.init&&(this.player.emit(EVENTS.videoInfo,this.videoInfo),this.init=!0)}}class Ve extends Ue{constructor(e){super(),this.player=e;const t=document.createElement("canvas");t.style.position="absolute",t.style.top=0,t.style.left=0,this.$videoElement=t,e.$container.appendChild(this.$videoElement),this.context2D=null,this.contextGl=null,this.contextGlRender=null,this.contextGlDestroy=null,this.bitmaprenderer=null,this.renderType=null,this.videoInfo={width:"",height:"",encType:""},this.controlHeight=0,this._initCanvasRender()}destroy(){this.contextGl&&(this.contextGl=null),this.context2D&&(this.context2D=null),this.contextGlRender&&(this.contextGlDestroy&&this.contextGlDestroy(),this.contextGlDestroy=null,this.contextGlRender=null),this.bitmaprenderer&&(this.bitmaprenderer=null),this.renderType=null,this.videoInfo={width:"",height:"",encType:"",encTypeCode:""},this.player.$container.removeChild(this.$videoElement),this.init=!1,this.off()}_initContextGl(){this.contextGl=function(e){let t=null;const i=["webgl","experimental-webgl","moz-webgl","webkit-3d"];let o=0;for(;!t&&o<i.length;){const s=i[o];try{let i={preserveDrawingBuffer:!0};t=e.getContext(s,i)}catch(e){t=null}t&&"function"==typeof t.getParameter||(t=null),++o}return t}(this.$videoElement);const e=((e,t)=>{var i=["attribute vec4 vertexPos;","attribute vec4 texturePos;","varying vec2 textureCoord;","void main()","{","gl_Position = vertexPos;","textureCoord = texturePos.xy;","}"].join("\n"),o=["precision highp float;","varying highp vec2 textureCoord;","uniform sampler2D ySampler;","uniform sampler2D uSampler;","uniform sampler2D vSampler;","const mat4 YUV2RGB = mat4","(","1.1643828125, 0, 1.59602734375, -.87078515625,","1.1643828125, -.39176171875, -.81296875, .52959375,","1.1643828125, 2.017234375, 0, -1.081390625,","0, 0, 0, 1",");","void main(void) {","highp float y = texture2D(ySampler,  textureCoord).r;","highp float u = texture2D(uSampler,  textureCoord).r;","highp float v = texture2D(vSampler,  textureCoord).r;","gl_FragColor = vec4(y, u, v, 1) * YUV2RGB;","}"].join("\n");t&&e.pixelStorei(e.UNPACK_ALIGNMENT,1);var s=e.createShader(e.VERTEX_SHADER);e.shaderSource(s,i),e.compileShader(s),e.getShaderParameter(s,e.COMPILE_STATUS)||console.log("Vertex shader failed to compile: "+e.getShaderInfoLog(s));var r=e.createShader(e.FRAGMENT_SHADER);e.shaderSource(r,o),e.compileShader(r),e.getShaderParameter(r,e.COMPILE_STATUS)||console.log("Fragment shader failed to compile: "+e.getShaderInfoLog(r));var a=e.createProgram();e.attachShader(a,s),e.attachShader(a,r),e.linkProgram(a),e.getProgramParameter(a,e.LINK_STATUS)||console.log("Program failed to compile: "+e.getProgramInfoLog(a)),e.useProgram(a);var n=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,n),e.bufferData(e.ARRAY_BUFFER,new Float32Array([1,1,-1,1,1,-1,-1,-1]),e.STATIC_DRAW);var A=e.getAttribLocation(a,"vertexPos");e.enableVertexAttribArray(A),e.vertexAttribPointer(A,2,e.FLOAT,!1,0,0);var c=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,c),e.bufferData(e.ARRAY_BUFFER,new Float32Array([1,0,0,0,1,1,0,1]),e.STATIC_DRAW);var l=e.getAttribLocation(a,"texturePos");function d(t,i){var o=e.createTexture();return e.bindTexture(e.TEXTURE_2D,o),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.bindTexture(e.TEXTURE_2D,null),e.uniform1i(e.getUniformLocation(a,t),i),o}e.enableVertexAttribArray(l),e.vertexAttribPointer(l,2,e.FLOAT,!1,0,0);var u=d("ySampler",0),h=d("uSampler",1),p=d("vSampler",2);return{render:function(t,i,o,s,r){e.viewport(0,0,t,i),e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,u),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t,i,0,e.LUMINANCE,e.UNSIGNED_BYTE,o),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,h),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,i/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,s),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,p),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,t/2,i/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,r),e.drawArrays(e.TRIANGLE_STRIP,0,4)},destroy:function(){try{e.deleteProgram(a),e.deleteBuffer(n),e.deleteBuffer(c),e.deleteTexture(u),e.deleteTexture(h),e.deleteBuffer(p)}catch(e){}}}})(this.contextGl,this.player._opt.openWebglAlignment);this.contextGlRender=e.render,this.contextGlDestroy=e.destroy}updateVideoInfo(e){e.encTypeCode&&(this.videoInfo.encType=F[e.encTypeCode]),e.width&&(this.videoInfo.width=e.width),e.height&&(this.videoInfo.height=e.height),this.videoInfo.encType&&this.videoInfo.height&&this.videoInfo.width&&!this.init&&(this.player.emit(R.videoInfo,this.videoInfo),this.init=!0)}initCanvasViewSize(){this.$videoElement.width=this.videoInfo.width,this.$videoElement.height=this.videoInfo.height,this.resize()}screenshot(e,t,i,o){e=e||be(),o=o||M.download;const s={png:"image/png",jpeg:"image/jpeg",webp:"image/webp"};let r=.92;!s[t]&&M[t]&&(o=t,t="png",i=void 0),"string"==typeof i&&(o=i,i=void 0),void 0!==i&&(r=Number(i));const a=this.$videoElement.toDataURL(s[t]||s.png,r),n=fe(a);return o===M.base64?a:o===M.blob?n:void(o===M.download&&ge(n,e))}render(){}clearView(){}play(){}pause(){}resize(){this.player.debug.log("canvasVideo","resize");const e=this.player._opt;let t=this.player.width,i=this.player.height;if(e.hasControl&&!e.controlAutoHide){const e=this.controlHeight;Be()&&this.player.fullscreen?t-=e:i-=e}let o=this.$videoElement.width,s=this.$videoElement.height;const r=e.rotate;let a=(t-o)/2,n=(i-s)/2;270!==r&&90!==r||(o=this.$videoElement.height,s=this.$videoElement.width);const A=t/o,c=i/s;let l=A>c?c:A;e.isResize||A!==c&&(l=A+","+c),e.isFullResize&&(l=A>c?A:c);let d="scale("+l+")";r&&(d+=" rotate("+r+"deg)"),this.$videoElement.style.transform=d,this.$videoElement.style.left=a+"px",this.$videoElement.style.top=n+"px"}}class Pe extends Ve{constructor(e){super(e),this.controlHeight=38,this.player.debug.log("CanvasVideo","init")}destroy(){super.destroy(),this.player.debug.log("CanvasVideoLoader","destroy")}_initContext2D(){this.context2D=this.$videoElement.getContext("2d")}_initCanvasRender(){this.player._opt.useWCS&&!this._supportOffscreen()?(this.renderType=z,this._initContext2D()):this._supportOffscreen()?(this.renderType=H,this._bindOffscreen()):(this.renderType=G,this._initContextGl())}_supportOffscreen(){return"function"==typeof this.$videoElement.transferControlToOffscreen&&this.player._opt.useOffscreen}_bindOffscreen(){this.bitmaprenderer=this.$videoElement.getContext("bitmaprenderer")}render(e){switch(this.player.videoTimestamp=e.ts,this.renderType){case H:this.bitmaprenderer.transferFromImageBitmap(e.buffer);break;case G:this.contextGlRender(this.$videoElement.width,this.$videoElement.height,e.output[0],e.output[1],e.output[2]);break;case z:this.context2D.drawImage(e.videoFrame,0,0,this.$videoElement.width,this.$videoElement.height)}}clearView(){switch(this.renderType){case H:(function(e,t){const i=document.createElement("canvas");return i.width=e,i.height=t,createImageBitmap(i,0,0,e,t)})(this.$videoElement.width,this.$videoElement.height).then((e=>{this.bitmaprenderer.transferFromImageBitmap(e)}));break;case G:this.contextGl.clear(this.contextGl.COLOR_BUFFER_BIT);break;case z:this.context2D.clearRect(0,0,this.$videoElement.width,this.$videoElement.height)}}}class Qe extends Ue{constructor(e){super(),this.player=e;const t=document.createElement("video");t.muted=!0,t.style.position="absolute",t.style.top=0,t.style.left=0,e.$container.appendChild(t),this.$videoElement=t,this.videoInfo={width:"",height:"",encType:""};const i=this.player._opt;i.useWCS&&i.wcsUseVideoRender&&(this.trackGenerator=new MediaStreamTrackGenerator({kind:"video"}),t.srcObject=new MediaStream([this.trackGenerator]),this.vwriter=this.trackGenerator.writable.getWriter()),this.resize();const{proxy:o}=this.player.events;o(this.$videoElement,"canplay",(()=>{this.player.debug.log("Video","canplay")})),o(this.$videoElement,"waiting",(()=>{this.player.emit(R.videoWaiting)})),o(this.$videoElement,"timeupdate",(e=>{})),this.player.debug.log("Video","init")}destroy(){this.player.$container.removeChild(this.$videoElement),this.$videoElement&&(this.$videoElement.src="",this.$videoElement=null),this.trackGenerator&&(this.trackGenerator=null),this.vwriter&&(this.trackGenerator=null),this.init=!1,this.off(),this.player.debug.log("Video","destroy")}play(){this.$videoElement.play()}clearView(){}screenshot(e,t,i,o){e=e||be(),o=o||M.download;let s=.92;!{png:"image/png",jpeg:"image/jpeg",webp:"image/webp"}[t]&&M[t]&&(o=t,t="png",i=void 0),"string"==typeof i&&(o=i,i=void 0),void 0!==i&&(s=Number(i));const r=this.$videoElement;let a=document.createElement("canvas");a.width=r.videoWidth,a.height=r.videoHeight;a.getContext("2d").drawImage(r,0,0,a.width,a.height);const n=a.toDataURL(M[t]||M.png,s),A=fe(n);return o===M.base64?n:o===M.blob?A:void(o===M.download&&ge(A,e))}initCanvasViewSize(){this.resize()}render(e){this.vwriter&&this.vwriter.write(e.videoFrame)}resize(){let e=this.player.width,t=this.player.height;const i=this.player._opt,s=i.rotate;if(i.hasControl&&!i.controlAutoHide){const s=i.playType===o?48:38;Be()&&this.player.fullscreen?e-=s:t-=s}this.$videoElement.width=e,this.$videoElement.height=t,270!==s&&90!==s||(this.$videoElement.width=t,this.$videoElement.height=e);let r=(e-this.$videoElement.width)/2,a=(t-this.$videoElement.height)/2,n="contain";i.isResize||(n="fill"),i.isFullResize&&(n="none"),this.$videoElement.style.objectFit=n,this.$videoElement.style.transform="rotate("+s+"deg)",this.$videoElement.style.left=r+"px",this.$videoElement.style.top=a+"px"}}class We extends Ve{constructor(e){super(e),this.controlHeight=48,this.bufferList=[],this.playing=!1,this.playInterval=null,this.fps=1,this.preFps=1,this.playbackRate=1,this._firstTimestamp=null,this._renderFps=0,this._startfpsTime=null,this._startFpsTimestamp=null,this.player.debug.log("CanvasPlaybackLoader","init")}destroy(){this._stopSync(),this._firstTimestamp=null,this.playing=!1,this.playbackRate=1,this.fps=1,this.preFps=1,this.bufferList=[],this._renderFps=0,this._startfpsTime=null,this._startFpsTimestamp=null,super.destroy(),this.player.debug.log("CanvasPlaybackLoader","destroy")}_initCanvasRender(){this.renderType=G,this._initContextGl()}_sync(){this._stopSync(),this._doPlay(),this.playInterval=setInterval((()=>{this._doPlay()}),this.fragDuration)}_doPlay(){if(this.bufferList.length>0&&!this.player.seeking){const e=this.bufferList.shift();e&&e.buffer&&(this.player.handleRender(),this._updateStats(e.ts),this._doRender(e.buffer))}}_stopSync(){this.playInterval&&(clearInterval(this.playInterval),this.playInterval=null)}_doRender(e){this.contextGlRender(this.$videoElement.width,this.$videoElement.height,e[0],e[1],e[2])}_updateStats(e){this.player.updateStats({fps:!0,ts:e}),this._startfpsTime||(this._startfpsTime=e,this._startFpsTimestamp=be());const t=e,i=be(),o=i-this._startFpsTimestamp;o<=1e3?this._renderFps+=1:(this.player.emit(R.playbackStats,{fps:this._renderFps,start:this._startfpsTime,end:t,timestamp:o,dataTimestamp:t-this._startfpsTime,audioBufferSize:this.player.audio.bufferSize,videoBufferSize:this.player.video.bufferSize,ts:e}),this._renderFps=0,this._startfpsTime=t,this._startFpsTimestamp=i)}get rate(){return this.playbackRate}get fragDuration(){return Math.ceil(1e3/(this.preFps*this.playbackRate))}get bufferSize(){return this.bufferList.length}initFps(){this.preFps=ye(this.player.playback.fps,1,100)}setFps(e){e!==this.fps&&(this.fps=ye(e,1,100),this._sync())}setRate(e){e!==this.playbackRate&&(this.player.debug.log("CanvasPlaybackLoader","setRate",e),this.playbackRate=e,this._sync())}pushData(e){null===this._firstTimestamp&&(this._firstTimestamp=e.ts);const t={ts:e.ts-this._firstTimestamp,buffer:e.output};this.bufferList.push(t)}initVideo(){this._sync(),this.playing=!0}initVideoDelay(){const e=this.player._opt.playbackDelayTime;e>0?this.delayTimeout=setTimeout((()=>{this.initVideo()}),e):this.initVideo()}clearView(){this.contextGl.clear(this.contextGl.COLOR_BUFFER_BIT)}clear(){this.bufferList=[]}resume(){this._sync(),this.playing=!0}pause(){this._stopSync(),this.playing=!1}}class Ne{constructor(e){return new(Ne.getLoaderFactory(e._opt))(e)}static getLoaderFactory(e){return e.useMSE||e.useWCS&&e.wcsUseVideoRender?Qe:e.playType===o?We:Pe}}class Je extends Oe{constructor(e){super(),this.bufferList=[],this.player=e,this.scriptNode=null,this.hasInitScriptNode=!1,this.audioContextChannel=null,this.audioContext=new(window.AudioContext||window.webkitAudioContext),this.gainNode=this.audioContext.createGain();const t=this.audioContext.createBufferSource();t.buffer=this.audioContext.createBuffer(1,1,22050),t.connect(this.audioContext.destination),t.noteOn?t.noteOn(0):t.start(0),this.audioBufferSourceNode=t,this.mediaStreamAudioDestinationNode=this.audioContext.createMediaStreamDestination(),this.audioEnabled(!0),this.gainNode.gain.value=0,this.playing=!1,this.audioSyncVideoOption={diff:null},this.audioInfo={encType:"",channels:"",sampleRate:""},this.init=!1,this.hasAudio=!1,this.on(R.videoSyncAudio,(e=>{this.audioSyncVideoOption=e}))}destroy(){this.closeAudio(),this.audioContext.close(),this.audioContext=null,this.gainNode=null,this.init=!1,this.hasAudio=!1,this.playing=!1,this.scriptNode&&(this.scriptNode.onaudioprocess=me,this.scriptNode=null),this.audioBufferSourceNode=null,this.mediaStreamAudioDestinationNode=null,this.hasInitScriptNode=!1,this.audioSyncVideoOption={diff:null},this.audioInfo={encType:"",channels:"",sampleRate:""},this.off()}updateAudioInfo(e){e.encTypeCode&&(this.audioInfo.encType=V[e.encTypeCode]),e.channels&&(this.audioInfo.channels=e.channels),e.sampleRate&&(this.audioInfo.sampleRate=e.sampleRate),this.audioInfo.sampleRate&&this.audioInfo.channels&&this.audioInfo.encType&&!this.init&&(this.player.emit(R.audioInfo,this.audioInfo),this.init=!0)}get isPlaying(){return this.playing}get isMute(){return 0===this.gainNode.gain.value||this.isStateSuspended()}get volume(){return this.gainNode.gain.value}get bufferSize(){return this.bufferList.length}initScriptNode(){}mute(e){e?(this.isMute||this.player.emit(R.mute,e),this.setVolume(0),this.audioEnabled(!1),this.clear()):(this.isMute&&this.player.emit(R.mute,e),this.setVolume(.5),this.audioEnabled(!0))}setVolume(e){e=parseFloat(e).toFixed(2),isNaN(e)||(this.audioEnabled(!0),e=ye(e,0,1),this.gainNode.gain.value=e,this.gainNode.gain.setValueAtTime(e,this.audioContext.currentTime),this.player.emit(R.volumechange,this.player.volume))}closeAudio(){this.hasInitScriptNode&&(this.scriptNode&&this.scriptNode.disconnect(this.gainNode),this.gainNode&&this.gainNode.disconnect(this.audioContext.destination),this.gainNode&&this.gainNode.disconnect(this.mediaStreamAudioDestinationNode)),this.clear()}audioEnabled(e){e?"suspended"===this.audioContext.state&&this.audioContext.resume():"running"===this.audioContext.state&&this.audioContext.suspend()}isStateRunning(){return"running"===this.audioContext.state}isStateSuspended(){return"suspended"===this.audioContext.state}clear(){this.bufferList=[]}play(e,t){}pause(){this.audioSyncVideoOption={diff:null},this.playing=!1,this.clear()}resume(){this.playing=!0}setRate(e){}}class ze extends Je{constructor(e){super(e),this.player.debug.log("AudioContext","init")}destroy(){super.destroy(),this.player.debug.log("AudioContext","destroy")}initScriptNode(){if(this.playing=!0,this.hasInitScriptNode)return;const e=this.audioInfo.channels,t=this.audioContext.createScriptProcessor(1024,0,e);t.onaudioprocess=t=>{const i=t.outputBuffer;if(this.bufferList.length&&this.playing){if(!this.player._opt.useWCS&&!this.player._opt.useMSE){if(this.audioSyncVideoOption.diff>$)return void this.player.debug.warn("AudioContext",`audioSyncVideoOption more than diff :${this.audioSyncVideoOption.diff}, waiting`);if(this.audioSyncVideoOption.diff<-1e3){this.player.debug.warn("AudioContext",`audioSyncVideoOption less than diff :${this.audioSyncVideoOption.diff}, dropping`);let e=this.bufferList.shift();for(;e.ts-this.player.videoTimestamp<-1e3&&this.bufferList.length>0;)e=this.bufferList.shift();if(0===this.bufferList.length)return}}if(0===this.bufferList.length)return;const t=this.bufferList.shift();t&&t.ts&&(this.player.audioTimestamp=t.ts);for(let o=0;o<e;o++){const e=t.buffer[o],s=i.getChannelData(o);for(let t=0;t<1024;t++)s[t]=e[t]||0}}},t.connect(this.gainNode),this.scriptNode=t,this.gainNode.connect(this.audioContext.destination),this.gainNode.connect(this.mediaStreamAudioDestinationNode),this.hasInitScriptNode=!0}play(e,t){this.isMute||(this.hasAudio=!0,this.bufferList.push({buffer:e,ts:t}),this.bufferList.length>20&&(this.player.debug.warn("AudioContext",`bufferList is large: ${this.bufferList.length}`),this.bufferList.length>50&&this.bufferList.shift()))}}class Ge{constructor(e,t,i,o){this.player=e,this.audio=t,this.buffer=i,this.channel=o}extract(e,t){let i=this.provide(t);for(let t=0;t<i.size;t++)e[2*t]=i.left[t],e[2*t+1]=i.right[t];return this.audio.tempAudioTimestamp=i.ts,i.size}provide(e){let t=new Float32Array(e),i=new Float32Array(e),o=0,s=0,r=0,a=e/1024;if(this.buffer.length>a){for(let e=0;e<a;e++){const e=this.buffer.shift();2===this.channel?(t.set(e.buffer[0],r),i.set(e.buffer[1],r)):(t.set(e.buffer[0],r),i.set(e.buffer[0],r)),r+=1024,s=e.ts}o=t.length}return{size:o,ts:s,left:t,right:i}}destroy(){this.buffer=null,this.channel=null}}class He{constructor(){this._vector=new Float32Array,this._position=0,this._frameCount=0}get vector(){return this._vector}get position(){return this._position}get startIndex(){return 2*this._position}get frameCount(){return this._frameCount}get endIndex(){return 2*(this._position+this._frameCount)}clear(){this.receive(this._frameCount),this.rewind()}put(e){this._frameCount+=e}putSamples(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const o=2*(t=t||0);i>=0||(i=(e.length-o)/2);const s=2*i;this.ensureCapacity(i+this._frameCount);const r=this.endIndex;this.vector.set(e.subarray(o,o+s),r),this._frameCount+=i}putBuffer(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;t=t||0,i>=0||(i=e.frameCount-t),this.putSamples(e.vector,e.position+t,i)}receive(e){e>=0&&!(e>this._frameCount)||(e=this.frameCount),this._frameCount-=e,this._position+=e}receiveSamples(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const i=2*t,o=this.startIndex;e.set(this._vector.subarray(o,o+i)),this.receive(t)}extract(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const o=this.startIndex+2*t,s=2*i;e.set(this._vector.subarray(o,o+s))}ensureCapacity(){const e=parseInt(2*(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0));if(this._vector.length<e){const t=new Float32Array(e);t.set(this._vector.subarray(this.startIndex,this.endIndex)),this._vector=t,this._position=0}else this.rewind()}ensureAdditionalCapacity(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.ensureCapacity(this._frameCount+e)}rewind(){this._position>0&&(this._vector.set(this._vector.subarray(this.startIndex,this.endIndex)),this._position=0)}}class qe{constructor(e){e?(this._inputBuffer=new He,this._outputBuffer=new He):this._inputBuffer=this._outputBuffer=null}get inputBuffer(){return this._inputBuffer}set inputBuffer(e){this._inputBuffer=e}get outputBuffer(){return this._outputBuffer}set outputBuffer(e){this._outputBuffer=e}clear(){this._inputBuffer.clear(),this._outputBuffer.clear()}}class Ye extends qe{constructor(e){super(e),this.reset(),this._rate=1}set rate(e){this._rate=e}reset(){this.slopeCount=0,this.prevSampleL=0,this.prevSampleR=0}clone(){const e=new Ye;return e.rate=this._rate,e}process(){const e=this._inputBuffer.frameCount;this._outputBuffer.ensureAdditionalCapacity(e/this._rate+1);const t=this.transpose(e);this._inputBuffer.receive(),this._outputBuffer.put(t)}transpose(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(0===e)return 0;const t=this._inputBuffer.vector,i=this._inputBuffer.startIndex,o=this._outputBuffer.vector,s=this._outputBuffer.endIndex;let r=0,a=0;for(;this.slopeCount<1;)o[s+2*a]=(1-this.slopeCount)*this.prevSampleL+this.slopeCount*t[i],o[s+2*a+1]=(1-this.slopeCount)*this.prevSampleR+this.slopeCount*t[i+1],a+=1,this.slopeCount+=this._rate;if(this.slopeCount-=1,1!==e)e:for(;;){for(;this.slopeCount>1;)if(this.slopeCount-=1,r+=1,r>=e-1)break e;const n=i+2*r;o[s+2*a]=(1-this.slopeCount)*t[n]+this.slopeCount*t[n+2],o[s+2*a+1]=(1-this.slopeCount)*t[n+1]+this.slopeCount*t[n+3],a+=1,this.slopeCount+=this._rate}return this.prevSampleL=t[i+2*e-2],this.prevSampleR=t[i+2*e-1],a}}class Xe{constructor(e){this._pipe=e}get pipe(){return this._pipe}get inputBuffer(){return this._pipe.inputBuffer}get outputBuffer(){return this._pipe.outputBuffer}fillInputBuffer(){throw new Error("fillInputBuffer() not overridden")}fillOutputBuffer(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;for(;this.outputBuffer.frameCount<e;){const e=16384-this.inputBuffer.frameCount;if(this.fillInputBuffer(e),this.inputBuffer.frameCount<16384)break;this._pipe.process()}}clear(){this._pipe.clear()}}const Ze=function(){};class Ke extends Xe{constructor(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Ze;super(t),this.callback=i,this.sourceSound=e,this.historyBufferSize=22050,this._sourcePosition=0,this.outputBufferPosition=0,this._position=0}get position(){return this._position}set position(e){if(e>this._position)throw new RangeError("New position may not be greater than current position");const t=this.outputBufferPosition-(this._position-e);if(t<0)throw new RangeError("New position falls outside of history buffer");this.outputBufferPosition=t,this._position=e}get sourcePosition(){return this._sourcePosition}set sourcePosition(e){this.clear(),this._sourcePosition=e}onEnd(){this.callback()}fillInputBuffer(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const t=new Float32Array(2*e),i=this.sourceSound.extract(t,e,this._sourcePosition);this._sourcePosition+=i,this.inputBuffer.putSamples(t,0,i)}extract(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.fillOutputBuffer(this.outputBufferPosition+t);const i=Math.min(t,this.outputBuffer.frameCount-this.outputBufferPosition);this.outputBuffer.extract(e,this.outputBufferPosition,i);const o=this.outputBufferPosition+i;return this.outputBufferPosition=Math.min(this.historyBufferSize,o),this.outputBuffer.receive(Math.max(o-this.historyBufferSize,0)),this._position+=i,i}handleSampleData(e){this.extract(e.data,4096)}clear(){super.clear(),this.outputBufferPosition=0}}const _e=[[124,186,248,310,372,434,496,558,620,682,744,806,868,930,992,1054,1116,1178,1240,1302,1364,1426,1488,0],[-100,-75,-50,-25,25,50,75,100,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[-20,-15,-10,-5,5,10,15,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[-4,-3,-2,-1,1,2,3,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]],$e=-10/1.5;class et extends qe{constructor(e){super(e),this._quickSeek=!0,this.midBufferDirty=!1,this.midBuffer=null,this.overlapLength=0,this.autoSeqSetting=!0,this.autoSeekSetting=!0,this._tempo=1,this.setParameters(44100,0,0,8)}clear(){super.clear(),this.clearMidBuffer()}clearMidBuffer(){this.midBufferDirty&&(this.midBufferDirty=!1,this.midBuffer=null)}setParameters(e,t,i,o){e>0&&(this.sampleRate=e),o>0&&(this.overlapMs=o),t>0?(this.sequenceMs=t,this.autoSeqSetting=!1):this.autoSeqSetting=!0,i>0?(this.seekWindowMs=i,this.autoSeekSetting=!1):this.autoSeekSetting=!0,this.calculateSequenceParameters(),this.calculateOverlapLength(this.overlapMs),this.tempo=this._tempo}set tempo(e){let t;this._tempo=e,this.calculateSequenceParameters(),this.nominalSkip=this._tempo*(this.seekWindowLength-this.overlapLength),this.skipFract=0,t=Math.floor(this.nominalSkip+.5),this.sampleReq=Math.max(t+this.overlapLength,this.seekWindowLength)+this.seekLength}get tempo(){return this._tempo}get inputChunkSize(){return this.sampleReq}get outputChunkSize(){return this.overlapLength+Math.max(0,this.seekWindowLength-2*this.overlapLength)}calculateOverlapLength(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;e=this.sampleRate*t/1e3,e=e<16?16:e,e-=e%8,this.overlapLength=e,this.refMidBuffer=new Float32Array(2*this.overlapLength),this.midBuffer=new Float32Array(2*this.overlapLength)}checkLimits(e,t,i){return e<t?t:e>i?i:e}calculateSequenceParameters(){let e,t;this.autoSeqSetting&&(e=150+-50*this._tempo,e=this.checkLimits(e,50,125),this.sequenceMs=Math.floor(e+.5)),this.autoSeekSetting&&(t=28.333333333333332+$e*this._tempo,t=this.checkLimits(t,15,25),this.seekWindowMs=Math.floor(t+.5)),this.seekWindowLength=Math.floor(this.sampleRate*this.sequenceMs/1e3),this.seekLength=Math.floor(this.sampleRate*this.seekWindowMs/1e3)}set quickSeek(e){this._quickSeek=e}clone(){const e=new et;return e.tempo=this._tempo,e.setParameters(this.sampleRate,this.sequenceMs,this.seekWindowMs,this.overlapMs),e}seekBestOverlapPosition(){return this._quickSeek?this.seekBestOverlapPositionStereoQuick():this.seekBestOverlapPositionStereo()}seekBestOverlapPositionStereo(){let e,t,i,o=0;for(this.preCalculateCorrelationReferenceStereo(),e=0,t=Number.MIN_VALUE;o<this.seekLength;o+=1)i=this.calculateCrossCorrelationStereo(2*o,this.refMidBuffer),i>t&&(t=i,e=o);return e}seekBestOverlapPositionStereoQuick(){let e,t,i,o,s,r=0;for(this.preCalculateCorrelationReferenceStereo(),t=Number.MIN_VALUE,e=0,o=0,s=0;r<4;r+=1){let a=0;for(;_e[r][a]&&(s=o+_e[r][a],!(s>=this.seekLength));)i=this.calculateCrossCorrelationStereo(2*s,this.refMidBuffer),i>t&&(t=i,e=s),a+=1;o=e}return e}preCalculateCorrelationReferenceStereo(){let e,t,i=0;for(;i<this.overlapLength;i+=1)t=i*(this.overlapLength-i),e=2*i,this.refMidBuffer[e]=this.midBuffer[e]*t,this.refMidBuffer[e+1]=this.midBuffer[e+1]*t}calculateCrossCorrelationStereo(e,t){const i=this._inputBuffer.vector;e+=this._inputBuffer.startIndex;let o=0,s=2;const r=2*this.overlapLength;let a;for(;s<r;s+=2)a=s+e,o+=i[a]*t[s]+i[a+1]*t[s+1];return o}overlap(e){this.overlapStereo(2*e)}overlapStereo(e){const t=this._inputBuffer.vector;e+=this._inputBuffer.startIndex;const i=this._outputBuffer.vector,o=this._outputBuffer.endIndex;let s,r,a=0;const n=1/this.overlapLength;let A,c,l;for(;a<this.overlapLength;a+=1)r=(this.overlapLength-a)*n,A=a*n,s=2*a,c=s+e,l=s+o,i[l+0]=t[c+0]*A+this.midBuffer[s+0]*r,i[l+1]=t[c+1]*A+this.midBuffer[s+1]*r}process(){let e,t,i;if(null===this.midBuffer){if(this._inputBuffer.frameCount<this.overlapLength)return;this.midBuffer=new Float32Array(2*this.overlapLength),this._inputBuffer.receiveSamples(this.midBuffer,this.overlapLength)}for(;this._inputBuffer.frameCount>=this.sampleReq;){e=this.seekBestOverlapPosition(),this._outputBuffer.ensureAdditionalCapacity(this.overlapLength),this.overlap(Math.floor(e)),this._outputBuffer.put(this.overlapLength),t=this.seekWindowLength-2*this.overlapLength,t>0&&this._outputBuffer.putBuffer(this._inputBuffer,e+this.overlapLength,t);const o=this._inputBuffer.startIndex+2*(e+this.seekWindowLength-this.overlapLength);this.midBuffer.set(this._inputBuffer.vector.subarray(o,o+2*this.overlapLength)),this.skipFract+=this.nominalSkip,i=Math.floor(this.skipFract),this.skipFract-=i,this._inputBuffer.receive(i)}}}const tt=function(e,t){return(e>t?e-t:t-e)>1e-10};class it{constructor(){this.transposer=new Ye(!1),this.stretch=new et(!1),this._inputBuffer=new He,this._intermediateBuffer=new He,this._outputBuffer=new He,this._rate=0,this._tempo=0,this.virtualPitch=1,this.virtualRate=1,this.virtualTempo=1,this.calculateEffectiveRateAndTempo()}clear(){this.transposer.clear(),this.stretch.clear()}clone(){const e=new it;return e.rate=this.rate,e.tempo=this.tempo,e}get rate(){return this._rate}set rate(e){this.virtualRate=e,this.calculateEffectiveRateAndTempo()}set rateChange(e){this._rate=1+.01*e}get tempo(){return this._tempo}set tempo(e){this.virtualTempo=e,this.calculateEffectiveRateAndTempo()}set tempoChange(e){this.tempo=1+.01*e}set pitch(e){this.virtualPitch=e,this.calculateEffectiveRateAndTempo()}set pitchOctaves(e){this.pitch=Math.exp(.69314718056*e),this.calculateEffectiveRateAndTempo()}set pitchSemitones(e){this.pitchOctaves=e/12}get inputBuffer(){return this._inputBuffer}get outputBuffer(){return this._outputBuffer}calculateEffectiveRateAndTempo(){const e=this._tempo,t=this._rate;this._tempo=this.virtualTempo/this.virtualPitch,this._rate=this.virtualRate*this.virtualPitch,tt(this._tempo,e)&&(this.stretch.tempo=this._tempo),tt(this._rate,t)&&(this.transposer.rate=this._rate),this._rate>1?this._outputBuffer!=this.transposer.outputBuffer&&(this.stretch.inputBuffer=this._inputBuffer,this.stretch.outputBuffer=this._intermediateBuffer,this.transposer.inputBuffer=this._intermediateBuffer,this.transposer.outputBuffer=this._outputBuffer):this._outputBuffer!=this.stretch.outputBuffer&&(this.transposer.inputBuffer=this._inputBuffer,this.transposer.outputBuffer=this._intermediateBuffer,this.stretch.inputBuffer=this._intermediateBuffer,this.stretch.outputBuffer=this._outputBuffer)}process(){this._rate>1?(this.stretch.process(),this.transposer.process()):(this.transposer.process(),this.stretch.process())}}class ot{constructor(e,t,i){this.player=e,this.audio=t,this.soundTouch=new it,this.soundTouch.tempo=1,this.soundTouch.rate=1,this.filter=new Ke(i,this.soundTouch,1024)}setRate(e){this.soundTouch.tempo=e}provide(e){let t=new Float32Array(2*e),i=this.filter.extract(t,e),o=new Float32Array(i),s=new Float32Array(i);for(let e=0;e<i;e++)o[e]=t[2*e],s[e]=t[2*e+1];return{size:i,left:o,right:s,ts:this.audio.tempAudioTimestamp||0}}destroy(){this.soundTouch&&(this.soundTouch.clear(),this.soundTouch=null),this.filter&&(this.filter=null)}}class st extends Je{constructor(e){super(e),this.playbackRate=1,this.rateProcessor=null,this.processor=null,this.firstTimestamp=null,this.player.debug.log("AudioPlaybackContext","init")}destroy(){super.destroy(),this.processor&&(this.processor.destroy(),this.processor=null),this.rateProcessor&&(this.rateProcessor.destroy(),this.rateProcessor=null),this.player.debug.log("AudioPlaybackLoader","destroy")}initScriptNode(e){if(this.playing=!0,this.hasInitScriptNode)return;const t=this.audioInfo.channels;this.processor=new Ge(this.player,this,this.bufferList,this.audioContextChannel),this.rateProcessor=new ot(this.player,this,this.processor);const i=this.audioContext.createScriptProcessor(1024,0,t);i.onaudioprocess=e=>{const i=e.outputBuffer;if(this.bufferList.length&&this.playing){if(this.audioSyncVideoOption.diff>$)return;if(this.audioSyncVideoOption.diff<-1e3){let e=this._provide(i.length);for(;e.ts-this.player.videoTimestamp<-1e3&&e.size>0;)e=this._provide(i.length);if(0===e.size)return}const e=this._provide(i.length);if(0===e.size)return;e&&e.ts&&(this.player.audioTimestamp=e.ts);for(let o=0;o<t;o++){const t=e.buffer[o],s=i.getChannelData(o);for(let e=0;e<1024;e++)s[e]=t[e]||0}}},i.connect(this.gainNode),this.scriptNode=i,this.gainNode.connect(this.audioContext.destination),this.gainNode.connect(this.mediaStreamAudioDestinationNode),this.hasInitScriptNode=!0}initScriptNodeDelay(e){const t=this.player._opt.playbackDelayTime;t>0?setTimeout((()=>{this.initScriptNode(e)}),t):this.initScriptNode(e)}setRate(e){e!==this.playbackRate&&this.rateProcessor&&(this.player.debug.log("AudioPlaybackContext","setRate",e),this.playbackRate=e,this.rateProcessor.setRate(e))}play(e,t){this.isMute||(null===this.firstTimestamp&&(this.firstTimestamp=t),this.hasAudio=!0,this.bufferList.push({buffer:e,ts:t-this.firstTimestamp}))}_provide(e){return(1===this.playbackRate?this.processor:this.rateProcessor).provide(e)}}class rt{constructor(e){return new(rt.getLoaderFactory(e._opt))(e)}static getLoaderFactory(e){return e.playType===o?st:ze}}class at extends Oe{constructor(e){super(),this.player=e,this.playing=!1,this.abortController=new AbortController,this.streamRate=Se((t=>{e.emit(R.kBps,(t/1024).toFixed(2))})),e.debug.log("FetchStream","init")}destroy(){this.abort(),this.off(),this.streamRate=null,this.player.debug.log("FetchStream","destroy")}fetchStream(e){const{demux:t}=this.player;this.player._times.streamStart=be(),fetch(e,{signal:this.abortController.signal}).then((e=>{const i=e.body.getReader();this.emit(R.streamSuccess);const o=()=>{i.read().then((e=>{let{done:i,value:s}=e;i?t.close():(this.streamRate&&this.streamRate(s.byteLength),t.dispatch(s),o())})).catch((e=>{t.close(),this.emit(I.fetchError,e),this.player.emit(R.error,I.fetchError),this.abort()}))};o()})).catch((e=>{this.abort(),this.emit(I.fetchError,e),this.player.emit(R.error,I.fetchError)}))}abort(){this.abortController&&(this.abortController.abort(),this.abortController=null)}}class nt extends Oe{constructor(e){super(),this.player=e,this.socket=null,this.socketStatus=j,this.wsUrl=null,this.streamRate=Se((t=>{e.emit(R.kBps,(t/1024).toFixed(2))}))}destroy(){this.socket&&(this.socket.close(),this.socket=null),this.socketStatus=j,this.streamRate=null,this.wsUrl=null,this.off(),this.player.debug.log("websocketLoader","destroy")}_createWebSocket(){const e=this.player,{debug:t,events:{proxy:i},demux:o}=e;this.socket=new WebSocket(this.wsUrl),this.socket.binaryType="arraybuffer",i(this.socket,"open",(()=>{this.emit(R.streamSuccess),t.log("websocketLoader","socket open"),this.socketStatus=x})),i(this.socket,"message",(e=>{this.streamRate&&this.streamRate(e.data.byteLength),this._handleMessage(e.data)})),i(this.socket,"close",(()=>{t.log("websocketLoader","socket close"),this.emit(R.streamEnd),this.socketStatus=D})),i(this.socket,"error",(e=>{t.log("websocketLoader","socket error"),this.emit(I.websocketError,e),this.player.emit(R.error,I.websocketError),this.socketStatus=L,o.close(),t.log("websocketLoader","socket error:",e)}))}_handleMessage(e){const{demux:t}=this.player;t?t.dispatch(e):this.player.debug.warn("websocketLoader","websocket handle message demux is null")}fetchStream(e){this.player._times.streamStart=be(),this.wsUrl=e,this._createWebSocket()}}class At{constructor(e){return new(At.getLoaderFactory(e._opt.protocol))(e)}static getLoaderFactory(i){return i===t?at:i===e?nt:void 0}}var ct=he((function(e){function t(e,s){if(!e)throw"First parameter is required.";s=new i(e,s=s||{type:"video"});var r=this;function a(t){t&&(s.initCallback=function(){t(),t=s.initCallback=null});var i=new o(e,s);(h=new i(e,s)).record(),u("recording"),s.disableLogs||console.log("Initialized recorderType:",h.constructor.name,"for output-type:",s.type)}function n(e){if(e=e||function(){},h){if("paused"===r.state)return r.resumeRecording(),void setTimeout((function(){n(e)}),1);"recording"===r.state||s.disableLogs||console.warn('Recording state should be: "recording", however current state is: ',r.state),s.disableLogs||console.log("Stopped recording "+s.type+" stream."),"gif"!==s.type?h.stop(t):(h.stop(),t()),u("stopped")}else m();function t(t){if(h){Object.keys(h).forEach((function(e){"function"!=typeof h[e]&&(r[e]=h[e])}));var i=h.blob;if(!i){if(!t)throw"Recording failed.";h.blob=i=t}if(i&&!s.disableLogs&&console.log(i.type,"->",g(i.size)),e){var o;try{o=l.createObjectURL(i)}catch(e){}"function"==typeof e.call?e.call(r,o):e(o)}s.autoWriteToDisk&&c((function(e){var t={};t[s.type+"Blob"]=e,I.Store(t)}))}else"function"==typeof e.call?e.call(r,""):e("")}}function A(e){postMessage((new FileReaderSync).readAsDataURL(e))}function c(e,t){if(!e)throw"Pass a callback function over getDataURL.";var i=t?t.blob:(h||{}).blob;if(!i)return s.disableLogs||console.warn("Blob encoder did not finish its job yet."),void setTimeout((function(){c(e,t)}),1e3);if("undefined"==typeof Worker||navigator.mozGetUserMedia){var o=new FileReader;o.readAsDataURL(i),o.onload=function(t){e(t.target.result)}}else{var r=function(e){try{var t=l.createObjectURL(new Blob([e.toString(),"this.onmessage =  function (eee) {"+e.name+"(eee.data);}"],{type:"application/javascript"})),i=new Worker(t);return l.revokeObjectURL(t),i}catch(e){}}(A);r.onmessage=function(t){e(t.data)},r.postMessage(i)}}function d(e){e=e||0,"paused"!==r.state?"stopped"!==r.state&&(e>=r.recordingDuration?n(r.onRecordingStopped):(e+=1e3,setTimeout((function(){d(e)}),1e3))):setTimeout((function(){d(e)}),1e3)}function u(e){r&&(r.state=e,"function"==typeof r.onStateChanged.call?r.onStateChanged.call(r,e):r.onStateChanged(e))}var h,p='It seems that recorder is destroyed or "startRecording" is not invoked for '+s.type+" recorder.";function m(){!0!==s.disableLogs&&console.warn(p)}var f={startRecording:function(t){return s.disableLogs||console.log("RecordRTC version: ",r.version),t&&(s=new i(e,t)),s.disableLogs||console.log("started recording "+s.type+" stream."),h?(h.clearRecordedData(),h.record(),u("recording"),r.recordingDuration&&d(),r):(a((function(){r.recordingDuration&&d()})),r)},stopRecording:n,pauseRecording:function(){h?"recording"===r.state?(u("paused"),h.pause(),s.disableLogs||console.log("Paused recording.")):s.disableLogs||console.warn("Unable to pause the recording. Recording state: ",r.state):m()},resumeRecording:function(){h?"paused"===r.state?(u("recording"),h.resume(),s.disableLogs||console.log("Resumed recording.")):s.disableLogs||console.warn("Unable to resume the recording. Recording state: ",r.state):m()},initRecorder:a,setRecordingDuration:function(e,t){if(void 0===e)throw"recordingDuration is required.";if("number"!=typeof e)throw"recordingDuration must be a number.";return r.recordingDuration=e,r.onRecordingStopped=t||function(){},{onRecordingStopped:function(e){r.onRecordingStopped=e}}},clearRecordedData:function(){h?(h.clearRecordedData(),s.disableLogs||console.log("Cleared old recorded data.")):m()},getBlob:function(){if(h)return h.blob;m()},getDataURL:c,toURL:function(){if(h)return l.createObjectURL(h.blob);m()},getInternalRecorder:function(){return h},save:function(e){h?b(h.blob,e):m()},getFromDisk:function(e){h?t.getFromDisk(s.type,e):m()},setAdvertisementArray:function(e){s.advertisement=[];for(var t=e.length,i=0;i<t;i++)s.advertisement.push({duration:i,image:e[i]})},blob:null,bufferSize:0,sampleRate:0,buffer:null,reset:function(){"recording"!==r.state||s.disableLogs||console.warn("Stop an active recorder."),h&&"function"==typeof h.clearRecordedData&&h.clearRecordedData(),h=null,u("inactive"),r.blob=null},onStateChanged:function(e){s.disableLogs||console.log("Recorder state changed:",e)},state:"inactive",getState:function(){return r.state},destroy:function(){var e=s.disableLogs;s={disableLogs:!0},r.reset(),u("destroyed"),f=r=null,k.AudioContextConstructor&&(k.AudioContextConstructor.close(),k.AudioContextConstructor=null),s.disableLogs=e,s.disableLogs||console.log("RecordRTC is destroyed.")},version:"5.6.2"};if(!this)return r=f,f;for(var y in f)this[y]=f[y];return r=this,f}function i(e,t){return t.recorderType||t.type||(t.audio&&t.video?t.type="video":t.audio&&!t.video&&(t.type="audio")),t.recorderType&&!t.type&&(t.recorderType===R||t.recorderType===C||void 0!==L&&t.recorderType===L?t.type="video":t.recorderType===j?t.type="gif":t.recorderType===E?t.type="audio":t.recorderType===B&&(v(e,"audio").length&&v(e,"video").length||!v(e,"audio").length&&v(e,"video").length?t.type="video":v(e,"audio").length&&!v(e,"video").length&&(t.type="audio"))),void 0!==B&&"undefined"!=typeof MediaRecorder&&"requestData"in MediaRecorder.prototype&&(t.mimeType||(t.mimeType="video/webm"),t.type||(t.type=t.mimeType.split("/")[0]),t.bitsPerSecond),t.type||(t.mimeType&&(t.type=t.mimeType.split("/")[0]),t.type||(t.type="audio")),t}function o(e,t){var i;return(p||d||u)&&(i=E),"undefined"!=typeof MediaRecorder&&"requestData"in MediaRecorder.prototype&&!p&&(i=B),"video"===t.type&&(p||u)&&(i=R,void 0!==L&&"undefined"!=typeof ReadableStream&&(i=L)),"gif"===t.type&&(i=j),"canvas"===t.type&&(i=C),S()&&i!==C&&i!==j&&"undefined"!=typeof MediaRecorder&&"requestData"in MediaRecorder.prototype&&(v(e,"video").length||v(e,"audio").length)&&("audio"===t.type?"function"==typeof MediaRecorder.isTypeSupported&&MediaRecorder.isTypeSupported("audio/webm")&&(i=B):"function"==typeof MediaRecorder.isTypeSupported&&MediaRecorder.isTypeSupported("video/webm")&&(i=B)),e instanceof Array&&e.length&&(i=D),t.recorderType&&(i=t.recorderType),!t.disableLogs&&i&&i.name&&console.log("Using recorderType:",i.name||i.constructor.name),!i&&m&&(i=B),i}function s(e){this.addStream=function(t){t&&(e=t)},this.mediaType={audio:!0,video:!0},this.startRecording=function(){var i,o=this.mediaType,s=this.mimeType||{audio:null,video:null,gif:null};if("function"!=typeof o.audio&&S()&&!v(e,"audio").length&&(o.audio=!1),"function"!=typeof o.video&&S()&&!v(e,"video").length&&(o.video=!1),"function"!=typeof o.gif&&S()&&!v(e,"video").length&&(o.gif=!1),!o.audio&&!o.video&&!o.gif)throw"MediaStream must have either audio or video tracks.";if(o.audio&&(i=null,"function"==typeof o.audio&&(i=o.audio),this.audioRecorder=new t(e,{type:"audio",bufferSize:this.bufferSize,sampleRate:this.sampleRate,numberOfAudioChannels:this.numberOfAudioChannels||2,disableLogs:this.disableLogs,recorderType:i,mimeType:s.audio,timeSlice:this.timeSlice,onTimeStamp:this.onTimeStamp}),o.video||this.audioRecorder.startRecording()),o.video){i=null,"function"==typeof o.video&&(i=o.video);var r=e;if(S()&&o.audio&&"function"==typeof o.audio){var a=v(e,"video")[0];h?((r=new f).addTrack(a),i&&i===R&&(i=B)):(r=new f).addTrack(a)}this.videoRecorder=new t(r,{type:"video",video:this.video,canvas:this.canvas,frameInterval:this.frameInterval||10,disableLogs:this.disableLogs,recorderType:i,mimeType:s.video,timeSlice:this.timeSlice,onTimeStamp:this.onTimeStamp,workerPath:this.workerPath,webAssemblyPath:this.webAssemblyPath,frameRate:this.frameRate,bitrate:this.bitrate}),o.audio||this.videoRecorder.startRecording()}if(o.audio&&o.video){var n=this,A=!0===S();(o.audio instanceof E&&o.video||!0!==o.audio&&!0!==o.video&&o.audio!==o.video)&&(A=!1),!0===A?(n.audioRecorder=null,n.videoRecorder.startRecording()):n.videoRecorder.initRecorder((function(){n.audioRecorder.initRecorder((function(){n.videoRecorder.startRecording(),n.audioRecorder.startRecording()}))}))}o.gif&&(i=null,"function"==typeof o.gif&&(i=o.gif),this.gifRecorder=new t(e,{type:"gif",frameRate:this.frameRate||200,quality:this.quality||10,disableLogs:this.disableLogs,recorderType:i,mimeType:s.gif}),this.gifRecorder.startRecording())},this.stopRecording=function(e){e=e||function(){},this.audioRecorder&&this.audioRecorder.stopRecording((function(t){e(t,"audio")})),this.videoRecorder&&this.videoRecorder.stopRecording((function(t){e(t,"video")})),this.gifRecorder&&this.gifRecorder.stopRecording((function(t){e(t,"gif")}))},this.pauseRecording=function(){this.audioRecorder&&this.audioRecorder.pauseRecording(),this.videoRecorder&&this.videoRecorder.pauseRecording(),this.gifRecorder&&this.gifRecorder.pauseRecording()},this.resumeRecording=function(){this.audioRecorder&&this.audioRecorder.resumeRecording(),this.videoRecorder&&this.videoRecorder.resumeRecording(),this.gifRecorder&&this.gifRecorder.resumeRecording()},this.getBlob=function(e){var t={};return this.audioRecorder&&(t.audio=this.audioRecorder.getBlob()),this.videoRecorder&&(t.video=this.videoRecorder.getBlob()),this.gifRecorder&&(t.gif=this.gifRecorder.getBlob()),e&&e(t),t},this.destroy=function(){this.audioRecorder&&(this.audioRecorder.destroy(),this.audioRecorder=null),this.videoRecorder&&(this.videoRecorder.destroy(),this.videoRecorder=null),this.gifRecorder&&(this.gifRecorder.destroy(),this.gifRecorder=null)},this.getDataURL=function(e){function t(e,t){if("undefined"!=typeof Worker){var i=function(e){var t,i=l.createObjectURL(new Blob([e.toString(),"this.onmessage =  function (eee) {"+e.name+"(eee.data);}"],{type:"application/javascript"})),o=new Worker(i);if(void 0!==l)t=l;else{if("undefined"==typeof webkitURL)throw"Neither URL nor webkitURL detected.";t=webkitURL}return t.revokeObjectURL(i),o}((function(e){postMessage((new FileReaderSync).readAsDataURL(e))}));i.onmessage=function(e){t(e.data)},i.postMessage(e)}else{var o=new FileReader;o.readAsDataURL(e),o.onload=function(e){t(e.target.result)}}}this.getBlob((function(i){i.audio&&i.video?t(i.audio,(function(o){t(i.video,(function(t){e({audio:o,video:t})}))})):i.audio?t(i.audio,(function(t){e({audio:t})})):i.video&&t(i.video,(function(t){e({video:t})}))}))},this.writeToDisk=function(){t.writeToDisk({audio:this.audioRecorder,video:this.videoRecorder,gif:this.gifRecorder})},this.save=function(e){(e=e||{audio:!0,video:!0,gif:!0}).audio&&this.audioRecorder&&this.audioRecorder.save("string"==typeof e.audio?e.audio:""),e.video&&this.videoRecorder&&this.videoRecorder.save("string"==typeof e.video?e.video:""),e.gif&&this.gifRecorder&&this.gifRecorder.save("string"==typeof e.gif?e.gif:"")}}t.version="5.6.2",e.exports=t,t.getFromDisk=function(e,t){if(!t)throw"callback is mandatory.";console.log("Getting recorded "+("all"===e?"blobs":e+" blob ")+" from disk!"),I.Fetch((function(i,o){"all"!==e&&o===e+"Blob"&&t&&t(i),"all"===e&&t&&t(i,o.replace("Blob",""))}))},t.writeToDisk=function(e){console.log("Writing recorded blob(s) to disk!"),(e=e||{}).audio&&e.video&&e.gif?e.audio.getDataURL((function(t){e.video.getDataURL((function(i){e.gif.getDataURL((function(e){I.Store({audioBlob:t,videoBlob:i,gifBlob:e})}))}))})):e.audio&&e.video?e.audio.getDataURL((function(t){e.video.getDataURL((function(e){I.Store({audioBlob:t,videoBlob:e})}))})):e.audio&&e.gif?e.audio.getDataURL((function(t){e.gif.getDataURL((function(e){I.Store({audioBlob:t,gifBlob:e})}))})):e.video&&e.gif?e.video.getDataURL((function(t){e.gif.getDataURL((function(e){I.Store({videoBlob:t,gifBlob:e})}))})):e.audio?e.audio.getDataURL((function(e){I.Store({audioBlob:e})})):e.video?e.video.getDataURL((function(e){I.Store({videoBlob:e})})):e.gif&&e.gif.getDataURL((function(e){I.Store({gifBlob:e})}))},s.getFromDisk=t.getFromDisk,s.writeToDisk=t.writeToDisk,void 0!==t&&(t.MRecordRTC=s);var r;(r=void 0!==ue?ue:null)&&"undefined"==typeof window&&void 0!==ue&&(ue.navigator={userAgent:"Fake/5.0 (FakeOS) AppleWebKit/123 (KHTML, like Gecko) Fake/12.3.4567.89 Fake/123.45",getUserMedia:function(){}},ue.console||(ue.console={}),void 0!==ue.console.log&&void 0!==ue.console.error||(ue.console.error=ue.console.log=ue.console.log||function(){console.log(arguments)}),"undefined"==typeof document&&(r.document={documentElement:{appendChild:function(){return""}}},document.createElement=document.captureStream=document.mozCaptureStream=function(){var e={getContext:function(){return e},play:function(){},pause:function(){},drawImage:function(){},toDataURL:function(){return""},style:{}};return e},r.HTMLVideoElement=function(){}),"undefined"==typeof location&&(r.location={protocol:"file:",href:"",hash:""}),"undefined"==typeof screen&&(r.screen={width:0,height:0}),void 0===l&&(r.URL={createObjectURL:function(){return""},revokeObjectURL:function(){return""}}),r.window=ue);var a=window.requestAnimationFrame;if(void 0===a)if("undefined"!=typeof webkitRequestAnimationFrame)a=webkitRequestAnimationFrame;else if("undefined"!=typeof mozRequestAnimationFrame)a=mozRequestAnimationFrame;else if("undefined"!=typeof msRequestAnimationFrame)a=msRequestAnimationFrame;else if(void 0===a){var n=0;a=function(e,t){var i=(new Date).getTime(),o=Math.max(0,16-(i-n)),s=setTimeout((function(){e(i+o)}),o);return n=i+o,s}}var A=window.cancelAnimationFrame;void 0===A&&("undefined"!=typeof webkitCancelAnimationFrame?A=webkitCancelAnimationFrame:"undefined"!=typeof mozCancelAnimationFrame?A=mozCancelAnimationFrame:"undefined"!=typeof msCancelAnimationFrame?A=msCancelAnimationFrame:void 0===A&&(A=function(e){clearTimeout(e)}));var c=window.AudioContext;void 0===c&&("undefined"!=typeof webkitAudioContext&&(c=webkitAudioContext),"undefined"!=typeof mozAudioContext&&(c=mozAudioContext));var l=window.URL;void 0===l&&"undefined"!=typeof webkitURL&&(l=webkitURL),"undefined"!=typeof navigator&&void 0===navigator.getUserMedia&&(void 0!==navigator.webkitGetUserMedia&&(navigator.getUserMedia=navigator.webkitGetUserMedia),void 0!==navigator.mozGetUserMedia&&(navigator.getUserMedia=navigator.mozGetUserMedia));var d=!(-1===navigator.userAgent.indexOf("Edge")||!navigator.msSaveBlob&&!navigator.msSaveOrOpenBlob),u=!!window.opera||-1!==navigator.userAgent.indexOf("OPR/"),h=navigator.userAgent.toLowerCase().indexOf("firefox")>-1&&"netscape"in window&&/ rv:/.test(navigator.userAgent),p=!u&&!d&&!!navigator.webkitGetUserMedia||y()||-1!==navigator.userAgent.toLowerCase().indexOf("chrome/"),m=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);m&&!p&&-1!==navigator.userAgent.indexOf("CriOS")&&(m=!1,p=!0);var f=window.MediaStream;function g(e){if(0===e)return"0 Bytes";var t=parseInt(Math.floor(Math.log(e)/Math.log(1e3)),10);return(e/Math.pow(1e3,t)).toPrecision(3)+" "+["Bytes","KB","MB","GB","TB"][t]}function b(e,t){if(!e)throw"Blob object is required.";if(!e.type)try{e.type="video/webm"}catch(e){}var i=(e.type||"video/webm").split("/")[1];if(-1!==i.indexOf(";")&&(i=i.split(";")[0]),t&&-1!==t.indexOf(".")){var o=t.split(".");t=o[0],i=o[1]}var s=(t||Math.round(9999999999*Math.random())+888888888)+"."+i;if(void 0!==navigator.msSaveOrOpenBlob)return navigator.msSaveOrOpenBlob(e,s);if(void 0!==navigator.msSaveBlob)return navigator.msSaveBlob(e,s);var r=document.createElement("a");r.href=l.createObjectURL(e),r.download=s,r.style="display:none;opacity:0;color:transparent;",(document.body||document.documentElement).appendChild(r),"function"==typeof r.click?r.click():(r.target="_blank",r.dispatchEvent(new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0}))),l.revokeObjectURL(r.href)}function y(){return"undefined"!=typeof window&&"object"==typeof window.process&&"renderer"===window.process.type||(!("undefined"==typeof process||"object"!=typeof process.versions||!process.versions.electron)||"object"==typeof navigator&&"string"==typeof navigator.userAgent&&navigator.userAgent.indexOf("Electron")>=0)}function v(e,t){return e&&e.getTracks?e.getTracks().filter((function(e){return e.kind===(t||"audio")})):[]}function w(e,t){"srcObject"in t?t.srcObject=e:"mozSrcObject"in t?t.mozSrcObject=e:t.srcObject=e}void 0===f&&"undefined"!=typeof webkitMediaStream&&(f=webkitMediaStream),void 0!==f&&void 0===f.prototype.stop&&(f.prototype.stop=function(){this.getTracks().forEach((function(e){e.stop()}))}),void 0!==t&&(t.invokeSaveAsDialog=b,t.getTracks=v,t.getSeekableBlob=function(e,t){if("undefined"==typeof EBML)throw new Error("Please link: https://www.webrtc-experiment.com/EBML.js");var i=new EBML.Reader,o=new EBML.Decoder,s=EBML.tools,r=new FileReader;r.onload=function(e){o.decode(this.result).forEach((function(e){i.read(e)})),i.stop();var r=s.makeMetadataSeekable(i.metadatas,i.duration,i.cues),a=this.result.slice(i.metadataSize),n=new Blob([r,a],{type:"video/webm"});t(n)},r.readAsArrayBuffer(e)},t.bytesToSize=g,t.isElectron=y);var k={};function S(){if(h||m||d)return!0;var e,t,i=navigator.userAgent,o=""+parseFloat(navigator.appVersion),s=parseInt(navigator.appVersion,10);return(p||u)&&(e=i.indexOf("Chrome"),o=i.substring(e+7)),-1!==(t=o.indexOf(";"))&&(o=o.substring(0,t)),-1!==(t=o.indexOf(" "))&&(o=o.substring(0,t)),s=parseInt(""+o,10),isNaN(s)&&(o=""+parseFloat(navigator.appVersion),s=parseInt(navigator.appVersion,10)),s>=49}function B(e,t){var i=this;if(void 0===e)throw'First argument "MediaStream" is required.';if("undefined"==typeof MediaRecorder)throw"Your browser does not support the Media Recorder API. Please try other modules e.g. WhammyRecorder or StereoAudioRecorder.";if("audio"===(t=t||{mimeType:"video/webm"}).type){var o;if(v(e,"video").length&&v(e,"audio").length)navigator.mozGetUserMedia?(o=new f).addTrack(v(e,"audio")[0]):o=new f(v(e,"audio")),e=o;t.mimeType&&-1!==t.mimeType.toString().toLowerCase().indexOf("audio")||(t.mimeType=p?"audio/webm":"audio/ogg"),t.mimeType&&"audio/ogg"!==t.mimeType.toString().toLowerCase()&&navigator.mozGetUserMedia&&(t.mimeType="audio/ogg")}var s,r=[];function a(){i.timestamps.push((new Date).getTime()),"function"==typeof t.onTimeStamp&&t.onTimeStamp(i.timestamps[i.timestamps.length-1],i.timestamps)}function n(e){return s&&s.mimeType?s.mimeType:e.mimeType||"video/webm"}function A(){r=[],s=null,i.timestamps=[]}this.getArrayOfBlobs=function(){return r},this.record=function(){i.blob=null,i.clearRecordedData(),i.timestamps=[],c=[],r=[];var o=t;t.disableLogs||console.log("Passing following config over MediaRecorder API.",o),s&&(s=null),p&&!S()&&(o="video/vp8"),"function"==typeof MediaRecorder.isTypeSupported&&o.mimeType&&(MediaRecorder.isTypeSupported(o.mimeType)||(t.disableLogs||console.warn("MediaRecorder API seems unable to record mimeType:",o.mimeType),o.mimeType="audio"===t.type?"audio/webm":"video/webm"));try{s=new MediaRecorder(e,o),t.mimeType=o.mimeType}catch(t){s=new MediaRecorder(e)}o.mimeType&&!MediaRecorder.isTypeSupported&&"canRecordMimeType"in s&&!1===s.canRecordMimeType(o.mimeType)&&(t.disableLogs||console.warn("MediaRecorder API seems unable to record mimeType:",o.mimeType)),s.ondataavailable=function(e){if(e.data&&c.push("ondataavailable: "+g(e.data.size)),"number"!=typeof t.timeSlice)!e.data||!e.data.size||e.data.size<100||i.blob?i.recordingCallback&&(i.recordingCallback(new Blob([],{type:n(o)})),i.recordingCallback=null):(i.blob=t.getNativeBlob?e.data:new Blob([e.data],{type:n(o)}),i.recordingCallback&&(i.recordingCallback(i.blob),i.recordingCallback=null));else if(e.data&&e.data.size&&(r.push(e.data),a(),"function"==typeof t.ondataavailable)){var s=t.getNativeBlob?e.data:new Blob([e.data],{type:n(o)});t.ondataavailable(s)}},s.onstart=function(){c.push("started")},s.onpause=function(){c.push("paused")},s.onresume=function(){c.push("resumed")},s.onstop=function(){c.push("stopped")},s.onerror=function(e){e&&(e.name||(e.name="UnknownError"),c.push("error: "+e),t.disableLogs||(-1!==e.name.toString().toLowerCase().indexOf("invalidstate")?console.error("The MediaRecorder is not in a state in which the proposed operation is allowed to be executed.",e):-1!==e.name.toString().toLowerCase().indexOf("notsupported")?console.error("MIME type (",o.mimeType,") is not supported.",e):-1!==e.name.toString().toLowerCase().indexOf("security")?console.error("MediaRecorder security error",e):"OutOfMemory"===e.name?console.error("The UA has exhaused the available memory. User agents SHOULD provide as much additional information as possible in the message attribute.",e):"IllegalStreamModification"===e.name?console.error("A modification to the stream has occurred that makes it impossible to continue recording. An example would be the addition of a Track while recording is occurring. User agents SHOULD provide as much additional information as possible in the message attribute.",e):"OtherRecordingError"===e.name?console.error("Used for an fatal error other than those listed above. User agents SHOULD provide as much additional information as possible in the message attribute.",e):"GenericError"===e.name?console.error("The UA cannot provide the codec or recording option that has been requested.",e):console.error("MediaRecorder Error",e)),function(e){if(!i.manuallyStopped&&s&&"inactive"===s.state)return delete t.timeslice,void s.start(6e5);setTimeout(void 0,1e3)}(),"inactive"!==s.state&&"stopped"!==s.state&&s.stop())},"number"==typeof t.timeSlice?(a(),s.start(t.timeSlice)):s.start(36e5),t.initCallback&&t.initCallback()},this.timestamps=[],this.stop=function(e){e=e||function(){},i.manuallyStopped=!0,s&&(this.recordingCallback=e,"recording"===s.state&&s.stop(),"number"==typeof t.timeSlice&&setTimeout((function(){i.blob=new Blob(r,{type:n(t)}),i.recordingCallback(i.blob)}),100))},this.pause=function(){s&&"recording"===s.state&&s.pause()},this.resume=function(){s&&"paused"===s.state&&s.resume()},this.clearRecordedData=function(){s&&"recording"===s.state&&i.stop(A),A()},this.getInternalRecorder=function(){return s},this.blob=null,this.getState=function(){return s&&s.state||"inactive"};var c=[];this.getAllStates=function(){return c},void 0===t.checkForInactiveTracks&&(t.checkForInactiveTracks=!1);i=this;!function o(){if(s&&!1!==t.checkForInactiveTracks)return!1===function(){if("active"in e){if(!e.active)return!1}else if("ended"in e&&e.ended)return!1;return!0}()?(t.disableLogs||console.log("MediaStream seems stopped."),void i.stop()):void setTimeout(o,1e3)}(),this.name="MediaStreamRecorder",this.toString=function(){return this.name}}function E(e,i){if(!v(e,"audio").length)throw"Your stream has no audio tracks.";var o,s=this,r=[],a=[],n=!1,A=0,c=2,d=(i=i||{}).desiredSampRate;function u(){if(!1===i.checkForInactiveTracks)return!0;if("active"in e){if(!e.active)return!1}else if("ended"in e&&e.ended)return!1;return!0}function h(e,t){function i(e,t){var i,o=e.numberOfAudioChannels,s=e.leftBuffers.slice(0),r=e.rightBuffers.slice(0),a=e.sampleRate,n=e.internalInterleavedLength,A=e.desiredSampRate;function c(e,t,i){var o=Math.round(e.length*(t/i)),s=[],r=Number((e.length-1)/(o-1));s[0]=e[0];for(var a=1;a<o-1;a++){var n=a*r,A=Number(Math.floor(n)).toFixed(),c=Number(Math.ceil(n)).toFixed(),d=n-A;s[a]=l(e[A],e[c],d)}return s[o-1]=e[e.length-1],s}function l(e,t,i){return e+(t-e)*i}function d(e,t){for(var i=new Float64Array(t),o=0,s=e.length,r=0;r<s;r++){var a=e[r];i.set(a,o),o+=a.length}return i}function u(e,t,i){for(var o=i.length,s=0;s<o;s++)e.setUint8(t+s,i.charCodeAt(s))}2===o&&(s=d(s,n),r=d(r,n),A&&(s=c(s,A,a),r=c(r,A,a))),1===o&&(s=d(s,n),A&&(s=c(s,A,a))),A&&(a=A),2===o&&(i=function(e,t){for(var i=e.length+t.length,o=new Float64Array(i),s=0,r=0;r<i;)o[r++]=e[s],o[r++]=t[s],s++;return o}(s,r)),1===o&&(i=s);var h=i.length,p=new ArrayBuffer(44+2*h),m=new DataView(p);u(m,0,"RIFF"),m.setUint32(4,36+2*h,!0),u(m,8,"WAVE"),u(m,12,"fmt "),m.setUint32(16,16,!0),m.setUint16(20,1,!0),m.setUint16(22,o,!0),m.setUint32(24,a,!0),m.setUint32(28,a*o*2,!0),m.setUint16(32,2*o,!0),m.setUint16(34,16,!0),u(m,36,"data"),m.setUint32(40,2*h,!0);for(var f=h,g=44,b=0;b<f;b++)m.setInt16(g,32767*i[b],!0),g+=2;if(t)return t({buffer:p,view:m});postMessage({buffer:p,view:m})}if(e.noWorker)i(e,(function(e){t(e.buffer,e.view)}));else{var o,s,r,a=(o=i,s=l.createObjectURL(new Blob([o.toString(),";this.onmessage =  function (eee) {"+o.name+"(eee.data);}"],{type:"application/javascript"})),(r=new Worker(s)).workerURL=s,r);a.onmessage=function(e){t(e.data.buffer,e.data.view),l.revokeObjectURL(a.workerURL),a.terminate()},a.postMessage(e)}}!0===i.leftChannel&&(c=1),1===i.numberOfAudioChannels&&(c=1),(!c||c<1)&&(c=2),i.disableLogs||console.log("StereoAudioRecorder is set to record number of channels: "+c),void 0===i.checkForInactiveTracks&&(i.checkForInactiveTracks=!0),this.record=function(){if(!1===u())throw"Please make sure MediaStream is active.";w(),S=y=!1,n=!0,void 0!==i.timeSlice&&E()},this.stop=function(e){e=e||function(){},n=!1,h({desiredSampRate:d,sampleRate:b,numberOfAudioChannels:c,internalInterleavedLength:A,leftBuffers:r,rightBuffers:1===c?[]:a,noWorker:i.noWorker},(function(t,i){s.blob=new Blob([i],{type:"audio/wav"}),s.buffer=new ArrayBuffer(i.buffer.byteLength),s.view=i,s.sampleRate=d||b,s.bufferSize=g,s.length=A,S=!1,e&&e(s.blob)}))},void 0===t.Storage&&(t.Storage={AudioContextConstructor:null,AudioContext:window.AudioContext||window.webkitAudioContext}),t.Storage.AudioContextConstructor&&"closed"!==t.Storage.AudioContextConstructor.state||(t.Storage.AudioContextConstructor=new t.Storage.AudioContext);var p=t.Storage.AudioContextConstructor,m=p.createMediaStreamSource(e),f=[0,256,512,1024,2048,4096,8192,16384],g=void 0===i.bufferSize?4096:i.bufferSize;if(-1===f.indexOf(g)&&(i.disableLogs||console.log("Legal values for buffer-size are "+JSON.stringify(f,null,"\t"))),p.createJavaScriptNode)o=p.createJavaScriptNode(g,c,c);else{if(!p.createScriptProcessor)throw"WebAudio API has no support on this browser.";o=p.createScriptProcessor(g,c,c)}m.connect(o),i.bufferSize||(g=o.bufferSize);var b=void 0!==i.sampleRate?i.sampleRate:p.sampleRate||44100;(b<22050||b>96e3)&&(i.disableLogs||console.log("sample-rate must be under range 22050 and 96000.")),i.disableLogs||i.desiredSampRate&&console.log("Desired sample-rate: "+i.desiredSampRate);var y=!1;function w(){r=[],a=[],A=0,S=!1,n=!1,y=!1,p=null,s.leftchannel=r,s.rightchannel=a,s.numberOfAudioChannels=c,s.desiredSampRate=d,s.sampleRate=b,s.recordingLength=A,B={left:[],right:[],recordingLength:0}}function k(){o&&(o.onaudioprocess=null,o.disconnect(),o=null),m&&(m.disconnect(),m=null),w()}this.pause=function(){y=!0},this.resume=function(){if(!1===u())throw"Please make sure MediaStream is active.";if(!n)return i.disableLogs||console.log("Seems recording has been restarted."),void this.record();y=!1},this.clearRecordedData=function(){i.checkForInactiveTracks=!1,n&&this.stop(k),k()},this.name="StereoAudioRecorder",this.toString=function(){return this.name};var S=!1;o.onaudioprocess=function(e){if(!y)if(!1===u()&&(i.disableLogs||console.log("MediaStream seems stopped."),o.disconnect(),n=!1),n){S||(S=!0,i.onAudioProcessStarted&&i.onAudioProcessStarted(),i.initCallback&&i.initCallback());var t=e.inputBuffer.getChannelData(0),l=new Float32Array(t);if(r.push(l),2===c){var d=e.inputBuffer.getChannelData(1),h=new Float32Array(d);a.push(h)}A+=g,s.recordingLength=A,void 0!==i.timeSlice&&(B.recordingLength+=g,B.left.push(l),2===c&&B.right.push(h))}else m&&(m.disconnect(),m=null)},p.createMediaStreamDestination?o.connect(p.createMediaStreamDestination()):o.connect(p.destination),this.leftchannel=r,this.rightchannel=a,this.numberOfAudioChannels=c,this.desiredSampRate=d,this.sampleRate=b,s.recordingLength=A;var B={left:[],right:[],recordingLength:0};function E(){n&&"function"==typeof i.ondataavailable&&void 0!==i.timeSlice&&(B.left.length?(h({desiredSampRate:d,sampleRate:b,numberOfAudioChannels:c,internalInterleavedLength:B.recordingLength,leftBuffers:B.left,rightBuffers:1===c?[]:B.right},(function(e,t){var o=new Blob([t],{type:"audio/wav"});i.ondataavailable(o),setTimeout(E,i.timeSlice)})),B={left:[],right:[],recordingLength:0}):setTimeout(E,i.timeSlice))}}function C(e,t){if("undefined"==typeof html2canvas)throw"Please link: https://www.webrtc-experiment.com/screenshot.js";(t=t||{}).frameInterval||(t.frameInterval=10);var i=!1;["captureStream","mozCaptureStream","webkitCaptureStream"].forEach((function(e){e in document.createElement("canvas")&&(i=!0)}));var o,s,r,a=!(!window.webkitRTCPeerConnection&&!window.webkitGetUserMedia||!window.chrome),n=50,A=navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./);if(a&&A&&A[2]&&(n=parseInt(A[2],10)),a&&n<52&&(i=!1),t.useWhammyRecorder&&(i=!1),i)if(t.disableLogs||console.log("Your browser supports both MediRecorder API and canvas.captureStream!"),e instanceof HTMLCanvasElement)o=e;else{if(!(e instanceof CanvasRenderingContext2D))throw"Please pass either HTMLCanvasElement or CanvasRenderingContext2D.";o=e.canvas}else navigator.mozGetUserMedia&&(t.disableLogs||console.error("Canvas recording is NOT supported in Firefox."));this.record=function(){if(r=!0,i&&!t.useWhammyRecorder){var e;"captureStream"in o?e=o.captureStream(25):"mozCaptureStream"in o?e=o.mozCaptureStream(25):"webkitCaptureStream"in o&&(e=o.webkitCaptureStream(25));try{var a=new f;a.addTrack(v(e,"video")[0]),e=a}catch(e){}if(!e)throw"captureStream API are NOT available.";(s=new B(e,{mimeType:t.mimeType||"video/webm"})).record()}else h.frames=[],u=(new Date).getTime(),d();t.initCallback&&t.initCallback()},this.getWebPImages=function(i){if("canvas"===e.nodeName.toLowerCase()){var o=h.frames.length;h.frames.forEach((function(e,i){var s=o-i;t.disableLogs||console.log(s+"/"+o+" frames remaining"),t.onEncodingCallback&&t.onEncodingCallback(s,o);var r=e.image.toDataURL("image/webp",1);h.frames[i].image=r})),t.disableLogs||console.log("Generating WebM"),i()}else i()},this.stop=function(e){r=!1;var o=this;i&&s?s.stop(e):this.getWebPImages((function(){h.compile((function(i){t.disableLogs||console.log("Recording finished!"),o.blob=i,o.blob.forEach&&(o.blob=new Blob([],{type:"video/webm"})),e&&e(o.blob),h.frames=[]}))}))};var c=!1;function l(){h.frames=[],r=!1,c=!1}function d(){if(c)return u=(new Date).getTime(),setTimeout(d,500);if("canvas"===e.nodeName.toLowerCase()){var i=(new Date).getTime()-u;return u=(new Date).getTime(),h.frames.push({image:(o=document.createElement("canvas"),s=o.getContext("2d"),o.width=e.width,o.height=e.height,s.drawImage(e,0,0),o),duration:i}),void(r&&setTimeout(d,t.frameInterval))}var o,s;html2canvas(e,{grabMouse:void 0===t.showMousePointer||t.showMousePointer,onrendered:function(e){var i=(new Date).getTime()-u;if(!i)return setTimeout(d,t.frameInterval);u=(new Date).getTime(),h.frames.push({image:e.toDataURL("image/webp",1),duration:i}),r&&setTimeout(d,t.frameInterval)}})}this.pause=function(){c=!0,s instanceof B&&s.pause()},this.resume=function(){c=!1,s instanceof B?s.resume():r||this.record()},this.clearRecordedData=function(){r&&this.stop(l),l()},this.name="CanvasRecorder",this.toString=function(){return this.name};var u=(new Date).getTime(),h=new T.Video(100)}function R(e,t){function i(e){e=void 0!==e?e:10;var t=(new Date).getTime()-A;return t?r?(A=(new Date).getTime(),setTimeout(i,100)):(A=(new Date).getTime(),n.paused&&n.play(),d.drawImage(n,0,0,l.width,l.height),c.frames.push({duration:t,image:l.toDataURL("image/webp")}),void(s||setTimeout(i,e,e))):setTimeout(i,e,e)}function o(e,t,i,o,s){var r=document.createElement("canvas");r.width=l.width,r.height=l.height;var a,n,A,c=r.getContext("2d"),d=[],u=-1===t,h=t&&t>0&&t<=e.length?t:e.length,p=0,m=0,f=0,g=Math.sqrt(Math.pow(255,2)+Math.pow(255,2)+Math.pow(255,2)),b=i&&i>=0&&i<=1?i:0,y=o&&o>=0&&o<=1?o:0,v=!1;n=-1,A=(a={length:h,functionToLoop:function(t,i){var o,s,r,a=function(){!v&&r-o<=r*y||(u&&(v=!0),d.push(e[i])),t()};if(v)a();else{var n=new Image;n.onload=function(){c.drawImage(n,0,0,l.width,l.height);var e=c.getImageData(0,0,l.width,l.height);o=0,s=e.data.length,r=e.data.length/4;for(var t=0;t<s;t+=4){var i={r:e.data[t],g:e.data[t+1],b:e.data[t+2]};Math.sqrt(Math.pow(i.r-p,2)+Math.pow(i.g-m,2)+Math.pow(i.b-f,2))<=g*b&&o++}a()},n.src=e[i].image}},callback:function(){(d=d.concat(e.slice(h))).length<=0&&d.push(e[e.length-1]),s(d)}}).length,function e(){++n!==A?setTimeout((function(){a.functionToLoop(e,n)}),1):a.callback()}()}(t=t||{}).frameInterval||(t.frameInterval=10),t.disableLogs||console.log("Using frames-interval:",t.frameInterval),this.record=function(){t.width||(t.width=320),t.height||(t.height=240),t.video||(t.video={width:t.width,height:t.height}),t.canvas||(t.canvas={width:t.width,height:t.height}),l.width=t.canvas.width||320,l.height=t.canvas.height||240,d=l.getContext("2d"),t.video&&t.video instanceof HTMLVideoElement?(n=t.video.cloneNode(),t.initCallback&&t.initCallback()):(n=document.createElement("video"),w(e,n),n.onloadedmetadata=function(){t.initCallback&&t.initCallback()},n.width=t.video.width,n.height=t.video.height),n.muted=!0,n.play(),A=(new Date).getTime(),c=new T.Video,t.disableLogs||(console.log("canvas resolutions",l.width,"*",l.height),console.log("video width/height",n.width||l.width,"*",n.height||l.height)),i(t.frameInterval)};var s=!1;this.stop=function(e){e=e||function(){},s=!0;var i=this;setTimeout((function(){o(c.frames,-1,null,null,(function(o){c.frames=o,t.advertisement&&t.advertisement.length&&(c.frames=t.advertisement.concat(c.frames)),c.compile((function(t){i.blob=t,i.blob.forEach&&(i.blob=new Blob([],{type:"video/webm"})),e&&e(i.blob)}))}))}),10)};var r=!1;function a(){c.frames=[],s=!0,r=!1}this.pause=function(){r=!0},this.resume=function(){r=!1,s&&this.record()},this.clearRecordedData=function(){s||this.stop(a),a()},this.name="WhammyRecorder",this.toString=function(){return this.name};var n,A,c,l=document.createElement("canvas"),d=l.getContext("2d")}void 0!==c?k.AudioContext=c:"undefined"!=typeof webkitAudioContext&&(k.AudioContext=webkitAudioContext),void 0!==t&&(t.Storage=k),void 0!==t&&(t.MediaStreamRecorder=B),void 0!==t&&(t.StereoAudioRecorder=E),void 0!==t&&(t.CanvasRecorder=C),void 0!==t&&(t.WhammyRecorder=R);var T=function(){function e(e){this.frames=[],this.duration=e||1,this.quality=.8}function t(e){function t(e,t,i){return[{data:e,id:231}].concat(i.map((function(e){var i=function(e){var t=0;e.keyframe&&(t|=128);e.invisible&&(t|=8);e.lacing&&(t|=e.lacing<<1);e.discardable&&(t|=1);if(e.trackNum>127)throw"TrackNumber > 127 not supported";return[128|e.trackNum,e.timecode>>8,255&e.timecode,t].map((function(e){return String.fromCharCode(e)})).join("")+e.frame}({discardable:0,frame:e.data.slice(4),invisible:0,keyframe:1,lacing:0,trackNum:1,timecode:Math.round(t)});return t+=e.duration,{data:i,id:163}})))}function i(e){for(var t=[];e>0;)t.push(255&e),e>>=8;return new Uint8Array(t.reverse())}function o(e){var t=[];e=(e.length%8?new Array(9-e.length%8).join("0"):"")+e;for(var i=0;i<e.length;i+=8)t.push(parseInt(e.substr(i,8),2));return new Uint8Array(t)}function s(e){for(var t=[],r=0;r<e.length;r++){var a=e[r].data;"object"==typeof a&&(a=s(a)),"number"==typeof a&&(a=o(a.toString(2))),"string"==typeof a&&(a=new Uint8Array(a.split("").map((function(e){return e.charCodeAt(0)}))));var n=a.size||a.byteLength||a.length,A=Math.ceil(Math.ceil(Math.log(n)/Math.log(2))/8),c=n.toString(2),l=new Array(7*A+7+1-c.length).join("0")+c,d=new Array(A).join("0")+"1"+l;t.push(i(e[r].id)),t.push(o(d)),t.push(a)}return new Blob(t,{type:"video/webm"})}function r(e,t){return parseInt(e.substr(t+4,4).split("").map((function(e){var t=e.charCodeAt(0).toString(2);return new Array(8-t.length+1).join("0")+t})).join(""),2)}function a(e){for(var t=0,i={};t<e.length;){var o=e.substr(t,4),s=r(e,t),n=e.substr(t+4+4,s);t+=8+s,i[o]=i[o]||[],"RIFF"===o||"LIST"===o?i[o].push(a(n)):i[o].push(n)}return i}var n=new function(e){var i=function(e){if(!e[0])return void postMessage({error:"Something went wrong. Maybe WebP format is not supported in the current browser."});for(var t=e[0].width,i=e[0].height,o=e[0].duration,s=1;s<e.length;s++)o+=e[s].duration;return{duration:o,width:t,height:i}}(e);if(!i)return[];for(var o,r=[{id:440786851,data:[{data:1,id:17030},{data:1,id:17143},{data:4,id:17138},{data:8,id:17139},{data:"webm",id:17026},{data:2,id:17031},{data:2,id:17029}]},{id:408125543,data:[{id:357149030,data:[{data:1e6,id:2807729},{data:"whammy",id:19840},{data:"whammy",id:22337},{data:(o=i.duration,[].slice.call(new Uint8Array(new Float64Array([o]).buffer),0).map((function(e){return String.fromCharCode(e)})).reverse().join("")),id:17545}]},{id:374648427,data:[{id:174,data:[{data:1,id:215},{data:1,id:29637},{data:0,id:156},{data:"und",id:2274716},{data:"V_VP8",id:134},{data:"VP8",id:2459272},{data:1,id:131},{id:224,data:[{data:i.width,id:176},{data:i.height,id:186}]}]}]}]}],a=0,n=0;a<e.length;){var A=[],c=0;do{A.push(e[a]),c+=e[a].duration,a++}while(a<e.length&&c<3e4);var l={id:524531317,data:t(n,0,A)};r[1].data.push(l),n+=c}return s(r)}(e.map((function(e){var t=function(e){for(var t=e.RIFF[0].WEBP[0],i=t.indexOf("*"),o=0,s=[];o<4;o++)s[o]=t.charCodeAt(i+3+o);return{width:16383&(s[1]<<8|s[0]),height:16383&(s[3]<<8|s[2]),data:t,riff:e}}(a(atob(e.image.slice(23))));return t.duration=e.duration,t})));postMessage(n)}return e.prototype.add=function(e,t){if("canvas"in e&&(e=e.canvas),"toDataURL"in e&&(e=e.toDataURL("image/webp",this.quality)),!/^data:image\/webp;base64,/gi.test(e))throw"Input must be formatted properly as a base64 encoded DataURI of type image/webp";this.frames.push({image:e,duration:t||this.duration})},e.prototype.compile=function(e){var i,o,s,r=(i=t,o=l.createObjectURL(new Blob([i.toString(),"this.onmessage =  function (eee) {"+i.name+"(eee.data);}"],{type:"application/javascript"})),s=new Worker(o),l.revokeObjectURL(o),s);r.onmessage=function(t){t.data.error?console.error(t.data.error):e(t.data)},r.postMessage(this.frames)},{Video:e}}();void 0!==t&&(t.Whammy=T);var I={init:function(){var e=this;if("undefined"!=typeof indexedDB&&void 0!==indexedDB.open){var t,i=this.dbName||location.href.replace(/\/|:|#|%|\.|\[|\]/g,""),o=indexedDB.open(i,1);o.onerror=e.onError,o.onsuccess=function(){((t=o.result).onerror=e.onError,t.setVersion)?1!==t.version?t.setVersion(1).onsuccess=function(){s(t),r()}:r():r()},o.onupgradeneeded=function(e){s(e.target.result)}}else console.error("IndexedDB API are not available in this browser.");function s(t){t.createObjectStore(e.dataStoreName)}function r(){var i=t.transaction([e.dataStoreName],"readwrite");function o(t){i.objectStore(e.dataStoreName).get(t).onsuccess=function(i){e.callback&&e.callback(i.target.result,t)}}e.videoBlob&&i.objectStore(e.dataStoreName).put(e.videoBlob,"videoBlob"),e.gifBlob&&i.objectStore(e.dataStoreName).put(e.gifBlob,"gifBlob"),e.audioBlob&&i.objectStore(e.dataStoreName).put(e.audioBlob,"audioBlob"),o("audioBlob"),o("videoBlob"),o("gifBlob")}},Fetch:function(e){return this.callback=e,this.init(),this},Store:function(e){return this.audioBlob=e.audioBlob,this.videoBlob=e.videoBlob,this.gifBlob=e.gifBlob,this.init(),this},onError:function(e){console.error(JSON.stringify(e,null,"\t"))},dataStoreName:"recordRTC",dbName:null};function j(e,t){if("undefined"==typeof GIFEncoder){var i=document.createElement("script");i.src="https://www.webrtc-experiment.com/gif-recorder.js",(document.body||document.documentElement).appendChild(i)}t=t||{};var o=e instanceof CanvasRenderingContext2D||e instanceof HTMLCanvasElement;this.record=function(){"undefined"!=typeof GIFEncoder&&c?(o||(t.width||(t.width=l.offsetWidth||320),t.height||(t.height=l.offsetHeight||240),t.video||(t.video={width:t.width,height:t.height}),t.canvas||(t.canvas={width:t.width,height:t.height}),r.width=t.canvas.width||320,r.height=t.canvas.height||240,l.width=t.video.width||320,l.height=t.video.height||240),(u=new GIFEncoder).setRepeat(0),u.setDelay(t.frameRate||200),u.setQuality(t.quality||10),u.start(),"function"==typeof t.onGifRecordingStarted&&t.onGifRecordingStarted(),h=a((function e(i){if(!0!==p.clearedRecordedData){if(s)return setTimeout((function(){e(i)}),100);h=a(e),void 0===typeof d&&(d=i),i-d<90||(!o&&l.paused&&l.play(),o||n.drawImage(l,0,0,r.width,r.height),t.onGifPreview&&t.onGifPreview(r.toDataURL("image/png")),u.addFrame(n),d=i)}})),t.initCallback&&t.initCallback()):setTimeout(p.record,1e3)},this.stop=function(e){e=e||function(){},h&&A(h),this.blob=new Blob([new Uint8Array(u.stream().bin)],{type:"image/gif"}),e(this.blob),u.stream().bin=[]};var s=!1;this.pause=function(){s=!0},this.resume=function(){s=!1},this.clearRecordedData=function(){p.clearedRecordedData=!0,u&&(u.stream().bin=[])},this.name="GifRecorder",this.toString=function(){return this.name};var r=document.createElement("canvas"),n=r.getContext("2d");o&&(e instanceof CanvasRenderingContext2D?r=(n=e).canvas:e instanceof HTMLCanvasElement&&(n=e.getContext("2d"),r=e));var c=!0;if(!o){var l=document.createElement("video");l.muted=!0,l.autoplay=!0,l.playsInline=!0,c=!1,l.onloadedmetadata=function(){c=!0},w(e,l),l.play()}var d,u,h=null,p=this}function x(e,i){!function(e){void 0===t&&e&&"undefined"==typeof window&&void 0!==ue&&(ue.navigator={userAgent:"Fake/5.0 (FakeOS) AppleWebKit/123 (KHTML, like Gecko) Fake/12.3.4567.89 Fake/123.45",getUserMedia:function(){}},ue.console||(ue.console={}),void 0!==ue.console.log&&void 0!==ue.console.error||(ue.console.error=ue.console.log=ue.console.log||function(){console.log(arguments)}),"undefined"==typeof document&&(e.document={documentElement:{appendChild:function(){return""}}},document.createElement=document.captureStream=document.mozCaptureStream=function(){var e={getContext:function(){return e},play:function(){},pause:function(){},drawImage:function(){},toDataURL:function(){return""},style:{}};return e},e.HTMLVideoElement=function(){}),"undefined"==typeof location&&(e.location={protocol:"file:",href:"",hash:""}),"undefined"==typeof screen&&(e.screen={width:0,height:0}),void 0===c&&(e.URL={createObjectURL:function(){return""},revokeObjectURL:function(){return""}}),e.window=ue)}(void 0!==ue?ue:null),i=i||"multi-streams-mixer";var o=[],s=!1,r=document.createElement("canvas"),a=r.getContext("2d");r.style.opacity=0,r.style.position="absolute",r.style.zIndex=-1,r.style.top="-1000em",r.style.left="-1000em",r.className=i,(document.body||document.documentElement).appendChild(r),this.disableLogs=!1,this.frameInterval=10,this.width=360,this.height=240,this.useGainNode=!0;var n=this,A=window.AudioContext;void 0===A&&("undefined"!=typeof webkitAudioContext&&(A=webkitAudioContext),"undefined"!=typeof mozAudioContext&&(A=mozAudioContext));var c=window.URL;void 0===c&&"undefined"!=typeof webkitURL&&(c=webkitURL),"undefined"!=typeof navigator&&void 0===navigator.getUserMedia&&(void 0!==navigator.webkitGetUserMedia&&(navigator.getUserMedia=navigator.webkitGetUserMedia),void 0!==navigator.mozGetUserMedia&&(navigator.getUserMedia=navigator.mozGetUserMedia));var l=window.MediaStream;void 0===l&&"undefined"!=typeof webkitMediaStream&&(l=webkitMediaStream),void 0!==l&&void 0===l.prototype.stop&&(l.prototype.stop=function(){this.getTracks().forEach((function(e){e.stop()}))});var d={};function u(){if(!s){var e=o.length,t=!1,i=[];if(o.forEach((function(e){e.stream||(e.stream={}),e.stream.fullcanvas?t=e:i.push(e)})),t)r.width=t.stream.width,r.height=t.stream.height;else if(i.length){r.width=e>1?2*i[0].width:i[0].width;var a=1;3!==e&&4!==e||(a=2),5!==e&&6!==e||(a=3),7!==e&&8!==e||(a=4),9!==e&&10!==e||(a=5),r.height=i[0].height*a}else r.width=n.width||360,r.height=n.height||240;t&&t instanceof HTMLVideoElement&&h(t),i.forEach((function(e,t){h(e,t)})),setTimeout(u,n.frameInterval)}}function h(e,t){if(!s){var i=0,o=0,r=e.width,n=e.height;1===t&&(i=e.width),2===t&&(o=e.height),3===t&&(i=e.width,o=e.height),4===t&&(o=2*e.height),5===t&&(i=e.width,o=2*e.height),6===t&&(o=3*e.height),7===t&&(i=e.width,o=3*e.height),void 0!==e.stream.left&&(i=e.stream.left),void 0!==e.stream.top&&(o=e.stream.top),void 0!==e.stream.width&&(r=e.stream.width),void 0!==e.stream.height&&(n=e.stream.height),a.drawImage(e,i,o,r,n),"function"==typeof e.stream.onRender&&e.stream.onRender(a,i,o,r,n,t)}}function p(e){var t=document.createElement("video");return function(e,t){"srcObject"in t?t.srcObject=e:"mozSrcObject"in t?t.mozSrcObject=e:t.srcObject=e}(e,t),t.className=i,t.muted=!0,t.volume=0,t.width=e.width||n.width||360,t.height=e.height||n.height||240,t.play(),t}function m(t){o=[],(t=t||e).forEach((function(e){if(e.getTracks().filter((function(e){return"video"===e.kind})).length){var t=p(e);t.stream=e,o.push(t)}}))}void 0!==A?d.AudioContext=A:"undefined"!=typeof webkitAudioContext&&(d.AudioContext=webkitAudioContext),this.startDrawingFrames=function(){u()},this.appendStreams=function(t){if(!t)throw"First parameter is required.";t instanceof Array||(t=[t]),t.forEach((function(t){var i=new l;if(t.getTracks().filter((function(e){return"video"===e.kind})).length){var s=p(t);s.stream=t,o.push(s),i.addTrack(t.getTracks().filter((function(e){return"video"===e.kind}))[0])}if(t.getTracks().filter((function(e){return"audio"===e.kind})).length){var r=n.audioContext.createMediaStreamSource(t);n.audioDestination=n.audioContext.createMediaStreamDestination(),r.connect(n.audioDestination),i.addTrack(n.audioDestination.stream.getTracks().filter((function(e){return"audio"===e.kind}))[0])}e.push(i)}))},this.releaseStreams=function(){o=[],s=!0,n.gainNode&&(n.gainNode.disconnect(),n.gainNode=null),n.audioSources.length&&(n.audioSources.forEach((function(e){e.disconnect()})),n.audioSources=[]),n.audioDestination&&(n.audioDestination.disconnect(),n.audioDestination=null),n.audioContext&&n.audioContext.close(),n.audioContext=null,a.clearRect(0,0,r.width,r.height),r.stream&&(r.stream.stop(),r.stream=null)},this.resetVideoStreams=function(e){!e||e instanceof Array||(e=[e]),m(e)},this.name="MultiStreamsMixer",this.toString=function(){return this.name},this.getMixedStream=function(){s=!1;var t=function(){var e;m(),"captureStream"in r?e=r.captureStream():"mozCaptureStream"in r?e=r.mozCaptureStream():n.disableLogs||console.error("Upgrade to latest Chrome or otherwise enable this flag: chrome://flags/#enable-experimental-web-platform-features");var t=new l;return e.getTracks().filter((function(e){return"video"===e.kind})).forEach((function(e){t.addTrack(e)})),r.stream=t,t}(),i=function(){d.AudioContextConstructor||(d.AudioContextConstructor=new d.AudioContext);n.audioContext=d.AudioContextConstructor,n.audioSources=[],!0===n.useGainNode&&(n.gainNode=n.audioContext.createGain(),n.gainNode.connect(n.audioContext.destination),n.gainNode.gain.value=0);var t=0;if(e.forEach((function(e){if(e.getTracks().filter((function(e){return"audio"===e.kind})).length){t++;var i=n.audioContext.createMediaStreamSource(e);!0===n.useGainNode&&i.connect(n.gainNode),n.audioSources.push(i)}})),!t)return;return n.audioDestination=n.audioContext.createMediaStreamDestination(),n.audioSources.forEach((function(e){e.connect(n.audioDestination)})),n.audioDestination.stream}();return i&&i.getTracks().filter((function(e){return"audio"===e.kind})).forEach((function(e){t.addTrack(e)})),e.forEach((function(e){e.fullcanvas})),t}}function D(e,t){e=e||[];var i,o,s=this;(t=t||{elementClass:"multi-streams-mixer",mimeType:"video/webm",video:{width:360,height:240}}).frameInterval||(t.frameInterval=10),t.video||(t.video={}),t.video.width||(t.video.width=360),t.video.height||(t.video.height=240),this.record=function(){var s;i=new x(e,t.elementClass||"multi-streams-mixer"),(s=[],e.forEach((function(e){v(e,"video").forEach((function(e){s.push(e)}))})),s).length&&(i.frameInterval=t.frameInterval||10,i.width=t.video.width||360,i.height=t.video.height||240,i.startDrawingFrames()),t.previewStream&&"function"==typeof t.previewStream&&t.previewStream(i.getMixedStream()),(o=new B(i.getMixedStream(),t)).record()},this.stop=function(e){o&&o.stop((function(t){s.blob=t,e(t),s.clearRecordedData()}))},this.pause=function(){o&&o.pause()},this.resume=function(){o&&o.resume()},this.clearRecordedData=function(){o&&(o.clearRecordedData(),o=null),i&&(i.releaseStreams(),i=null)},this.addStreams=function(s){if(!s)throw"First parameter is required.";s instanceof Array||(s=[s]),e.concat(s),o&&i&&(i.appendStreams(s),t.previewStream&&"function"==typeof t.previewStream&&t.previewStream(i.getMixedStream()))},this.resetVideoStreams=function(e){i&&(!e||e instanceof Array||(e=[e]),i.resetVideoStreams(e))},this.getMixer=function(){return i},this.name="MultiStreamRecorder",this.toString=function(){return this.name}}function L(e,t){var i,o,s;function r(){return new ReadableStream({start:function(o){var s=document.createElement("canvas"),r=document.createElement("video"),a=!0;r.srcObject=e,r.muted=!0,r.height=t.height,r.width=t.width,r.volume=0,r.onplaying=function(){s.width=t.width,s.height=t.height;var e=s.getContext("2d"),n=1e3/t.frameRate,A=setInterval((function(){if(i&&(clearInterval(A),o.close()),a&&(a=!1,t.onVideoProcessStarted&&t.onVideoProcessStarted()),e.drawImage(r,0,0),"closed"!==o._controlledReadableStream.state)try{o.enqueue(e.getImageData(0,0,t.width,t.height))}catch(e){}}),n)},r.play()}})}function a(e,A){if(!t.workerPath&&!A)return i=!1,void fetch("https://unpkg.com/webm-wasm@latest/dist/webm-worker.js").then((function(t){t.arrayBuffer().then((function(t){a(e,t)}))}));if(!t.workerPath&&A instanceof ArrayBuffer){var c=new Blob([A],{type:"text/javascript"});t.workerPath=l.createObjectURL(c)}t.workerPath||console.error("workerPath parameter is missing."),(o=new Worker(t.workerPath)).postMessage(t.webAssemblyPath||"https://unpkg.com/webm-wasm@latest/dist/webm-wasm.wasm"),o.addEventListener("message",(function(e){"READY"===e.data?(o.postMessage({width:t.width,height:t.height,bitrate:t.bitrate||1200,timebaseDen:t.frameRate||30,realtime:t.realtime}),r().pipeTo(new WritableStream({write:function(e){i?console.error("Got image, but recorder is finished!"):o.postMessage(e.data.buffer,[e.data.buffer])}}))):e.data&&(s||n.push(e.data))}))}"undefined"!=typeof ReadableStream&&"undefined"!=typeof WritableStream||console.error("Following polyfill is strongly recommended: https://unpkg.com/@mattiasbuelens/web-streams-polyfill/dist/polyfill.min.js"),(t=t||{}).width=t.width||640,t.height=t.height||480,t.frameRate=t.frameRate||30,t.bitrate=t.bitrate||1200,t.realtime=t.realtime||!0,this.record=function(){n=[],s=!1,this.blob=null,a(e),"function"==typeof t.initCallback&&t.initCallback()},this.pause=function(){s=!0},this.resume=function(){s=!1};var n=[];this.stop=function(e){i=!0;var t=this;!function(e){o?(o.addEventListener("message",(function(t){null===t.data&&(o.terminate(),o=null,e&&e())})),o.postMessage(null)):e&&e()}((function(){t.blob=new Blob(n,{type:"video/webm"}),e(t.blob)}))},this.name="WebAssemblyRecorder",this.toString=function(){return this.name},this.clearRecordedData=function(){n=[],s=!1,this.blob=null},this.blob=null}void 0!==t&&(t.DiskStorage=I),void 0!==t&&(t.GifRecorder=j),void 0===t&&(e.exports=x),void 0!==t&&(t.MultiStreamRecorder=D),void 0!==t&&(t.RecordRTCPromisesHandler=function(e,i){if(!this)throw'Use "new RecordRTCPromisesHandler()"';if(void 0===e)throw'First argument "MediaStream" is required.';var o=this;o.recordRTC=new t(e,i),this.startRecording=function(){return new Promise((function(e,t){try{o.recordRTC.startRecording(),e()}catch(e){t(e)}}))},this.stopRecording=function(){return new Promise((function(e,t){try{o.recordRTC.stopRecording((function(i){o.blob=o.recordRTC.getBlob(),o.blob&&o.blob.size?e(i):t("Empty blob.",o.blob)}))}catch(e){t(e)}}))},this.pauseRecording=function(){return new Promise((function(e,t){try{o.recordRTC.pauseRecording(),e()}catch(e){t(e)}}))},this.resumeRecording=function(){return new Promise((function(e,t){try{o.recordRTC.resumeRecording(),e()}catch(e){t(e)}}))},this.getDataURL=function(e){return new Promise((function(e,t){try{o.recordRTC.getDataURL((function(t){e(t)}))}catch(e){t(e)}}))},this.getBlob=function(){return new Promise((function(e,t){try{e(o.recordRTC.getBlob())}catch(e){t(e)}}))},this.getInternalRecorder=function(){return new Promise((function(e,t){try{e(o.recordRTC.getInternalRecorder())}catch(e){t(e)}}))},this.reset=function(){return new Promise((function(e,t){try{e(o.recordRTC.reset())}catch(e){t(e)}}))},this.destroy=function(){return new Promise((function(e,t){try{e(o.recordRTC.destroy())}catch(e){t(e)}}))},this.getState=function(){return new Promise((function(e,t){try{e(o.recordRTC.getState())}catch(e){t(e)}}))},this.blob=null,this.version="5.6.2"}),void 0!==t&&(t.WebAssemblyRecorder=L)}));class lt extends Oe{constructor(e){super(),this.player=e,this.fileName="",this.fileType=J.webm,this.isRecording=!1,this.recordingTimestamp=0,this.recordingInterval=null,e.debug.log("Recorder","init")}destroy(){this._reset(),this.player.debug.log("Recorder","destroy")}setFileName(e,t){this.fileName=e,J.mp4!==t&&J.webm!==t||(this.fileType=t)}get recording(){return this.isRecording}get recordTime(){return this.recordingTimestamp}startRecord(){const e=this.player.debug,t={type:"video",mimeType:"video/webm;codecs=h264",onTimeStamp:t=>{e.log("Recorder","record timestamp :"+t)},disableLogs:!this.player._opt.debug};try{const e=this.player.video.$videoElement.captureStream(25);if(this.player.audio&&this.player.audio.mediaStreamAudioDestinationNode&&this.player.audio.mediaStreamAudioDestinationNode.stream&&!this.player.audio.isStateSuspended()&&this.player.audio.hasAudio&&this.player._opt.hasAudio){const t=this.player.audio.mediaStreamAudioDestinationNode.stream;if(t.getAudioTracks().length>0){const i=t.getAudioTracks()[0];i&&i.enabled&&e.addTrack(i)}}this.recorder=ct(e,t)}catch(t){e.error("Recorder",t),this.emit(R.recordCreateError)}this.recorder&&(this.isRecording=!0,this.player.emit(R.recording,!0),this.recorder.startRecording(),e.log("Recorder","start recording"),this.player.emit(R.recordStart),this.recordingInterval=window.setInterval((()=>{this.recordingTimestamp+=1,this.player.emit(R.recordingTimestamp,this.recordingTimestamp)}),1e3))}stopRecordAndSave(){this.recorder&&this.isRecording&&this.recorder.stopRecording((()=>{this.player.debug.log("Recorder","stop recording"),this.player.emit(R.recordEnd),function(e,t,i){const o=window.URL.createObjectURL(e),s=document.createElement("a");s.href=o,s.download=(t||be())+"."+(i||J.webm),s.click(),setTimeout((()=>{window.URL.revokeObjectURL(o)}),Ee()?1e3:0)}(this.recorder.getBlob(),this.fileName,this.fileType),this._reset(),this.player.emit(R.recording,!1)}))}_reset(){this.isRecording=!1,this.recordingTimestamp=0,this.recorder&&(this.recorder.destroy(),this.recorder=null),this.fileName=null,this.recordingInterval&&clearInterval(this.recordingInterval),this.recordingInterval=null}}class dt{constructor(e){return new(dt.getLoaderFactory())(e)}static getLoaderFactory(){return lt}}class ut{constructor(e){this.player=e,this.decoderWorker=new Worker(e._opt.decoder),this._initDecoderWorker(),e.debug.log("decoderWorker","init")}destroy(){this.decoderWorker.postMessage({cmd:E}),this.decoderWorker.terminate(),this.decoderWorker=null,this.player.debug.log("decoderWorker","destroy")}_initDecoderWorker(){const{debug:e,events:{proxy:t}}=this.player;this.decoderWorker.onmessage=t=>{const s=t.data;switch(s.cmd){case A:e.log("decoderWorker","onmessage:",A),this.player.loaded||this.player.emit(R.load),this.player.emit(R.decoderWorkerInit),this._initWork();break;case p:e.log("decoderWorker","onmessage:",p,s.code),this.player._times.decodeStart||(this.player._times.decodeStart=be()),this.player.video.updateVideoInfo({encTypeCode:s.code});break;case h:e.log("decoderWorker","onmessage:",h,s.code),this.player.audio&&this.player.audio.updateAudioInfo({encTypeCode:s.code});break;case c:e.log("decoderWorker","onmessage:",c,`width:${s.w},height:${s.h}`),this.player.video.updateVideoInfo({width:s.w,height:s.h}),this.player.video.initCanvasViewSize(),this.player._opt.playType===o&&(this.player.video.initFps(),this.player.video.initVideoDelay());break;case u:e.log("decoderWorker","onmessage:",u,`channels:${s.channels},sampleRate:${s.sampleRate}`),this.player.audio&&(this.player.audio.updateAudioInfo(s),this.player._opt.playType===i?this.player.audio.initScriptNode(s):this.player._opt.playType===o&&this.player.audio.initScriptNodeDelay(s));break;case l:this.player._opt.playType===i?(this.player.handleRender(),this.player.video.render(s),this.player.emit(R.timeUpdate,s.ts),this.player.updateStats({fps:!0,ts:s.ts,buf:s.delay}),this.player._times.videoStart||(this.player._times.videoStart=be(),this.player.handlePlayToRenderTimes())):this.player._opt.playType===o&&this.player.video.pushData(s);break;case d:this.player.playing&&this.player.audio&&(this.player._opt.playType===i||this.player._opt.playType===o)&&this.player.audio.play(s.buffer,s.ts);break;case m:s.message&&-1!==s.message.indexOf(f)&&(this.player.emit(R.error,I.wasmDecodeError),this.player.emit(I.wasmDecodeError));break;default:this.player[s.cmd]&&this.player[s.cmd](s)}}}_initWork(){const e={debug:this.player._opt.debug,forceNoOffscreen:this.player._opt.forceNoOffscreen,useWCS:this.player._opt.useWCS,videoBuffer:this.player._opt.videoBuffer,openWebglAlignment:this.player._opt.openWebglAlignment};this.decoderWorker.postMessage({cmd:w,opt:JSON.stringify(e),sampleRate:this.player.audio&&this.player.audio.audioContext.sampleRate||0})}decodeVideo(e,t,s){this.player._opt.playType===i?this._decodeVideo(e,t,s):this.player._opt.playType===o&&(this.player.video.rate>=this.player._opt.playbackForwardMaxRateDecodeIFrame?s&&this._decodeVideoNoDelay(e,t):this._decodeVideoNoDelay(e,t))}_decodeVideo(e,t,i){const o={type:b,ts:Math.max(t,0),isIFrame:i};this.decoderWorker.postMessage({cmd:k,buffer:e,options:o},[e.buffer])}_decodeVideoNoDelay(e,t){this.decoderWorker.postMessage({cmd:B,buffer:e,ts:Math.max(t,0)},[e.buffer])}decodeAudio(e,t){this.player._opt.playType===i?this.player._opt.useWCS&&!this.player._opt.useOffscreen||this.player._opt.useMSE?this._decodeAudioNoDelay(e,t):this._decodeAudio(e,t):this.player._opt.playType===o&&this._decodeAudioNoDelay(e,t)}_decodeAudio(e,t){const i={type:g,ts:Math.max(t,0)};this.decoderWorker.postMessage({cmd:k,buffer:e,options:i},[e.buffer])}_decodeAudioNoDelay(e,t){this.decoderWorker.postMessage({cmd:S,buffer:e,ts:Math.max(t,0)},[e.buffer])}updateWorkConfig(e){this.decoderWorker.postMessage({cmd:C,key:e.key,value:e.value})}}class ht extends Oe{constructor(e){super(),this.player=e,this.stopId=null,this.firstTimestamp=null,this.startTimestamp=null,this.delay=-1,this.bufferList=[],this.dropping=!1,this.initInterval()}destroy(){this.stopId&&(clearInterval(this.stopId),this.stopId=null),this.firstTimestamp=null,this.startTimestamp=null,this.delay=-1,this.bufferList=[],this.dropping=!1,this.off()}getDelay(e){return e?(this.firstTimestamp?e&&(this.delay=Date.now()-this.startTimestamp-(e-this.firstTimestamp)):(this.firstTimestamp=e,this.startTimestamp=Date.now(),this.delay=-1),this.delay):-1}resetDelay(){this.firstTimestamp=null,this.startTimestamp=null,this.delay=-1,this.dropping=!1}initInterval(){this.player.debug.log("common dumex","init Interval");let e=()=>{let e;const t=this.player._opt.videoBuffer;if(this.bufferList.length)if(this.dropping){for(e=this.bufferList.shift(),e.type===g&&0===e.payload[1]&&this._doDecoderDecode(e);!e.isIFrame&&this.bufferList.length;)e=this.bufferList.shift(),e.type===g&&0===e.payload[1]&&this._doDecoderDecode(e);e.isIFrame&&(this.dropping=!1,this._doDecoderDecode(e))}else if(e=this.bufferList[0],-1===this.getDelay(e.ts))this.bufferList.shift(),this._doDecoderDecode(e);else if(this.delay>t+1e3)this.resetDelay(),this.dropping=!0;else for(;this.bufferList.length&&(e=this.bufferList[0],this.getDelay(e.ts)>t);)this.bufferList.shift(),this._doDecoderDecode(e)};e(),this.stopId=setInterval(e,10)}_doDecode(e,t,i,o){const s=this.player;let r={ts:i,type:t,isIFrame:!1};s._opt.useWCS&&!s._opt.useOffscreen||s._opt.useMSE?(t===b&&(r.isIFrame=o),this.pushBuffer(e,r)):t===b?s.decoderWorker&&s.decoderWorker.decodeVideo(e,i,o):t===g&&s._opt.hasAudio&&s.decoderWorker&&s.decoderWorker.decodeAudio(e,i)}_doDecoderDecode(e){const t=this.player,{webcodecsDecoder:i,mseDecoder:o}=t;e.type===g?t._opt.hasAudio&&t.decoderWorker&&t.decoderWorker.decodeAudio(e.payload,e.ts):e.type===b&&(t._opt.useWCS&&!t._opt.useOffscreen?i.decodeVideo(e.payload,e.ts,e.isIFrame):t._opt.useMSE&&o.decodeVideo(e.payload,e.ts,e.isIFrame))}pushBuffer(e,t){t.type===g?this.bufferList.push({ts:t.ts,payload:e,type:g}):t.type===b&&this.bufferList.push({ts:t.ts,payload:e,type:b,isIFrame:t.isIFrame})}close(){}}class pt extends ht{constructor(e){super(e),this.input=this._inputFlv(),this.flvDemux=this.dispatchFlvData(this.input),e.debug.log("FlvDemux","init")}destroy(){super.destroy(),this.input=null,this.flvDemux=null,this.player.debug.log("FlvDemux","destroy")}dispatch(e){this.flvDemux(e)}*_inputFlv(){yield 9;const e=new ArrayBuffer(4),t=new Uint8Array(e),i=new Uint32Array(e),o=this.player;for(;;){t[3]=0;const e=yield 15,s=e[4];t[0]=e[7],t[1]=e[6],t[2]=e[5];const r=i[0];t[0]=e[10],t[1]=e[9],t[2]=e[8];let a=i[0];16777215===a&&(t[3]=e[11],a=i[0]);const n=yield r;switch(s){case y:o._opt.hasAudio&&(o.updateStats({abps:n.byteLength}),n.byteLength>0&&this._doDecode(n,g,a));break;case v:if(o._times.demuxStart||(o._times.demuxStart=be()),o._opt.hasVideo){o.updateStats({vbps:n.byteLength});const e=n[0]>>4==1;n.byteLength>0&&this._doDecode(n,b,a,e)}}}}dispatchFlvData(e){let t=e.next(),i=null;return o=>{let s=new Uint8Array(o);if(i){let e=new Uint8Array(i.length+s.length);e.set(i),e.set(s,i.length),s=e,i=null}for(;s.length>=t.value;){let i=s.slice(t.value);t=e.next(s.slice(0,t.value)),s=i}s.length>0&&(i=s)}}close(){this.input&&this.input.return(null)}}class mt extends ht{constructor(e){super(e),e.debug.log("M7sDemux","init")}destroy(){super.destroy(),this.player.debug.log("M7sDemux","destroy")}dispatch(e){const t=this.player,i=new DataView(e),o=i.getUint8(0),s=i.getUint32(1,!1);switch(o){case g:if(t._opt.hasAudio){const i=new Uint8Array(e,5);t.updateStats({abps:i.byteLength}),i.byteLength>0&&this._doDecode(i,o,s)}break;case b:if(t._opt.hasVideo&&(t._times.demuxStart||(t._times.demuxStart=be()),i.byteLength>5)){const r=new Uint8Array(e,5),a=i.getUint8(5)>>4==1;t.updateStats({vbps:r.byteLength}),r.byteLength>0&&this._doDecode(r,o,s,a)}}}}class ft{constructor(e){return new(ft.getLoaderFactory(e._opt.demuxType))(e)}static getLoaderFactory(e){return e===r?mt:e===s?pt:void 0}}class gt extends Oe{constructor(e){super(),this.player=e,this.hasInit=!1,this.isInitInfo=!1,this.decoder=null,this.initDecoder(),e.debug.log("Webcodecs","init")}destroy(){this.decoder&&(this.decoder.close(),this.decoder=null),this.hasInit=!1,this.isInitInfo=!1,this.off(),this.player.debug.log("Webcodecs","destroy")}initDecoder(){const e=this;this.decoder=new VideoDecoder({output(t){e.handleDecode(t)},error(t){e.handleError(t)}})}handleDecode(e){this.isInitInfo||(this.player.video.updateVideoInfo({width:e.codedWidth,height:e.codedHeight}),this.player.video.initCanvasViewSize(),this.isInitInfo=!0),this.player._times.videoStart||(this.player._times.videoStart=be(),this.player.handlePlayToRenderTimes()),this.player.handleRender(),this.player.video.render({videoFrame:e}),this.player.updateStats({fps:!0,ts:0,buf:this.player.demux.delay}),setTimeout((function(){e.close?e.close():e.destroy()}),100)}handleError(e){this.player.debug.log("Webcodecs","VideoDecoder handleError",e)}decodeVideo(e,t,i){if(this.hasInit){const o=new EncodedVideoChunk({data:e.slice(5),timestamp:t,type:i?q:Y});this.decoder.decode(o)}else if(i&&0===e[1]){const t=15&e[0];if(this.player.video.updateVideoInfo({encTypeCode:t}),t===U)return void this.emit(I.webcodecsH265NotSupport);this.player._times.decodeStart||(this.player._times.decodeStart=be());const i=function(e){let t=e.subarray(1,4),i="avc1.";for(let e=0;e<3;e++){let o=t[e].toString(16);o.length<2&&(o="0"+o),i+=o}return{codec:i,description:e}}(e.slice(5));this.decoder.configure(i),this.hasInit=!0}}}const bt={play:"播放",pause:"暂停",audio:"",mute:"",screenshot:"截图",loading:"加载",fullscreen:"全屏",fullscreenExit:"退出全屏",record:"录制",recordStop:"停止录制",narrow:"缩小",expand:"放大"};var yt=Object.keys(bt).reduce(((e,t)=>(e[t]=`\n    <i class="jessibuca-icon jessibuca-icon-${t}"></i>\n    ${bt[t]?`<span class="icon-title-tips"><span class="icon-title">${bt[t]}</span></span>`:""}\n`,e)),{});function vt(e,t){let i=!1;return e.forEach((e=>{i||e.startTimestamp<=t&&e.endTimestamp>t&&(i=!0)})),i}function wt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0;const o=e.length,s=t.length,r=Math.max(o,s),a=ee,n=Math.ceil(r/a);let A=0,c=0;!function s(){let r="",l="";for(let i=0;i<a;i++){const i=e[c];i&&(r+=`\n                     <div class="jessibuca-playback-time-minute-one${i.hasRecord?" active":""}${i.isStart?" start":""}" data-has-record="${i.hasRecord}"\n                     data-time="${i.timestamp}" data-type="${i.dataType}">\n                        <span class="jessibuca-playback-time-title-tips ${c>o-60?"jessibuca-playback-time-title-tips-left":""}"><span class="jessibuca-playback-time-title">${i.title}</span></span>\n                    </div>\n                `);const s=t[c];s&&(l+=`\n                  <div class="jessibuca-playback-time-hour" data-hour="${s.hour}" data-min="${s.min}" data-second="${s.second}"><span class="jessibuca-playback-time-hour-text">${s.title}</span></div>\n                `),c+=1}r&&i.$playbackTimeListOne.insertAdjacentHTML("beforeend",r),l&&i.$playbackTimeListSecond.insertAdjacentHTML("beforeend",l),A+=1,A<n&&(i.rafId=window.requestAnimationFrame(s))}()}function kt(e,t){const i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[];const i=(e[0]||{}).startTimestamp;for(let o=0;o<1440;o++){const s=o%60==0;let r=!1;i&&(r=vt(e,De(i,o))),t.push({title:je(o),timestamp:o,dataType:"min",hasRecord:r,isStart:s})}return t}(e);wt(i,function(){let e=[];for(let t=0;t<24;t++){let i=t+":00";t<10&&(i="0"+i),e.push({title:i,hour:t,min:0,second:0})}return e}(),t)}function St(e,t){const i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[];const i=(e[0]||{}).startTimestamp;for(let o=0;o<1440;o++){let s=60*o,r=s%1800==0,a=!1;i&&(a=vt(e,Le(i,s))),t.push({title:xe(s),timestamp:s,dataType:"second",hasRecord:a,isStart:r});let n=60*o+30;r=n%1800==0,i&&(a=vt(e,Le(i,n))),t.push({title:xe(n),timestamp:n,dataType:"second",hasRecord:a,isStart:r})}return t}(e);wt(i,function(){let e=[];for(let t=0;t<24;t++){let i=t+":00",o=t+":30";t<10&&(i="0"+i,o="0"+o),e.push({title:i,hour:t,min:0,second:0}),e.push({title:o,hour:t,min:30,second:0})}return e}(),t)}function Bt(e,t){const i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[];const i=(e[0]||{}).startTimestamp;for(let o=0;o<144;o++)for(let s=0;s<60;s++){let r=10*s+600*o,a=r%600==0,n=!1;i&&(n=vt(e,Le(i,r))),t.push({title:xe(r),timestamp:r,dataType:"second",isStart:a,hasRecord:n})}return t}(e);wt(i,function(){let e=[];for(let t=0;t<24;t++){let i=t+":00";t<10&&(i="0"+i),e.push({title:i,hour:t,min:0,second:0});for(let o=1;o<6;o++){let s=o+"0";e.push({title:i.replace(":00",":"+s),hour:t,min:10*o,second:0})}}return e}(),t)}function Et(e,t){const i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[];const i=(e[0]||{}).startTimestamp;for(let o=0;o<288;o++)for(let s=0;s<60;s++){let r=5*s+300*o,a=r%300==0,n=!1;i&&(n=vt(e,Le(i,r))),t.push({title:xe(r),timestamp:r,dataType:"second",isStart:a,hasRecord:n})}return t}(e);wt(i,function(){let e=[];for(let t=0;t<24;t++){let i=t+":00";t<10&&(i="0"+i),e.push({title:i,hour:t,min:0,second:0}),e.push({title:i.replace(":00",":05"),hour:t,min:5,second:0});for(let o=1;o<6;o++){let s=o+"0",r=o+"5";e.push({title:i.replace(":00",":"+s),hour:t,min:10*o,second:0}),e.push({title:i.replace(":00",":"+r),hour:t,min:10*o+5,second:0})}}return e}(),t)}var Ct=(e,t)=>{const{events:{proxy:i}}=e,s=document.createElement("object");s.setAttribute("aria-hidden","true"),s.setAttribute("tabindex",-1),s.type="text/html",s.data="about:blank",ve(s,{display:"block",position:"absolute",top:"0",left:"0",height:"100%",width:"100%",overflow:"hidden",pointerEvents:"none",zIndex:"-1"});let r=e.width,a=e.height;i(s,"load",(()=>{i(s.contentDocument.defaultView,"resize",(()=>{e.width===r&&e.height===a||(r=e.width,a=e.height,e.emit(R.resize))}))})),e.$container.appendChild(s),e.on(R.destroy,(()=>{e.$container.removeChild(s)})),e.on(R.volumechange,(()=>{!function(e){if(0===e)ve(t.$volumeOn,"display","none"),ve(t.$volumeOff,"display","flex"),ve(t.$volumeHandle,"top","48px");else if(t.$volumeHandle&&t.$volumePanel){const i=we(t.$volumePanel,"height")||60,o=we(t.$volumeHandle,"height"),s=i-(i-o)*e-o;ve(t.$volumeHandle,"top",`${s}px`),ve(t.$volumeOn,"display","flex"),ve(t.$volumeOff,"display","none")}t.$volumePanelText&&(t.$volumePanelText.innerHTML=parseInt(100*e))}(e.volume)})),e.on(R.loading,(e=>{ve(t.$loading,"display",e?"flex":"none"),ve(t.$poster,"display","none"),e&&ve(t.$playBig,"display","none")}));const n=i=>{let o=!0===(s=i)||!1===s?i:e.fullscreen;var s;ve(t.$fullscreenExit,"display",o?"flex":"none"),ve(t.$fullscreen,"display",o?"none":"flex")};try{pe.on("change",n),e.events.destroys.push((()=>{pe.off("change",n)}))}catch(e){}e.on(R.webFullscreen,(e=>{n(e)})),e.on(R.recording,(()=>{ve(t.$record,"display",e.recording?"none":"flex"),ve(t.$recordStop,"display",e.recording?"flex":"none")})),e.on(R.recordingTimestamp,(e=>{})),e.on(R.playing,(e=>{ve(t.$play,"display",e?"none":"flex"),ve(t.$playBig,"display",e?"none":"block"),ve(t.$pause,"display",e?"flex":"none"),ve(t.$screenshot,"display",e?"flex":"none"),ve(t.$record,"display",e?"flex":"none"),ve(t.$fullscreen,"display",e?"flex":"none"),e||t.$speed&&(t.$speed.innerHTML=Re("")),A()})),e.on(R.kBps,(i=>{if(e._opt.showBandwidth){const e=Re(i);t.$speed&&(t.$speed.innerHTML=e),A()}}));const A=()=>{if(e._opt.playType===o){const e=t.controlsInnerRect.width-t.controlsLeftRect.width-t.controlsRightRect.width-t.controlsPlaybackBtnsRect.width;t.$playbackTimeInner.style.width=e+"px"}};if(e._opt.playType===o){const i=()=>{if(e._opt.playType===o){let i=0;const o=e.playback&&e.playback.playingTimestamp;if(o){const s=new Date(o),r=s.getHours(),a=s.getMinutes(),n=s.getSeconds();e.playback.is60Min?i=60*r+a:e.playback.is30Min?i=2*(60*r+a)+parseInt(n/30,10):e.playback.is10Min?i=6*(60*r+a)+parseInt(n/10,10):e.playback.is5Min?i=12*(60*r+a)+parseInt(n/5,10):e.playback.is1Min&&(i=60*(60*r+a)+parseInt(n,10)),t.$playbackCurrentTime.style.left=i+"px"}}},s=e=>{t.$playbackNarrow.classList.remove("disabled"),t.$playbackExpand.classList.remove("disabled"),e===se&&t.$playbackNarrow.classList.add("disabled"),e===ne&&t.$playbackExpand.classList.add("disabled")},r=()=>{const e=t.$playbackCurrentTime.style.left;let i=parseInt(e,10);const o=t.controlsPlaybackTimeInner.width;i=i-o/2>0?parseInt(i-o/2,10):0,console.log("_playbackTimeOffset",i),t.$playbackTimeInner.scrollLeft=i};e._opt.showBandwidth&&(t.$controlsLeft.style.width="90px"),e.on(R.playbackTime,(e=>{t.$playbackCurrentTimeText&&(t.$playbackCurrentTimeText.innerHTML=Ce(e,"{h}:{i}:{s}")),i()})),e.on(R.playbackPrecision,((e,o)=>{t.$playbackTimeScroll.classList.remove(Ae.oneHour,Ae.halfHour,Ae.fiveMin,Ae.tenMin),t.$playbackTimeScroll.classList.add(Ae[e]),t.rafId&&(window.cancelAnimationFrame(t.rafId),t.rafId=null),t.changePercisitionInterval&&(clearTimeout(t.changePercisitionInterval),t.changePercisitionInterval=null),t.$playbackTimeListOne.innerHTML="",t.$playbackTimeListSecond.innerHTML="",t.changePercisitionInterval=setTimeout((()=>{switch(t.$playbackTimeListOne.innerHTML="",t.$playbackTimeListSecond.innerHTML="",e){case se:kt(o,t);break;case re:St(o,t);break;case ae:Bt(o,t);break;case ne:Et(o,t)}i(),s(e),r()}),16)})),e.on(R.resize,(()=>{A()})),A()}};function Rt(e,t){void 0===t&&(t={});var i=t.insertAt;if(e&&"undefined"!=typeof document){var o=document.head||document.getElementsByTagName("head")[0],s=document.createElement("style");s.type="text/css","top"===i&&o.firstChild?o.insertBefore(s,o.firstChild):o.appendChild(s),s.styleSheet?s.styleSheet.cssText=e:s.appendChild(document.createTextNode(e))}}Rt('@keyframes rotation{0%{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(1turn)}}.jessibuca-container .jessibuca-icon{cursor:pointer;width:16px;height:16px}.jessibuca-container .jessibuca-poster{position:absolute;z-index:10;left:0;top:0;right:0;bottom:0;height:100%;width:100%;background-position:50%;background-repeat:no-repeat;background-size:contain;pointer-events:none}.jessibuca-container .jessibuca-play-big{position:absolute;display:none;height:100%;width:100%;background:rgba(0,0,0,.4)}.jessibuca-container .jessibuca-play-big:after{cursor:pointer;content:"";position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);display:block;width:48px;height:48px;background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACgklEQVRoQ+3ZPYsTQRjA8eeZZCFlWttAwCIkZOaZJt8hlvkeHrlccuAFT6wEG0FQOeQQLCIWih6chQgKgkkKIyqKCVYip54IWmiQkTmyYhFvd3Zn3yDb7szu/7cv7GaDkPEFM94PK0DSZ9DzDAyHw7uI2HRDlVJX5/N5r9FoHCYdr/fvCRiNRmpJ6AEidoUQ15NG+AH8BgD2n9AHANAmohdJQfwAfgGA4xF4bjabnW21Whob62ILoKNfAsAGEd2PU2ATcNSNiDf0/cE5/xAHxDpgEf0NADaJ6HLUiKgAbvcjpdSGlPJZVJCoAUfdSqkLxWLxTLlc/mkbEgtgET1TSnWklLdtIuIEuN23crlcp16vv7cBSQKgu38AwBYRXQyLSArg3hsjRDxNRE+CQhIF/BN9qVAobFYqle+mkLQAdLd+8K0T0U0TRJoAbvc9fVkJId75gaQRoLv1C2STiPTb7rFLWgE6+g0RncwyYEJEtawCvjDGmpzzp5kD6NfxfD7frtVqB17xen2a7oG3ALBm+oMoFQBEPD+dTvtBfpImDXjIGFvjnD/3c7ksG5MU4HDxWeZa0HB3XhKAXcdxOn5vUi9gnIDXSqm2lHLPK8pkfVyAbSLqm4T5HRs1YB8RO0KIid8g03FRAT4rpbpSyh3TINPxUQB2GGM9zvkn05gg420CJovLZT9ISNA5tgB9ItoOGhFmnh/AcZ/X9xhj65zzV2Eiwsz1A1j2B8dHAOgS0W6YnduY6wkYj8d3lFKn/j66Ea84jtOrVqtfbQSE3YYnYDAY5Eql0hYAnNDv6kKIx2F3anO+J8DmzqLY1goQxVE12ebqDJgcrSjGrs5AFEfVZJt/AF0m+jHzUTtnAAAAAElFTkSuQmCC");background-repeat:no-repeat;background-position:50%}.jessibuca-container .jessibuca-play-big:hover:after{background-image:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACEElEQVRoQ+2ZXStEQRjH/3/yIXwDdz7J+i7kvdisXCk3SiFJW27kglBcSFFKbqwQSa4krykuKB09Naf2Yndn5jgzc06d53Znd36/mWfeniVyHsw5PwqB0DOonYEoijYBlOpAFwCMkHwLDS/9mwhEDUCfAAyTXA4tYSLwC6CtCegegH6S56FETAR+AHRoACcBTJAUWa+RloBAXwAYIrnt0yBNgZi7qtbHgw8RFwLC/QFglOScawlXAjH3gUqrE1cirgVi7mkAYyS/0xbxJSDcdwAGSa6nKeFTIOZeUyL3aYiEEBDuLwDjJGf+KxFKIOY+BdBL8iipSGiBmHtWbbuftiJZERBuOfgGSK7aSGRJIObeUml1ayKSRQHhlgtkiaTcdltGVgUE+ppkV54FaiS78yrwqlLoOI8Cch2XV548W7WRpTVwA6DP9kGUFYEpAOUkT9LQAvtq1M+0udKkQSgBqSlJWWYxKXj8vRACK+o6bbRIdYI+Ba7U7rKjg7L53JdAhWTZBsy0rWuBXZUuNVMg23auBF7UIl2yBbJt70JAoKV6/WwLk6R9mgKSJlJ1kLTxFmkJyCla8UZd15GJQKvyumyJ8gy8DAEvfZoINPqD41EtUjmUgoaJwAaAnjrKebVI34OSq85NBNqlCAWgE0CV5GEWwI3vQlmCbcSinYFCwPEIFDPgeIC1P1/MgHaIHDf4Aydx2TF7wnKeAAAAAElFTkSuQmCC")}.jessibuca-container .jessibuca-loading{display:none;flex-direction:column;justify-content:center;align-items:center;position:absolute;z-index:20;left:0;top:0;right:0;bottom:0;width:100%;height:100%;pointer-events:none}.jessibuca-container .jessibuca-loading-text{line-height:20px;font-size:13px;color:#fff;margin-top:10px}.jessibuca-container .jessibuca-controls{background-color:#161616;box-sizing:border-box;display:flex;flex-direction:column;justify-content:flex-end;position:absolute;z-index:40;left:0;right:0;bottom:0;height:38px;padding-left:13px;padding-right:13px;font-size:14px;color:#fff;opacity:0;visibility:hidden;transition:all .2s ease-in-out;-webkit-user-select:none;user-select:none;transition:width .5s ease-in}.jessibuca-container .jessibuca-controls .jessibuca-controls-item{position:relative;display:flex;justify-content:center;padding:0 8px}.jessibuca-container .jessibuca-controls .jessibuca-controls-item:hover .icon-title-tips{visibility:visible;opacity:1}.jessibuca-container .jessibuca-controls .jessibuca-fullscreen,.jessibuca-container .jessibuca-controls .jessibuca-fullscreen-exit,.jessibuca-container .jessibuca-controls .jessibuca-icon-audio,.jessibuca-container .jessibuca-controls .jessibuca-microphone-close,.jessibuca-container .jessibuca-controls .jessibuca-pause,.jessibuca-container .jessibuca-controls .jessibuca-play,.jessibuca-container .jessibuca-controls .jessibuca-record,.jessibuca-container .jessibuca-controls .jessibuca-record-stop,.jessibuca-container .jessibuca-controls .jessibuca-screenshot{display:none}.jessibuca-container .jessibuca-controls .jessibuca-icon-audio,.jessibuca-container .jessibuca-controls .jessibuca-icon-mute{z-index:1}.jessibuca-container .jessibuca-controls .jessibuca-controls-bottom{display:flex;justify-content:space-between;height:100%}.jessibuca-container .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-left,.jessibuca-container .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-right{display:flex;align-items:center}.jessibuca-container.jessibuca-controls-show .jessibuca-controls{opacity:1;visibility:visible}.jessibuca-container.jessibuca-controls-show-auto-hide .jessibuca-controls{opacity:.8;visibility:visible;display:none}.jessibuca-container.jessibuca-hide-cursor *{cursor:none!important}.jessibuca-container .jessibuca-icon-loading{width:50px;height:50px;background:url("data:image/png;base64,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") no-repeat 50%;background-size:100% 100%;animation:rotation 1s linear infinite}.jessibuca-container .jessibuca-icon-screenshot{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAE5UlEQVRoQ+1YW2sdVRT+1s7JxbsoVkEUrIIX0ouz15zYNA+N1RdtQfCltlUfvLbqL/BCwZ8grbHtizQqPojgBSr0JkiMmT2nxgapqBURtPVCq7HxJCeZJVPmxDlzZubMmXOSEsnAvOy917fXt9e39tp7E5b4R0vcfywTuNgRbBgBx3HuJqLVzPzmYjprjHkcwAlmLqXNm4XAISLaSESPaq2HF4OE67rbRGRYRA7btn1fbgLGmKsA/Azg0gBkGzO/vZAkHMd5hIiqc5wHcCMz/5k0Z2oExsfHV1QqldPAf8lORNu11m8tBAljzFYAYWxRSl1vWdZvuQj4RsYYF4AVBlgIOVVlE55HRIxt23ZuCfmGjuOsJ6LPoiAistW27XfaEYmIbOYhPc9bXywWR1oiEJDYQkR1zrYjEjGyqfqbKd8a7kJVtLgQ+30i8pht2wfyRKIdmJkJBPkQTbILfudJ7CTZNBvVpggEcgpvc/ML38zESbLJsxBNE/A9biX0rdjGyTQXgbxyapdsarb0PMlXtWnGoXbKpm0Essqp3bJpK4E0OXmed3+hUBDP8w5FI91M0rdcyLLILElOCbaZilSWeXMncRx4klTCY1spfG3dhZJWx3GcDUR0EEB3ZMw0ET2gtT6SZWWzjmlrBIJCl0hAKfWgZVmHszqXZVxbCSxpCS2JJA6umIhe8ZKKVLPbaBJ+S9toqVRa53nedgAbAKwIwH4FcAzAa0R0l4i8F7PPz189k6RFRA+LyNcAXojDV0oNW5b1eW4Cxpg9AHZkSaaa6hhzb065uDSCH2LmRB8Sk9gY4293g43Qo/1pV80m8yQMfZSZ781cB1zXHRKRZ2IMpgD8A+DamL4ZItqitX4/jbQx5iEA7wLoihn3V/ACckWMJN/QWj9b1x5tGBsbW6uUOh5pPy0iL3Z2dn6ilJqanp5ep5TaJSLhF4NppdRNaU8gPmapVLrO87yfIoXuWyJ6uVKp+HmFjo6OQSJ6FcBtYT+UUmstyxqvkWuUgDFmP4AnQu2/e563qlgs+u9DNZ8xZhRAX7VRRPbath0XuXk7Y8xeAE+FgL6fnJzsHRwcLIfBR0ZGLunq6poAsDLUvp+Zw7b1r9PGmJMAbg8Z7WDmoThZuK67WkS+DD18fcPMdzSQUBR/EzN/nIC/SUQ+DPXV4dclsTHmHAD/SfHCNzc3t7Kvr++HJKeMMacA3BL0nyuXyzcPDAxMxo0fHR29slAo/Ajg6qD/fE9Pzw29vb1/x42fmJi4vFwu+5G/LOg/y8zXNJLQ2dAES5JANMQ7mfn1jBI6ycx3NiMhItqstf4oAX+ziHwQ6qvDj5NQNIn/ALCKmX+JSeIvABRD7fuY+ekGBPYBeDI05tTMzExvf3+/vz2Hk91/ET8RSeI6/DoCpVJpjed5fmKGvzMAXpqdnT3oed5Ud3d3v4jsAqBr9Ei0Rmv9VRqBBPzvROQVETnq2xJRdRu9tRF+bCVOKWT+Kvl/TSIFk6SW/LAjKfjV5K8rZABi8dOOEv7FI7Z8x6zwEWbemLbyMfJr5qiSiJ96oclymBOR3bZtP9+M89WxxpjdAHY2sN3DzM8ljWl4I3Nd9x7/OE1ENcdpETnmH3e11n41zv0l4J8RkU+J6AAz+xtF4teQQG7PFslwmcAiLfSyhC72Qv9/I/Avns2OT7QJskoAAAAASUVORK5CYII=") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-screenshot:hover{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAED0lEQVRoQ+2ZycsdRRTFf2ejqHFAMQqiYBTUoElUHLNx3GgCgpuYRF2o0UT9CxwQ/BMkMSbZSKLiQgQHUDCJgjiAxiEiESdEcJbEedgcKaj3UV+/6q7u/jovPPkK3qbr1ql76p5bt6qemPKmKfefeQKHOoLFCNg+H1gi6fFJOmv7VmCvpD1N87Yh8ApwNXCzpB2TIGF7DRDm2inpmt4EbB8LfAMcGUHWSHryYJKwfRMwmuMP4BRJv9TN2RgB2wuB72BWsq+V9MTBIGF7NZBiGzhJ0o+9CIRBtt8FLqgADC6nRDbpVO9Iuqi3hCKB5cDrGZDVkp4aIhIV2aSQyyW9MScCkcQqIOfsnCORkc3I31b5VtyFRmg1IQ7dt0ja3icSQ2C2JhAjUU2ykd+dE7tBNp2i2olAJJFuc+nCt564QTadF6IzgUhiVGiqyinKaQjZpJP2ItBXTkPJZhACXeU0pGwGI9BWTkPLZlACBTldG4o5EA6E1dY66edcyNrs8Q36zg1vVaTazNs7iXPgDVJJzYs7VRvHRzaDEohyugJ4CTi84sg/wHWSdnVxsGQ7aQLXS9pZcqpL/6AEplpCU5HE8YpJ9YrXUKQ6baN1+HPaRm1fBqwFQnKGK2ZoPwCvAo8Ai4FnMpPMHMwapHUj8DFwbw3+Dklv9iZgexOwvktSRduxU2VDlErwmyXV+lCbxLbDdndlCT3TX3vV7JgnKfRuSVflfMkSsL0ZuDMz4E/gL+CETN+/wCpJzzaRtn0D8DRwWMbu1/gCcnSm7zFJd1W/jxGwvQx4r2IYnlbuA14GAomQFw8B6YtBKFSnNj2BxEJ3IvB1pdB9CjwQ8yqYhcg/DJxZ8WOZpA/SbzkC24DbEqOfgPMkBRKzmu23gEuSj1sk5SI3Y2J7C3BHMuZz4FxJf6fgto8APgIWJd+3SUrHjr9O294HnJUMWi8pSGqs2V4CvJ88fH0i6eyChKr4KyS9WIO/Ang+6RvDz0XgABCeFEdtkaQv65yy/QVweuwPY0+T9FuNQ8cAXwHHxf7wdHiypN9r7BfEl8GjYv9+SceXJLQ/mSDYTh2Baog3SHq0pYT2STqno4RWSnqhBn8l8FzSN4bfJol/jkn8bXUS228DFyfft0paVyCwFbg9sQkSDEkctueZZju8iO+tJPEYfo7A0piYKd73wP3xnB+20cvjNnphxdmlkj4sEMjhfwY8COyOY0fb6Bkl/K6FLKxS+M1KpDhJY8mvrG5doRwlf66QZfGbjhLh4pEt35kV3iUp/IvTunU8qtTil/7gaHOY2yjpntaez9b5RmBDYewmSXfX2RRvZLYvbThOh+NuqMa9Ww1+yLnXgO2SwkZR24oEens2oYHzBCa00PMSOtQL/f+NwH+Hg8hAnbrYgQAAAABJRU5ErkJggg==") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-play{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACgklEQVRoQ+3ZPYsTQRjA8eeZZCFlWttAwCIkZOaZJt8hlvkeHrlccuAFT6wEG0FQOeQQLCIWih6chQgKgkkKIyqKCVYip54IWmiQkTmyYhFvd3Zn3yDb7szu/7cv7GaDkPEFM94PK0DSZ9DzDAyHw7uI2HRDlVJX5/N5r9FoHCYdr/fvCRiNRmpJ6AEidoUQ15NG+AH8BgD2n9AHANAmohdJQfwAfgGA4xF4bjabnW21Whob62ILoKNfAsAGEd2PU2ATcNSNiDf0/cE5/xAHxDpgEf0NADaJ6HLUiKgAbvcjpdSGlPJZVJCoAUfdSqkLxWLxTLlc/mkbEgtgET1TSnWklLdtIuIEuN23crlcp16vv7cBSQKgu38AwBYRXQyLSArg3hsjRDxNRE+CQhIF/BN9qVAobFYqle+mkLQAdLd+8K0T0U0TRJoAbvc9fVkJId75gaQRoLv1C2STiPTb7rFLWgE6+g0RncwyYEJEtawCvjDGmpzzp5kD6NfxfD7frtVqB17xen2a7oG3ALBm+oMoFQBEPD+dTvtBfpImDXjIGFvjnD/3c7ksG5MU4HDxWeZa0HB3XhKAXcdxOn5vUi9gnIDXSqm2lHLPK8pkfVyAbSLqm4T5HRs1YB8RO0KIid8g03FRAT4rpbpSyh3TINPxUQB2GGM9zvkn05gg420CJovLZT9ISNA5tgB9ItoOGhFmnh/AcZ/X9xhj65zzV2Eiwsz1A1j2B8dHAOgS0W6YnduY6wkYj8d3lFKn/j66Ea84jtOrVqtfbQSE3YYnYDAY5Eql0hYAnNDv6kKIx2F3anO+J8DmzqLY1goQxVE12ebqDJgcrSjGrs5AFEfVZJt/AF0m+jHzUTtnAAAAAElFTkSuQmCC") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-play:hover{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACEElEQVRoQ+2ZXStEQRjH/3/yIXwDdz7J+i7kvdisXCk3SiFJW27kglBcSFFKbqwQSa4krykuKB09Naf2Yndn5jgzc06d53Znd36/mWfeniVyHsw5PwqB0DOonYEoijYBlOpAFwCMkHwLDS/9mwhEDUCfAAyTXA4tYSLwC6CtCegegH6S56FETAR+AHRoACcBTJAUWa+RloBAXwAYIrnt0yBNgZi7qtbHgw8RFwLC/QFglOScawlXAjH3gUqrE1cirgVi7mkAYyS/0xbxJSDcdwAGSa6nKeFTIOZeUyL3aYiEEBDuLwDjJGf+KxFKIOY+BdBL8iipSGiBmHtWbbuftiJZERBuOfgGSK7aSGRJIObeUml1ayKSRQHhlgtkiaTcdltGVgUE+ppkV54FaiS78yrwqlLoOI8Cch2XV548W7WRpTVwA6DP9kGUFYEpAOUkT9LQAvtq1M+0udKkQSgBqSlJWWYxKXj8vRACK+o6bbRIdYI+Ba7U7rKjg7L53JdAhWTZBsy0rWuBXZUuNVMg23auBF7UIl2yBbJt70JAoKV6/WwLk6R9mgKSJlJ1kLTxFmkJyCla8UZd15GJQKvyumyJ8gy8DAEvfZoINPqD41EtUjmUgoaJwAaAnjrKebVI34OSq85NBNqlCAWgE0CV5GEWwI3vQlmCbcSinYFCwPEIFDPgeIC1P1/MgHaIHDf4Aydx2TF7wnKeAAAAAElFTkSuQmCC") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-pause{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAABA0lEQVRoQ+1YwQqCUBAcfWXXsLr2AXWTPXno8yVB8AP6Aa3oHI+kCDqYaawJljSe133uzO44bx0M/HEG/v1gAd9mkAyQgY4I/F8LJUlyrQFtD2AtIkcNoFEU+Z7n7QD4DfFHEVlocrVmgAUAIAOl3mILPcDgEFcUhyrUKMGUUcroc3NQRimj9XJBGaWMvvPydKN0o6/9QTdKN6rZANxj6EbpRulGuZnjYqs8BbyR8Ub2Izeys+u6yyAIDpo/ehzHM2NMDsA0xFsRmWhyfTIDWSXxCEBmrd2EYXjSHJqm6bQoii2AOYBL5Z0xgFxEVppcrQvQJO0zhgX0iXbdWWSADHRE4AZQ731AhEUeNwAAAABJRU5ErkJggg==") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-pause:hover{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAA7klEQVRoQ+2YSwrCQBBEX6HiVvxsPYDewfN7By/gD9ciQkvERQwJdBSiYs0mEDo96aruombEjy/9+P/jAj7NoBkwA28i8H8tFBFRA9oeWEo6ZgCNiDGwAYpn3TpKmmVytWbABQBmoNRbbqEHGB7iiuJYhRol2DJqGX1uDsuoZdRmLuNZSzGWUcuoZdRHSp/IylNgK2ErYSthK3FHwLcSvpXIjoLt9Jfa6TMwl3TIMBkRE2AH9BriL5KGmVyvWIltJXEfKN6tJJ0ym0bECFgDU+Ba+WZQFCdpkcnVuoBM0i5jXECXaNftZQbMwJsI3AAPN3dAQflHegAAAABJRU5ErkJggg==") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-record{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAC+UlEQVRoQ+1ZS2sTURT+zlDJYE3XSq219QHVuEjnJDT+Bff9Abqw2voAEfGxqygUqWhVFHGl/yMLu9BwByxk5SNI66ML6U7axjhHbmhgWiftncxoOiV3FcI53z3f/e65594zhIQPSnj86BBot4IdBToKRFyBnbeFlFIScVEiuYvIWC6Xe2YK8pcC7SYA4CMzH4mDQBXAqilQBDsLQLfPf9FxnF4i8kwwmypARI+Wl5dvmIBEsUmlUkNE9NaHsVCpVAZGR0d/m+A2JSAid3K53E0TkCg2pVKpz7KseR/GfKVSGYxMAMA0M1+JEpyJb6lUOm5ZVnkrAsVisaunp+esiByr1Wp3R0ZGvmifzZK4XQQWHMc52MgBpdQuAOcAXABwuB400ZTjONdaIjA7O5u2bVsnWU1EujzP+5nP5xdMVjvIJkCBD8x8VCm1G8AYgAkAAxt8Z5j5YmgCSqlTAJ4D2OcD/AXgATNfbYVEAIFPIvKKiE4D6GuCea8xX6gtpJT6DmBvECgRFRzHeROWRAABE4iWCbwHEFhkPM/L5vP5dyaz+23+KwHXdR3P854S0YG1ILSCuthNMfNM2OC1/RYENLY+ygcBnPfht6ZAA6BYLNr6dyqVokKhsGpaNQ2TWJstreXaE2aed133sojcj41AKyvdzCdAgSXLsk4MDw9/a/i4rntbRPxFNZoC/5jAV2be759DKTUJ4FZSFFi0bbs/k8noy2R9dAjEuWU2YgXkQOK3kD6BMsysi2Z9JC2Jdcw/ALzwPO+xvmcl7Rj177JVEbkO4BARjSflFDJJuW1dBxJPoCIiL4noDIB1BS0pW6j+oJmbm+uuVqvjRKQfLr0bZHnIzJf0f6HeAybahrUJqAPruhLlcnnPysqKfpXp11n/Gv62zoHAroS+AafT6QkiGrIsazKbzX7eVIHEt1US39gCkOzWYthkjNE+tuZujDGZQ8XRXn8N4KT5lLFZ6uaYPt+nwyDuvC80YdhvB9uOAu1WoaNAR4GIK/AHvdr+QAexB7EAAAAASUVORK5CYII=") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-record:hover{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACfUlEQVRoQ+2ZSYsUQRCFvycK4nJXXEbHBdwO4kn/gv9CD467ICIutxEFkREdFUU86T/xojcPntyQcT2INw+uISFVkD1Wd2dWlU7nUHlqisiX+fJFZGREi8yHMt8/HYG5VrBToFOg4QnMPxcyM2t4KE2nT0i6EwvylwIjQOCFpE1tEPgGfI0FamC3AFgazP8IrJL0KwZzkAI3gLMxIA1ttgCPA4w3wHpJP2NwBxG4KOlcDEgTGzNbA8wEGP57vA0CU5JONtlczFwz2wY8HUbAzBYCB4CtwCVJb33OIAXmioC70LoyBsxsEXAQOApsLIhelnS6FgEzW+5BBvwA/FS+SPJFa40KBZ5L2mxmS4AJ4IjHxCzwaUnHkgmY2V7gLrAyAPwOXJN0qg6DCgIvgQfAPsDjo2pcKddLciEz+wCs6AO6W9KjVBIVBGIgahN4BvRLMjslPYlZPbT53wR2AbeBtcUmXEFPdh5U06mbd/shBBzbr/Jx4FCAX0+BEsDMFocEYrNmFcE+BD4XsXZL0oyZnQCutkagzkn3m1NBwDe/Q9L74MAuFEqUn5op8I8JvJO0elacTALnc1HAH3Njkvwx+WeYWUegTa/pwaqIgexdyIN4uyRPmqULZRXEvulPwD3gpr+zcrtGQxfzRHYG2AAczuUWiom3kc4D2RN4BdwH9gM9CS0XFyoLGu9UuN974eIFVDiuSzruH5LqgRhtU20q8kBPV8LMlhVVmVdnYwX+SMdAZVeieAF7eeltmElJr4cpkH1bJfvGVvatxdR4bMu+teZuWxtKxWncXn8I7EldtQV7vz79fp9KwZp//9CksB8F206BuVahU6BToOEJ/Ab7+KdABdTt8AAAAABJRU5ErkJggg==") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-recordStop{background:url("data:image/png;base64,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") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-recordStop:hover{background:url("data:image/png;base64,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") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-fullscreen{background:url("data:image/png;base64,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") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-fullscreen:hover{background:url("data:image/png;base64,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") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-fullscreenExit{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAADd0lEQVRoQ+2Zz2sdVRTHv+fJBDW6anDVXen6wZszYxYBiYgtFGst3VSDunKjpS0GpUlqfjVpsVVs6aaL0or4YxMVFCJZ2ZLdPUP+gq5bQnTxtNAkfTnlhnnlkmQy9yV9780rudt77tzv5/y4v4bQ4Y06XD/2ANodwec/AiJygJnvtdvTWfPnRkBEJAiCN8rl8kMfiPn5+Ve7u7v3rays0Orq6lJfX99/PuN2auMDoAD+BvA2M6/mTWSMOUtE48D6AjHGzN/kjdlNvy+AnWOOmQ/lTSYiEwDOWzsimgrDcCRvzG76GwGw8/zJzO9sN6GInAMwbW1UdSSKoqndCMwb6wNwGsB39Q+p6h/M/C4R2dTa1AoHYBWKyCkA1+pqiWi2Wq0e7e/vf7yRoJAAKcQggMtuJKIoOtoxACnE0/xOi/SXMAxPuhCFjUBdpIjYVWXSEf0TM3/g9BeriDMKdSPEz8z8vrU1xgwT0YXCrEJZy1iSJKOqOub0/8jMA0mSfKKqNwoPkHp7ioiGHIhRIvpHVa93BEBa2JcAfOlALAHo6RgAKzRJkk9V1S6xL7kpV4idOM31taxaIKJHqmpPnMMA9hcOQES2PDJkAT1XAAC+ZebPfWB3auNzmLObVsNRUNUXVHUujuM7OxXnMy4XwOcj29mIyOuq+lapVGrYCelKpkEQ3CyXy4tbzdN0AGPMxr2iYZ+sra3FcRybtgCIiK2BKw2rdgaUSqWoUqlIkQAepFDdAF7cBq5ERI9rtdr1OI7tmE2t6SmUEYFHAEaexYW/1QC2EF+ru5GIvg7D0D2GNJxprQY4o6qv1I/b6SpzOYqiLxpWng5oOQAzXxWRWwA+dkRfYOb1p5hGW6sBJpn5KytSRG4D+KguWFXHoyhy7xdeLC0F2ChSRL4H8OFuINoKYIUbY34gogHH3eeZef1K6tPaDpCm068A3nMEDzHzxY4BUNWSiPxORO6z5aDPPlGICNQ9bYyZIaLjjudzIQoFkKbTbwCO+UI0HcB9J/LdeY0xs0R02IGYYObRrWqiFQCfEZEtSHsfmGZm+4qxbbM/hQD8BeBNa0hEM2EYnmgLgP3lFARBT1dXly4vL//b29tbzQNIU+llAHeJaLFSqRzJes5vegR8xGbZLCwsHKzVav8z8/0sm0ID+MDvAfh4qZk2exFopnd9vv0ELrXBQO7fD10AAAAASUVORK5CYII=") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-fullscreenExit:hover{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAC/ElEQVRoQ+2Zy49NQRCHvx+ReK6IlZ34E7CUiCAR4xEbTLCyQRATYswwb2IQZDYWgojHZpCQECts+ResiQwLj0RClNSkb9Lu3HtPz7mZc8+V6eXt6tP1VVV3VdcVbT7U5vozC9BqD/7/HjCzlZLet9rS9fbP9ICZvQPWSfqRAmFmS4ClMHm+JiR9S1mXVyYFwIBXwEZJv7I2MrPjQH8A6JN0OWtNM/OpAL7HS0mbsjYzswGgN8gNS+rJWtPM/HQAfJ9nkrY22tDMTgMjQaZH0nAzCmatTQE4ClyNPvQU2CbJQ2vKKB2Aa2hmR4DrkbbPgQ5Jv6sJSgkQILqA0dgTkjraBiBAxPHtPz2UtDuGKK0HKkqamd8qg5HS9yXtjebLdYjrHNRqiAeS9gQvnQGGSnML1bvGzOwc0BfN35PUaWYHgRulBwjW9ju+O4JwqM/AWFsABIgLwKkIYgJY1jYAAeJQuGIXVIVcKTKxh8WfBin9J+AVpx/eFWUEqFkyNACKp0rhgWYArkg6kQibSyylmPOklQdibijBX+fSLHFRJkDid+qKmdlaYENOI0zeEcBNSZ9qbVIEQHWuyGOTNZLetgrAz8ClPFpHa1ZL8rf5lFGEB2oBfAxQi4D5DeDmAP7mGJPka0oD4LnDr9imH/xFe8AP4vLIjBclxWXItCOtaIBjwOKo3HaFRyWdnLbmYUHhAJKumdkt4ECk9JCkSitmWixFAwxKOjt5uZvdBvZH2vZLit8XSSBFA/yjpJndAfY1A9FSgOCJu0BnBNErqfIkzfRCywECxCNgR6Rtt6TzmdqHBmyKXG4ZM4sTWc04NzNPWE+AuG3ZlZInSuGBinXMbBzYGVkrE6JUACGcHgPbUyGKAIj7REmZ18y897o5ghiQ5E/bltRChwE/kF7Xj0jyLkbDYWbzgBfA+iA4LmlXqwD8LydvszjAF0lfswBCKC0E3gBeP22p186f8RBKUbaejJmtAr5L+lBPptQAKfCzAClWmkmZWQ/MpHVTvv0X9iFAQGQyevIAAAAASUVORK5CYII=") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-audio{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACrUlEQVRoQ+2ZPYgTURCAZzbBXJnCeL2Cnb87b9MEtPBUrrMQFAtrtT5/ClGs9LBWWz0RtbBUFCF4oJDsbO68wsLA2YqQSmLlvpEHu7IuMdlLcus+yUKKhJfZ+ebnvZl5CJY/aLn+MAP41x7M1QPMfFtr/crzvHfTAs8FoNPp1LTWzwHgqIg0lFLvrQHwfX8BER8DwC6jNCIecF13wwoA3/dvIuKNpLJa60Oe560XGoCZd4rICiKeTCtaeABmPg4AJmRqg6xcaABmvg4At4aFRyEBhoVM4UMoCplHADCfJTEL5YEsIVNID5iQAYCHALCYxeq5b6PMfF5EBAAEESthGK7W6/XPRpFWq7W3VCqtZg2ZcT3g+/6i4zjzIlLSWn/yPO/DIGMNLCWY2Sj/+xGRK0qpZfNDEASnROTFVi0fr8+aA8z8Ld6KEfGt67oLYwMAwEUium8EREn7OgeAjwCwPyo/nrque3YSgAtE9GDaAM1mc65arc4Zuf1+P2w0Gt9jJZl5DQAORt+fENG5wgEw8zUAMB/zbBBRwyqAIAjuiMjlSOlNItpjFUCqWl0josMzgChR/9hGAWBbknjmAdPhDdqa0gfZzAMJKyVP4v8hhJYRcSni+0JEu63ahZj5anyQici6UuqIVQDdbrfS6/UqRulyufyTiH5sF8AlIro37VpoWEHIzGZ2tM+sEZFnSqkzk9RCS0R01wjIsZz+mug53hDRia0AnI4bGgDYISItz/M2jYC8Gpp2u30MEWuO4zha665Sqp0ZYFStX/iWchRAItFGzoHSsrJ2ZFl1mHg6bfVYJeGJv85CC++BpIJZ5kSFC6G0ha0e7mYJqcJ7IOkRay84UhD2XjHFIFZf8iW9YcYoYRi+tO6aNeupOs66iU/icV46zf/MAKZpzXFk/QL+JG1PUPhRiQAAAABJRU5ErkJggg==") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-audio:hover{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACSElEQVRoQ+2Zu4sUQRCHf5+C+gf4yBXMfMYHGvjCzEBQDIzV+HwEohipGKupD0QNDE8UEwUFTe68wEDhTMVUMFJ+0tArzbjs9u3Ojt0wBR0M9MzUV1XdXVWNKhcq1189wP/2YKcesH1d0nPgdVvgnQDY3iTpqaT9kuaAt9UA2D4o6aGkzVHpXcByFQC2r0q60lB2D7BUNIDtjZIeSDoyRNGyAWwfiiET4n6YlAtg+7Kka2PCozyAMSHT5CkLIIbMfUlbMhdmOQCZIVOeB2LI3JN0NNPq6bTZe8D2aUmOY72kN8DnoIXt7eF5FSEzkQdsB+OEsFwr6RPwbpixhqYStoPyqVwAbkaAY5KeTWD5wStZHrD9XdJgK34FhBP9H8kFOAvciQBhn3/RAcBHSTvjfx4DJ6cBOAPcbRvA9gZJYQT5DfwYKGl7UdLu+PwIOFUiwCVJYQRZBuZqA7gh6XxUegXYVhtAmq0uAnt7gLhQm9vorBZx74Hcc6D3QLKH/z2JGyVnlYs4pCfzEe4rsLW2XehicpAtAftqAwiZbhhBfgE/ZwVwDrjddi40KiG0HXpHO+KcJ8CJaXKheeBWBOgqnf6W1BwvgcOrATieFDTrJL0HViJAVwXNgVgPrJH0BfiQDTDKtREiNK7KLSnHASQLLacP1PxcVkWWq8PU3emq2yqJJ0b1Qsv2QKpdZp+orBBqmrfq5m5mSJXtgUZI1XnB0YCo94opCal6L/ka3ghtlIXqrllzT9VJ5k19Ek/y0zbf6QHatOYk3/oDujC8QMWgjf4AAAAASUVORK5CYII=") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-mute{background:url("data:image/png;base64,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") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-mute:hover{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAHsUlEQVRoQ+2Z969VVRCFv7H33nvvvfcSe2+xxJgY4z9j/Bs0/mABFQXBhl1sgNjQSCyoiL2BDaxs873MJsfDuZd7gfeQxJ3cvAfv3HP22rNmzZo5wRq+Yg3fP/8DWN0RXCYCpZSzgM2Br4GPgW8j4s9hNlpKWQfYETgUOB44GtgMmA1MBF4BFkdEGea+Xdd2AbgF2B2YD0wHZkbEZ4M+qJSyIbArcARwMnAUsC/wO/AscCfwQkT8Meg9+13XBeBx4EjgZ+ClPLGXI+KbfjcqpXivLYA9gWOA0/PnDsDGwOeA977bCAwb1V7P7gIwDpBG2wJfAg/nZ3oXiFLK2sD6ef0+uWlp48kbSddfwAfAVOB+YNZoRuBG4CLgbGDLpNLTwIPAjDaIUsomwM7A4cCJyfm9ga0Bwbn+Bt4fKwDyV+5eAZyayWgkHgGmmBdNEKUUk/U44DzgNGA/YN1WyBWBucATwH3Aq6MZgbXyRAVxMXABsFUrEi9GxILkvbQ5JwGfABiR9ho7APXJpRSTzxO9CjgF2ClBPJrJ+JYSm/Io2Mvyeq+r1Km3G3sAPrmUsktu3pyQItskiFkpiS8CnybfBXl+5sBu8K8qP3YASik+/DdgEaBWbw+cCVwHnJRF7gd5nJEwwT9JmglC2hmRZiRUoQ8HzYFSynrABhk+C17PQtolozcBC/Kklb7FwCHANbk5f3d5zZuAlDI5rdoqj/pvxMwHBaHKaE3ie5eXxKWU7QCjb6WeHxHfDVMH1GlV521AinyUSnR5Jqr6XhP1JzUdeKwBQpqdkSBUMf+tMAjA68YPAOBA4FhgSToBJbhzdUVADyQlrMKTgdfyZJVVE1qLYGWta2FGQpm1UPldT1AQl2ZhE4R2xGgZAetJT1qUUoyeVDQCUyJi5jAA/JJlX99iNF7OgnYl4EcKbdS64Y8JtNJpXoKwGJrYFjm9kPliBDRznq4GT+No3ZCqHoY/zaVr8xnjI+KFYQEojz7M05JGPsQICOCwVgTakdB6mBOCsEIrxdWamDMT0iSapAcBB+T99Vq6Vb8nTQWgqx23IgCMwDONCAhAOghAo9dVrARSI1Hp5H1UMUG4WekpODcqrQQm1aw5ioDfU920Ih6YHuuBiJAFA+fASOY3ABhuXeYljRzYtNcNkwavZ/4YRblvJExM5dTN+38aPTfpx9/nAHdlHgnI52nNJ0WEtn4oAIax5oBfHgaAD5LLJp72WRDSoyb+91ln9s8Dsb5owd8Bbk/gyrFSbK49FBEzxhpAs05IC/NIGbXH0JnKbQFIyeuBvRLAbW44VW+1A2jmxJMZjXd1odlD7JER0L7bsRkBAeh4zQ9ltEZgzCnUjLh0MicmJZ0+TBD2Gkbg5pTm94A7snmSQv8ZAIKR956iEjs1IlQczaJ14obsJ7xGibV4mnOVQpNXRxJ35Zx+Zhpwj5GIiIWlFOVSo6j5ky4WLBNflTMCqtBqS+IuEMqnfshEVe91vUqsYxddsImubJsDyqjFTgBD54AevymjtZDphbQF/epAnxIxYh+sMc9nsiqPUse2VOeqOZRednk2SNrqiREhqKHqwFdZyOxfNXUC0I0KwGFVr0rc6zkWMM2bG7Jbsy6oTEZC2pjo0sUiah/iWObqdLH3R4QyPBQA7fRz2YBXANWNCqBt5vqdun/7NTepadOpujykOu2QItoMI+RyuuFh6ZYnDGslPAHD7Mk4BvTmypoAPBXNXHvqsDwAUsND8aQtYvJeu2Ak9EZq/7SIEJTqdHCOdewjTHjtx8AReCP7XBsVT8gC45BLWfNUmg3N8jZe/24E5Lb38nAEoPrIfYE9VaOd0w6jZHGTbh9EhNcMDODWDKeKIPIvsh/Qo1+Ykqf5ks+DLtXG++lwjazfdRRzbgOENcIaYGLrar1GN/prRPj9gQHIP2lkuNVuGwzlzBOxU7LntSvTCph4gyyHAwLQF1mRPVGpaERteOq0w0hI26UTQGdP/abYXS2lmzWZlkSE6iEnvc7S76alkP2q2q2LtGrK1X6rjlWsATZJWguHZfYCqlvtCeoE0Eg4AbSx6rsGfkNTSnGTqo+8tYsyUsqdPt+mpV9iVwBWWVvEEXuccyersEWrTgAtdkZipHOLCOtEzzUwgHqHdJImtRs3Cs5F7bYsRBa4rnu2B1uO10ckszE8U+Xs3FSnnrPYNpKhATQoZUNu+bcyGwk/5ong2vdtA5DjTXqqSnUo1o5E51S8AlkhAI1oSBsfrm6b4OaGvyuDTZUSQHMyt8z7gVYk6lTc4uaoRoXSTiyMiF+aUVgpABkNtdpCZ16Y4OaGUbHLqnkxCABzzHFkOxLSyeT31dTciLCOLF0rDaARDVVKVXJq4Rsac0PV0ke57LOVUe207906B1sZCXPBnDDHlGpP325tTu0lVgmF2glVSlGlPEUT3Eg4DFbvBVdfVzl56PmOLNXOg/D7RtQa4YxW8PPaqrTKItBSKR8qCLksJWzgLWbaaOvASxFhgexcpRQrsAehSCgWTsOdj/7YfrOzygE0gFjgfN0kDaSVUbAaa6N9xaTB67nyXbP0UQxUrEVdtBtNACa3Rc9ISCOLne5Tdzt7eQBSIEzsukedwTIvxkcNQL/TXZV/W+MB/AMANfVPjBGemwAAAABJRU5ErkJggg==") no-repeat 50%;background-size:100% 100%}.jessibuca-container .jessibuca-icon-text{font-size:14px;width:30px}.jessibuca-container .jessibuca-speed{font-size:14px;color:#fff}.jessibuca-container .jessibuca-quality-menu-list{position:absolute;left:50%;bottom:100%;visibility:hidden;opacity:0;transform:translateX(-50%);transition:visibility .3s,opacity .3s;background-color:rgba(0,0,0,.5);border-radius:4px}.jessibuca-container .jessibuca-quality-menu-list.jessibuca-quality-menu-shown{visibility:visible;opacity:1}.jessibuca-container .icon-title-tips{pointer-events:none;position:absolute;left:50%;bottom:100%;visibility:hidden;opacity:0;transform:translateX(-50%);transition:visibility .3s ease 0s,opacity .3s ease 0s;background-color:rgba(0,0,0,.5);border-radius:4px}.jessibuca-container .icon-title{display:inline-block;padding:5px 10px;font-size:12px;white-space:nowrap;color:#fff}.jessibuca-container .jessibuca-quality-menu{padding:8px 0}.jessibuca-container .jessibuca-quality-menu-item{display:block;height:25px;margin:0;padding:0 10px;cursor:pointer;font-size:14px;text-align:center;width:50px;color:hsla(0,0%,100%,.5);transition:color .3s,background-color .3s}.jessibuca-container .jessibuca-quality-menu-item:hover{background-color:hsla(0,0%,100%,.2)}.jessibuca-container .jessibuca-quality-menu-item:focus{outline:none}.jessibuca-container .jessibuca-quality-menu-item.jessibuca-quality-menu-item-active{color:#2298fc}.jessibuca-container .jessibuca-volume-panel-wrap{position:absolute;left:50%;bottom:100%;visibility:hidden;opacity:0;transform:translateX(-50%) translateY(22%);transition:visibility .3s,opacity .3s;background-color:rgba(0,0,0,.5);border-radius:4px;height:120px;width:50px;overflow:hidden}.jessibuca-container .jessibuca-volume-panel-wrap.jessibuca-volume-panel-wrap-show{visibility:visible;opacity:1}.jessibuca-container .jessibuca-volume-panel{cursor:pointer;position:absolute;top:21px;height:60px;width:50px;overflow:hidden}.jessibuca-container .jessibuca-volume-panel-text{position:absolute;left:0;top:0;width:50px;height:20px;line-height:20px;text-align:center;color:#fff;font-size:12px}.jessibuca-container .jessibuca-volume-panel-handle{position:absolute;top:48px;left:50%;width:12px;height:12px;border-radius:12px;margin-left:-6px;background:#fff}.jessibuca-container .jessibuca-volume-panel-handle:before{bottom:-54px;background:#fff}.jessibuca-container .jessibuca-volume-panel-handle:after{bottom:6px;background:hsla(0,0%,100%,.2)}.jessibuca-container .jessibuca-volume-panel-handle:after,.jessibuca-container .jessibuca-volume-panel-handle:before{content:"";position:absolute;display:block;left:50%;width:3px;margin-left:-1px;height:60px}.jessibuca-container.jessibuca-fullscreen-web .jessibuca-controls{width:100vh;transform:translateX(-13vw) translateY(-47.8vh) rotate(270deg);transition:width .5s ease-in}.jessibuca-container.jessibuca-fullscreen-web .jessibuca-play-big:after{transform:rotate(270deg)}.jessibuca-container.jessibuca-fullscreen-web .jessibuca-loading{flex-direction:row}.jessibuca-container.jessibuca-fullscreen-web .jessibuca-loading-text{transform:rotate(270deg)}.jessibuca-container-playback .jessibuca-controls{height:48px}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center{flex:1;display:flex;box-sizing:border-box;justify-content:space-between;font-size:12px}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-time{box-sizing:border-box;flex:1;position:relative;height:100%}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-time-inner{width:300px;height:100%;overflow-y:hidden;overflow-x:auto}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-current-time{position:absolute;left:0;top:0;height:15px;width:1px;background-color:red;text-align:center;z-index:1}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-current-time-text{position:absolute;box-sizing:border-box;padding:0 5px;width:60px;left:-25px;top:15px;border:1px solid red;height:15px;line-height:15px;cursor:move;background-color:#fff;color:#000;-webkit-user-select:none;user-select:none}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-time-scroll{position:relative;width:1440px;margin:0 auto}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-time-scroll.one-hour{width:1440px}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-time-scroll.half-hour{width:2880px}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-time-scroll.ten-min{width:8640px}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-time-scroll.five-min{width:17280px}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-time-scroll.one-min{width:86400px}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-time-list{position:relative;background-color:#ccc;height:48px}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-day{height:100%;overflow:hidden}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-one-wrap{height:8px;z-index:1}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-second-wrap{height:25px}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-controls-playback-btns{display:flex;align-items:center}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-minute-one,.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-second-one{float:left;width:1px;height:8px;margin:0;cursor:default;position:relative;z-index:1}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-minute-one.active,.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-second-one.active{background-color:green;cursor:pointer}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-minute-one.start,.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-second-one.start{background-color:#999}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-minute-one:hover .jessibuca-playback-time-title-tips,.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-second-one:hover .jessibuca-playback-time-title-tips{visibility:visible;opacity:1}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-title-tips{pointer-events:none;position:absolute;left:0;top:100%;visibility:hidden;opacity:0;transform:translateX(13%);transition:visibility .3s ease 0s,opacity .3s ease 0s;background-color:#000;border-radius:4px;z-index:3}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-title-tips.jessibuca-playback-time-title-tips-left{transform:translateX(-100%)}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-title-tips .jessibuca-playback-time-title{display:inline-block;padding:2px 5px;font-size:12px;white-space:nowrap;color:#fff}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-hour,.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-minute{float:left;position:relative;width:60px;box-sizing:border-box;border-top:1px solid #999;-webkit-user-select:none;user-select:none;text-align:left;height:25px;line-height:25px}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-hour:first-child,.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-minute:first-child{border-left:0}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-hour:first-child .jessibuca-playback-time-hour-text,.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-minute:first-child .jessibuca-playback-time-hour-text{left:0}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-hour:after,.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-minute:after{content:"";position:absolute;left:0;top:-8px;width:1px;height:14px;background-color:#999}.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-hour-text,.jessibuca-container-playback .jessibuca-controls .jessibuca-controls-bottom .jessibuca-controls-center .jessibuca-playback-time-minute-text{position:absolute;left:-13px}.jessibuca-container-playback .jessibuca-icon-narrow{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABNlJREFUaEPtmWmolVUUhp+XaJ5LpKQif0RkQpNE4B8bKKJBtEGlogFEi8AGG6gQ+lEWJZUQhkQWlA0alvWjsoIgApGgICusKKzIBiMaqag3lnzKOefu79v7fOdE90ILLpd7v7Xevd49rLX22mKMi8a4//xP4L9ewb5WwPYc4DZg0hAcfwhYJOnbQbCKCNg+AHgYmDHIYAnb74H5kla1xc0SsD0FWAsc3HaQArslkhYW6I1QaSRg+1Tg1ULgL4FPgfj9J7APcAhwFLB7AcZKSRcV6HWp1BKwfTzwdgbwG+Ax4Hlgg6Q/evVtx8pNBWYB52fwVki6oh8SSQK29wK+APatAXN1mJdK+rl0QNuTgduBmQ02CyUtKcWsI7AOOK0G5D1gjqT43UpszwWWNxhPlrSxBHwEAdvnAatrjN8ETpEUe3wgsT0NeB2SyXSjpFitrKQIfAUclLD8EDha0t9Z1EIF26cDL9eoz5L0TA4qRSD2d0omSvosB9jvd9uLqnPRa7pJ0pE5vFICt0hanANr+932JuCIhP1USW814ZYQ2CppXFvnSuxsn1uF4l71ByVdPSiBOyXdWuLIIDq2P68SXydMdhuVrMCxkt4dxLkSW9sR+69L6B4mKcglJUdgi6RkDWQ7ttVZwH5V6ZBb6Z2BTyS9kFK0PR14LvHtTEkvtSXwmqQRCa0qM6JG2r9kdnt0wpmzJf3V+X/bUTO9n8CbKykq4VYr8LikS3otba8HTmzh/HaTeZK6MnG1opsThd8Nku5tSyBZXNn+DjhwAAIjoovtqLuCQFSxnXKzpLvbElgl6cLECsTsRT3TVqZJeqNnC00AIlHGWemUBZKWtiWwXtJJCQK7ASuAiN+7ArnyIoJFZPivgTskxXWyS6qL04aEo7MlPd2WwC/AeEm/pgBsjwf26IeApN9rsC4HHkl8myKp9l6SC6OB1xjG2u6hxArELPdu1x/jKls3gYFRQiB5DobleODY3hOIwBBbs1PWSYqKtVZKCITxBElRZv8r0lCRXpk6L51OlBJYKyky5dDFdmTyONy79IBHohsn6YdhrEBgXCzpiWEzsB23spMTuMslzcuNl1qBR4FLawyz9XluwM7vtu8DrknYRLcu7sXR9WiUFIG9ga2JhBJAEe8jKr2SA859tx3lwfU1esskXZXDiO91XYloezzbAHCjpHtKBujVsX0osKyqZOsgYgKjXmryYZttU2PrfmBBg5ORNSOrRlMrK1XSmw9ECzFWuURmSlrTpJhrLT5VddSaMKJbETV+3F0/qCJKlA1RYhwee7k6pOc0NMqa8BtJlDR3o+a5rGS6Kp3InkEgirIoM4YhtSSyBGJ02zcBdw3DkwEwkiSKCFQkoip9YMCLTKf/0VONd4Eo4kplhqSua2cxge0j2I4ccS1wTOmoPXo/AZEQF0vabDsKuNpyOTHGdEnxXrFN+ibQQeQMIA5mZNHck1MUahG1IqKskRR/75Dq6WplHxMySVIEjPYEehyIrtrE6ieuhjsBvwFbgI+A6O/EzNeK7dnAk4UkVku6YGgECgfNqtmOR5AI3Tn5WNK2VmTrLZQboe33wjPxjqTjRiWBcKqAxI5XnFG3Ah1BIvZ46n2gq9k2aglUKxEPjdEvPaGqkF+U1JVQRzWBknM05gn8A3BFtUAnWz1iAAAAAElFTkSuQmCC") no-repeat 50%;background-size:100% 100%}.jessibuca-container-playback .jessibuca-icon-narrow:hover{background:url("data:image/png;base64,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") no-repeat 50%;background-size:100% 100%}.jessibuca-container-playback .jessibuca-icon-expand{background:url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABxJJREFUaEPtWX2MXVURnzn7dl2BEghQIFIsBcTKUpp75r59XT4bCihGI0oAFZASiWCMEJEQAlISICZGIITwIZEPqxUsAcQvPksxbOrbd859TUsRUPnQgOFLkV1Cydu9Z8w07zXb986999zdJYGE8+c9M7+Z3/mYmTMX4SM+cC78T5JksXPucERcDADzAWAPABDsSQB4FRHfQMQxAHg2iqI358JmB2PGBIwxQ4h4BgCcCABLA50aB4AnEPHB8fHxe5YvX/5+oF6mWGkCSZLUmPlyAPjiLI3/m5lvTtP0xlqtJsRmNIIJ1Ov1XSuVyrUA8O0ZWcpWkiN2idZ6zUxwgwgYY45FxF8CwH4zMRKos0ZrfRYiukD5bWKFBIwx5yDi7WVAZyHbTNP0hOHh4f+EYuQSsNaeDwA3B4C91b6cFgCeY2aJOlOIuLNz7gAA+CwzH4OIywKwXgCAGhEJZuHIJGCt/QoAPFCAkADArQBwLxG9U2StHblWAsAFANCXI/+s1vpQROQiTC8Ba+2BAPCPHGWJ7xcR0Y1FBnzzSZIcxMw/AYCTc/QfJCJZxNyRReAZAPhchubzzHxKHMdbisCL5pMkubhNxCuKiCu11nfl4fQQsNb+AAAkXPrGM4ODg7WhoaF3i5wLnU+S5JvM/KsM+ZZk9rzj6SMg2fETHkApAQ4OOeuiy8wqNCRaa78PADdkkLiBiC7MWhAfAe/FUUqNRFH0l6KVbV/+KwBgd0Ss9/f3f3fJkiVvF+kZY+5DxK965NKBgYG9sjBCCfyciM4tcsJaeyQAPNUlV9daH1G0G6Ojo/MGBwffAIBBj50fEpH3WIcQmAKAPUOOTpIkq5n5zG4HmDmK43hj0QIkSbKKma/0yG0mosN9+iEEVhPRt4qMy7y19jcAcGq3rFJqRRRF64owNm/evHur1ZJdqHgwFkZR9M/u74UEnHPHV6vVx4uMtwncBgA9R42ZDwsNu0mS/ImZv9DjKOKZWuueaFVEYLzVau0zMjKy1Udg7dq1fYsWLVqYpqlj5slKpSKJrSf5IOJpk5OTG/r7+wfTNH23Wq2+lrUgORHpp0R0cakdQMQNcgF9xowxX0fEawBggURNAJC7IuFXeeRlTmK6zMtiPJmm6dm+oq3ZbB7tnPuzZwfu01qfUooAANxNRN/oVrLWRgAgddBsxmNEdEI3wMaNGw9O0/R5T6X8FBEdXYoAM98Wx/F3upWMMdcjYmZyKcHq00T0r+ny1tp923XYTtO/I6LRWldLEQAAb/w3xlyGiFeXcNQrqpSa3/3Ir9fr+1UqFSkku6uBBhENlyXwABH1ZMd6vb53pVKRgk+6DzMacn+01vK23mE0Go1DlVI9hSIzr4vjeEVZAk8T0RKfh41GY4FS6kcAsJiZ+xBRaqjDJOl55IWsxPdPAoDI3Z9VihtjTkLEP3ow1hCRdEF2GEVhdHJqampBrVZ7PWSZjTF3SAnskdVE1AzEuBIRV3lkryCiq8oSAOn9hHYMskoJ59wR1Wp1QwgBa62UHD19JkT8ktb6D6UJAMAoER0VaPxeAOiJ1aGlhHT4mPmvHluZ74KiI7QNSym1NIqiTUUkrLVSMcqDaIeRpumi4eHhlwL0vQvAzA/HcdxTXgheEAEAGCOiWpED7RAoR2D6Rb6OiC4q0s1Ljoj4Na31/T6MUAKiK4/46wIc2bed5PZ3zj0Ux/HqIh2Zt9ZK7JdmQvd4jYgkuXmHj0AWkFzoz2utHwlxqIyMtdZ7dLYdEcRztNZ3BhNoNpvHOefyyucTiejRMg7myeaEXlH7GxEdkqef1VaRRmtPEdcBQsTvaa1vmg2JTZs2zW+1Wr+QXc3AebvVah00MjLy39IE1q9fX5k3b97fAWBhjrLE5FWhCWo6jrVWOtw/zsjaHdHXnXNL894O3ijU0R4bGzugr69PapIdqkIPobsR8dfM/AQRvZdFuF0mS111tvRKA3dvXCm1LIoiX27YBpHb3G00GkuVUqMAsHOAQekoyxvhRUSUF9ekc243pdQ+zCyZVeqkmYwJ59yyarUq9VTPCGmvH4KIv5em1kysz5HOO+2+VM9OFBIQB6y1O8k/AmY+fY4c6ob5n/xuQkRJeL6uoMhPKKVq3ccpiEDHmjHmdESUy5d3uUtxlL5omqaX1mq1V6y1y+U/Qw7ABCJGWuvtnfNSBAR4y5YtA1u3bl2JiOeV+DvZ7ZMUZ79l5lviOH5y+qQx5nhEzMszTSLSHZ3SBLqMHauU+rJz7ihEHMpoC3ZU5EEjvdV1Sqnf+ZpUHcFGo7FCKfVY1k4Q0Xa/Z0VguoFms7lXmqafUUp9yjm3CyIqZm4hokSnFyYmJl4u8184h0RKRNs7d3NGoNTBDxRuHydJmAMdFWY+L47jn83JEQr04wMV+1DvQAjzjwmErNIHKfN/D1fPT9VKzJcAAAAASUVORK5CYII=") no-repeat 50%;background-size:100% 100%}.jessibuca-container-playback .jessibuca-icon-expand:hover{background:url("data:image/png;base64,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") no-repeat 50%;background-size:100% 100%}.jessibuca-container-playback .jessibuca-playback-expand.disabled .jessibuca-icon-expand,.jessibuca-container-playback .jessibuca-playback-narrow.disabled .jessibuca-icon-narrow{cursor:no-drop}');class Tt{constructor(e){var t;this.player=e,((e,t)=>{e._opt.hasControl&&e._opt.controlAutoHide?e.$container.classList.add("jessibuca-controls-show-auto-hide"):e.$container.classList.add("jessibuca-controls-show");const i=e._opt,s=i.operateBtns,r=`\n        <div class="jessibuca-controls-center">\n            <div class="jessibuca-controls-playback-time">\n                <div class="jessibuca-controls-playback-time-inner">\n                    <div class="jessibuca-controls-playback-time-scroll">\n                        <div class="jessibuca-controls-playback-time-list">\n                            <div class="jessibuca-playback-time-day">\n                                <div class="jessibuca-playback-time-one-wrap"></div>\n                                <div class="jessibuca-playback-time-second-wrap"></div>\n                            </div>\n                        </div>\n                        <div class="jessibuca-controls-playback-current-time">\n                            <div class="jessibuca-controls-playback-current-time-text">00:00:00</div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class="jessibuca-controls-playback-btns">\n                <div class="jessibuca-controls-item jessibuca-playback-narrow">${yt.narrow}</div>\n                <div class="jessibuca-controls-item jessibuca-playback-expand">${yt.expand}</div>\n            </div>\n        </div>\n    `;e.$container.insertAdjacentHTML("beforeend",`\n            ${i.background?`<div class="jessibuca-poster" style="background-image: url(${i.background})"></div>`:""}\n            <div class="jessibuca-loading">\n                ${yt.loading}\n                ${i.loadingText?`<div class="jessibuca-loading-text">${i.loadingText}</div>`:""}\n            </div>\n            ${i.hasControl&&s.play?'<div class="jessibuca-play-big"></div>':""}\n            ${i.hasControl?`\n                <div class="jessibuca-controls">\n                    <div class="jessibuca-controls-bottom">\n                        <div class="jessibuca-controls-left">\n                            ${i.showBandwidth?'<div class="jessibuca-controls-item jessibuca-speed"></div>':""}\n                        </div>\n                        ${i.playType===o?r:""}\n\n                        <div class="jessibuca-controls-right">\n                             ${s.audio?`\n                                 <div class="jessibuca-controls-item jessibuca-volume">\n                                     ${yt.audio}\n                                     ${yt.mute}\n                                     <div class="jessibuca-volume-panel-wrap">\n                                          <div class="jessibuca-volume-panel">\n                                                 <div class="jessibuca-volume-panel-handle"></div>\n                                          </div>\n                                          <div class="jessibuca-volume-panel-text"></div>\n                                     </div>\n                                 </div>\n                             `:""}\n                             ${s.play?`<div class="jessibuca-controls-item jessibuca-play">${yt.play}</div><div class="jessibuca-controls-item jessibuca-pause">${yt.pause}</div>`:""}\n                             ${s.screenshot?`<div class="jessibuca-controls-item jessibuca-screenshot">${yt.screenshot}</div>`:""}\n                             ${s.record?` <div class="jessibuca-controls-item jessibuca-record">${yt.record}</div><div class="jessibuca-controls-item jessibuca-record-stop">${yt.recordStop}</div>`:""}\n                             ${s.fullscreen?`<div class="jessibuca-controls-item jessibuca-fullscreen">${yt.fullscreen}</div><div class="jessibuca-controls-item jessibuca-fullscreen-exit">${yt.fullscreenExit}</div>`:""}\n                        </div>\n                    </div>\n                </div>\n            `:""}\n\n        `),Object.defineProperty(t,"$poster",{value:e.$container.querySelector(".jessibuca-poster")}),Object.defineProperty(t,"$loading",{value:e.$container.querySelector(".jessibuca-loading")}),Object.defineProperty(t,"$play",{value:e.$container.querySelector(".jessibuca-play")}),Object.defineProperty(t,"$playBig",{value:e.$container.querySelector(".jessibuca-play-big")}),Object.defineProperty(t,"$pause",{value:e.$container.querySelector(".jessibuca-pause")}),Object.defineProperty(t,"$controls",{value:e.$container.querySelector(".jessibuca-controls")}),Object.defineProperty(t,"$controlsInner",{value:e.$container.querySelector(".jessibuca-controls-bottom")}),Object.defineProperty(t,"$controlsLeft",{value:e.$container.querySelector(".jessibuca-controls-left")}),Object.defineProperty(t,"$controlsRight",{value:e.$container.querySelector(".jessibuca-controls-right")}),Object.defineProperty(t,"$fullscreen",{value:e.$container.querySelector(".jessibuca-fullscreen")}),Object.defineProperty(t,"$fullscreen",{value:e.$container.querySelector(".jessibuca-fullscreen")}),Object.defineProperty(t,"$volume",{value:e.$container.querySelector(".jessibuca-volume")}),Object.defineProperty(t,"$volumePanelWrap",{value:e.$container.querySelector(".jessibuca-volume-panel-wrap")}),Object.defineProperty(t,"$volumePanelText",{value:e.$container.querySelector(".jessibuca-volume-panel-text")}),Object.defineProperty(t,"$volumePanel",{value:e.$container.querySelector(".jessibuca-volume-panel")}),Object.defineProperty(t,"$volumeHandle",{value:e.$container.querySelector(".jessibuca-volume-panel-handle")}),Object.defineProperty(t,"$volumeOn",{value:e.$container.querySelector(".jessibuca-icon-audio")}),Object.defineProperty(t,"$volumeOff",{value:e.$container.querySelector(".jessibuca-icon-mute")}),Object.defineProperty(t,"$fullscreen",{value:e.$container.querySelector(".jessibuca-fullscreen")}),Object.defineProperty(t,"$fullscreenExit",{value:e.$container.querySelector(".jessibuca-fullscreen-exit")}),Object.defineProperty(t,"$record",{value:e.$container.querySelector(".jessibuca-record")}),Object.defineProperty(t,"$recordStop",{value:e.$container.querySelector(".jessibuca-record-stop")}),Object.defineProperty(t,"$screenshot",{value:e.$container.querySelector(".jessibuca-screenshot")}),Object.defineProperty(t,"$speed",{value:e.$container.querySelector(".jessibuca-speed")}),Object.defineProperty(t,"$playbackTime",{value:e.$container.querySelector(".jessibuca-controls-playback-time")}),Object.defineProperty(t,"$playbackTimeInner",{value:e.$container.querySelector(".jessibuca-controls-playback-time-inner")}),Object.defineProperty(t,"$playbackTimeScroll",{value:e.$container.querySelector(".jessibuca-controls-playback-time-scroll")}),Object.defineProperty(t,"$playbackTimeList",{value:e.$container.querySelector(".jessibuca-controls-playback-time-list")}),Object.defineProperty(t,"$playbackTimeListOne",{value:e.$container.querySelector(".jessibuca-playback-time-one-wrap")}),Object.defineProperty(t,"$playbackTimeListSecond",{value:e.$container.querySelector(".jessibuca-playback-time-second-wrap")}),Object.defineProperty(t,"$playbackCurrentTime",{value:e.$container.querySelector(".jessibuca-controls-playback-current-time")}),Object.defineProperty(t,"$playbackCurrentTimeText",{value:e.$container.querySelector(".jessibuca-controls-playback-current-time-text")}),Object.defineProperty(t,"$controlsPlaybackBtns",{value:e.$container.querySelector(".jessibuca-controls-playback-btns")}),Object.defineProperty(t,"$playbackNarrow",{value:e.$container.querySelector(".jessibuca-playback-narrow")}),Object.defineProperty(t,"$playbackExpand",{value:e.$container.querySelector(".jessibuca-playback-expand")})})(e,this),t=this,Object.defineProperty(t,"controlsRect",{get:()=>t.$controls.getBoundingClientRect()}),Object.defineProperty(t,"controlsInnerRect",{get:()=>t.$controlsInner.getBoundingClientRect()}),Object.defineProperty(t,"controlsLeftRect",{get:()=>t.$controlsLeft.getBoundingClientRect()}),Object.defineProperty(t,"controlsRightRect",{get:()=>t.$controlsRight.getBoundingClientRect()}),Object.defineProperty(t,"controlsPlaybackTimeInner",{get:()=>t.$playbackTimeInner&&t.$playbackTimeInner.getBoundingClientRect()||{}}),Object.defineProperty(t,"controlsPlaybackBtnsRect",{get:()=>t.$controlsPlaybackBtns&&t.$controlsPlaybackBtns.getBoundingClientRect()||{width:0}}),Ct(e,this),((e,t)=>{const{events:{proxy:i},debug:s}=e;function r(e){const{bottom:i,height:o}=t.$volumePanel.getBoundingClientRect(),{height:s}=t.$volumeHandle.getBoundingClientRect();return ye(i-e.y-s/2,0,o-s/2)/(o-s)}i(window,["click","contextmenu"],(i=>{i.composedPath().indexOf(e.$container)>-1?t.isFocus=!0:t.isFocus=!1})),i(window,"orientationchange",(()=>{setTimeout((()=>{e.resize()}),300)})),i(t.$controls,"click",(e=>{e.stopPropagation()})),i(t.$pause,"click",(t=>{e.pause()})),i(t.$play,"click",(t=>{e.play()})),i(t.$playBig,"click",(t=>{e.play()})),i(t.$volume,"mouseover",(()=>{t.$volumePanelWrap.classList.add("jessibuca-volume-panel-wrap-show")})),i(t.$volume,"mouseout",(()=>{t.$volumePanelWrap.classList.remove("jessibuca-volume-panel-wrap-show")})),i(t.$volumeOn,"click",(i=>{i.stopPropagation(),ve(t.$volumeOn,"display","none"),ve(t.$volumeOff,"display","block"),e.lastVolume=e.volume,e.volume=0})),i(t.$volumeOff,"click",(i=>{i.stopPropagation(),ve(t.$volumeOn,"display","block"),ve(t.$volumeOff,"display","none"),e.volume=e.lastVolume||.5})),i(t.$screenshot,"click",(t=>{t.stopPropagation(),e.video.screenshot()})),i(t.$volumePanel,"click",(t=>{t.stopPropagation(),e.volume=r(t)})),i(t.$volumeHandle,"mousedown",(()=>{t.isVolumeDroging=!0})),i(t.$volumeHandle,"mousemove",(i=>{t.isVolumeDroging&&(e.volume=r(i))})),i(document,"mouseup",(()=>{t.isVolumeDroging&&(t.isVolumeDroging=!1)})),i(t.$record,"click",(t=>{t.stopPropagation(),e.recording=!0})),i(t.$recordStop,"click",(t=>{t.stopPropagation(),e.recording=!1})),i(t.$fullscreen,"click",(t=>{t.stopPropagation(),e.fullscreen=!0})),i(t.$fullscreenExit,"click",(t=>{t.stopPropagation(),e.fullscreen=!1})),e._opt.hasControl&&e._opt.controlAutoHide&&(i(e.$container,"mouseover",(()=>{e.fullscreen||ve(t.$controls,"display","block")})),i(e.$container,"mouseout",(()=>{ve(t.$controls,"display","none")}))),e._opt.playType===o&&(i(t.$playbackNarrow,"click",(t=>{t.stopPropagation(),e.playback&&e.playback.narrowPrecision()})),i(t.$playbackExpand,"click",(t=>{t.stopPropagation(),e.playback&&e.playback.expandPrecision()})),i(t.$playbackTimeList,"click",(t=>{const i=Fe(t);i.matches("div.jessibuca-playback-time-minute-one")&&e.playback&&e.playback.seek(i.dataset)})))})(e,this),e._opt.hotKey&&((e,t)=>{const{events:{proxy:i}}=e,o={};function s(e,t){o[e]?o[e].push(t):o[e]=[t]}s(te,(()=>{e.fullscreen&&(e.fullscreen=!1)})),s(ie,(()=>{e.volume+=.05})),s(oe,(()=>{e.volume-=.05})),i(window,"keydown",(e=>{if(t.isFocus){const t=document.activeElement.tagName.toUpperCase(),i=document.activeElement.getAttribute("contenteditable");if("INPUT"!==t&&"TEXTAREA"!==t&&""!==i&&"true"!==i){const t=o[e.keyCode];t&&(e.preventDefault(),t.forEach((e=>e())))}}}))})(e,this),this.player.debug.log("Control","init")}destroy(){this.$poster&&this.player.$container.removeChild(this.$poster),this.$loading&&this.player.$container.removeChild(this.$loading),this.$controls&&this.player.$container.removeChild(this.$controls),this.$playBig&&this.player.$container.removeChild(this.$playBig),this.player.debug.log("control","destroy")}autoSize(){const e=this.player;e.$container.style.padding="0 0";const t=e.width,i=e.height,o=t/i,s=e.video.$videoElement.width/e.video.$videoElement.height;if(o>s){const o=(t-i*s)/2;e.$container.style.padding=`0 ${o}px`}else{const o=(i-t/s)/2;e.$container.style.padding=`${o}px 0`}}}Rt(".jessibuca-container{position:relative;width:100%;height:100%;overflow:hidden}.jessibuca-container.jessibuca-fullscreen-web{position:fixed;z-index:9999;left:0;top:0;right:0;bottom:0;width:100vw!important;height:100vh!important;background:#000}");class It{static init(){It.types={avc1:[],avcC:[],hvc1:[],hvcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]};for(let e in It.types)It.types.hasOwnProperty(e)&&(It.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);let e=It.constants={};e.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),e.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),e.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSC=e.STCO=e.STTS,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),e.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),e.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}static box(e){let t=8,i=null,o=Array.prototype.slice.call(arguments,1),s=o.length;for(let e=0;e<s;e++)t+=o[e].byteLength;i=new Uint8Array(t),i[0]=t>>>24&255,i[1]=t>>>16&255,i[2]=t>>>8&255,i[3]=255&t,i.set(e,4);let r=8;for(let e=0;e<s;e++)i.set(o[e],r),r+=o[e].byteLength;return i}static generateInitSegment(e){let t=It.box(It.types.ftyp,It.constants.FTYP),i=It.moov(e),o=new Uint8Array(t.byteLength+i.byteLength);return o.set(t,0),o.set(i,t.byteLength),o}static moov(e){let t=It.mvhd(e.timescale,e.duration),i=It.trak(e),o=It.mvex(e);return It.box(It.types.moov,t,i,o)}static mvhd(e,t){return It.box(It.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))}static trak(e){return It.box(It.types.trak,It.tkhd(e),It.mdia(e))}static tkhd(e){let t=e.id,i=e.duration,o=e.presentWidth,s=e.presentHeight;return It.box(It.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,o>>>8&255,255&o,0,0,s>>>8&255,255&s,0,0]))}static mdia(e){return It.box(It.types.mdia,It.mdhd(e),It.hdlr(e),It.minf(e))}static mdhd(e){let t=e.timescale,i=e.duration;return It.box(It.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,i>>>24&255,i>>>16&255,i>>>8&255,255&i,85,196,0,0]))}static hdlr(e){let t=null;return t="audio"===e.type?It.constants.HDLR_AUDIO:It.constants.HDLR_VIDEO,It.box(It.types.hdlr,t)}static minf(e){let t=null;return t="audio"===e.type?It.box(It.types.smhd,It.constants.SMHD):It.box(It.types.vmhd,It.constants.VMHD),It.box(It.types.minf,t,It.dinf(),It.stbl(e))}static dinf(){return It.box(It.types.dinf,It.box(It.types.dref,It.constants.DREF))}static stbl(e){return It.box(It.types.stbl,It.stsd(e),It.box(It.types.stts,It.constants.STTS),It.box(It.types.stsc,It.constants.STSC),It.box(It.types.stsz,It.constants.STSZ),It.box(It.types.stco,It.constants.STCO))}static stsd(e){return"audio"===e.type?It.box(It.types.stsd,It.constants.STSD_PREFIX,It.mp4a(e)):"avc"===e.videoType?It.box(It.types.stsd,It.constants.STSD_PREFIX,It.avc1(e)):It.box(It.types.stsd,It.constants.STSD_PREFIX,It.hvc1(e))}static mp4a(e){let t=e.channelCount,i=e.audioSampleRate,o=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t,0,16,0,0,0,0,i>>>8&255,255&i,0,0]);return It.box(It.types.mp4a,o,It.esds(e))}static esds(e){let t=e.config||[],i=t.length,o=new Uint8Array([0,0,0,0,3,23+i,0,1,0,4,15+i,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([i]).concat(t).concat([6,1,2]));return It.box(It.types.esds,o)}static avc1(e){let t=e.avcc;const i=e.codecWidth,o=e.codecHeight;let s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,i>>>8&255,255&i,o>>>8&255,255&o,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return It.box(It.types.avc1,s,It.box(It.types.avcC,t))}static hvc1(e){let t=e.avcc;const i=e.codecWidth,o=e.codecHeight;let s=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,i>>>8&255,255&i,o>>>8&255,255&o,0,72,0,0,0,72,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return It.box(It.types.hvc1,s,It.box(It.types.hvcC,t))}static mvex(e){return It.box(It.types.mvex,It.trex(e))}static trex(e){let t=e.id,i=new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return It.box(It.types.trex,i)}static moof(e,t){return It.box(It.types.moof,It.mfhd(e.sequenceNumber),It.traf(e,t))}static mfhd(e){let t=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e]);return It.box(It.types.mfhd,t)}static traf(e,t){let i=e.id,o=It.box(It.types.tfhd,new Uint8Array([0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i])),s=It.box(It.types.tfdt,new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t])),r=It.sdtp(e),a=It.trun(e,r.byteLength+16+16+8+16+8+8);return It.box(It.types.traf,o,s,a,r)}static sdtp(e){let t=new Uint8Array(5),i=e.flags;return t[4]=i.isLeading<<6|i.dependsOn<<4|i.isDependedOn<<2|i.hasRedundancy,It.box(It.types.sdtp,t)}static trun(e,t){let i=new Uint8Array(28);t+=36,i.set([0,0,15,1,0,0,0,1,t>>>24&255,t>>>16&255,t>>>8&255,255&t],0);let o=e.duration,s=e.size,r=e.flags,a=e.cts;return i.set([o>>>24&255,o>>>16&255,o>>>8&255,255&o,s>>>24&255,s>>>16&255,s>>>8&255,255&s,r.isLeading<<2|r.dependsOn,r.isDependedOn<<6|r.hasRedundancy<<4|r.isNonSync,0,0,a>>>24&255,a>>>16&255,a>>>8&255,255&a],12),It.box(It.types.trun,i)}static mdat(e){return It.box(It.types.mdat,e)}}It.init();class jt{constructor(e){this.TAG="ExpGolomb",this._buffer=e,this._buffer_index=0,this._total_bytes=e.byteLength,this._total_bits=8*e.byteLength,this._current_word=0,this._current_word_bits_left=0}destroy(){this._buffer=null}_fillCurrentWord(){let e=this._total_bytes-this._buffer_index,t=Math.min(4,e),i=new Uint8Array(4);i.set(this._buffer.subarray(this._buffer_index,this._buffer_index+t)),this._current_word=new DataView(i.buffer).getUint32(0,!1),this._buffer_index+=t,this._current_word_bits_left=8*t}readBits(e){if(e<=this._current_word_bits_left){let t=this._current_word>>>32-e;return this._current_word<<=e,this._current_word_bits_left-=e,t}let t=this._current_word_bits_left?this._current_word:0;t>>>=32-this._current_word_bits_left;let i=e-this._current_word_bits_left;this._fillCurrentWord();let o=Math.min(i,this._current_word_bits_left),s=this._current_word>>>32-o;return this._current_word<<=o,this._current_word_bits_left-=o,t=t<<o|s,t}readBool(){return 1===this.readBits(1)}readByte(){return this.readBits(8)}_skipLeadingZero(){let e;for(e=0;e<this._current_word_bits_left;e++)if(0!=(this._current_word&2147483648>>>e))return this._current_word<<=e,this._current_word_bits_left-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()}readUEG(){let e=this._skipLeadingZero();return this.readBits(e+1)-1}readSEG(){let e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)}}class xt{static _ebsp2rbsp(e){let t=e,i=t.byteLength,o=new Uint8Array(i),s=0;for(let e=0;e<i;e++)e>=2&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(o[s]=t[e],s++);return new Uint8Array(o.buffer,0,s)}static parseSPS(e){let t=xt._ebsp2rbsp(e),i=new jt(t);i.readByte();let o=i.readByte();i.readByte();let s=i.readByte();i.readUEG();let r=xt.getProfileString(o),a=xt.getLevelString(s),n=1,A=420,c=[0,420,422,444],l=8;if((100===o||110===o||122===o||244===o||44===o||83===o||86===o||118===o||128===o||138===o||144===o)&&(n=i.readUEG(),3===n&&i.readBits(1),n<=3&&(A=c[n]),l=i.readUEG()+8,i.readUEG(),i.readBits(1),i.readBool())){let e=3!==n?8:12;for(let t=0;t<e;t++)i.readBool()&&(t<6?xt._skipScalingList(i,16):xt._skipScalingList(i,64))}i.readUEG();let d=i.readUEG();if(0===d)i.readUEG();else if(1===d){i.readBits(1),i.readSEG(),i.readSEG();let e=i.readUEG();for(let t=0;t<e;t++)i.readSEG()}let u=i.readUEG();i.readBits(1);let h=i.readUEG(),p=i.readUEG(),m=i.readBits(1);0===m&&i.readBits(1),i.readBits(1);let f=0,g=0,b=0,y=0;i.readBool()&&(f=i.readUEG(),g=i.readUEG(),b=i.readUEG(),y=i.readUEG());let v=1,w=1,k=0,S=!0,B=0,E=0;if(i.readBool()){if(i.readBool()){let e=i.readByte();e>0&&e<16?(v=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][e-1],w=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][e-1]):255===e&&(v=i.readByte()<<8|i.readByte(),w=i.readByte()<<8|i.readByte())}if(i.readBool()&&i.readBool(),i.readBool()&&(i.readBits(4),i.readBool()&&i.readBits(24)),i.readBool()&&(i.readUEG(),i.readUEG()),i.readBool()){let e=i.readBits(32),t=i.readBits(32);S=i.readBool(),B=t,E=2*e,k=B/E}}let C=1;1===v&&1===w||(C=v/w);let R=0,T=0;if(0===n)R=1,T=2-m;else{R=3===n?1:2,T=(1===n?2:1)*(2-m)}let I=16*(h+1),j=16*(p+1)*(2-m);I-=(f+g)*R,j-=(b+y)*T;let x=Math.ceil(I*C);return i.destroy(),i=null,{profile_string:r,level_string:a,bit_depth:l,ref_frames:u,chroma_format:A,chroma_format_string:xt.getChromaFormatString(A),frame_rate:{fixed:S,fps:k,fps_den:E,fps_num:B},sar_ratio:{width:v,height:w},codec_size:{width:I,height:j},present_size:{width:x,height:j}}}static _skipScalingList(e,t){let i=8,o=8,s=0;for(let r=0;r<t;r++)0!==o&&(s=e.readSEG(),o=(i+s+256)%256),i=0===o?i:o}static getProfileString(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}static getLevelString(e){return(e/10).toFixed(1)}static getChromaFormatString(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}}class Dt{static parseSPS(e){}}class Lt extends Oe{constructor(e){super(),this.player=e,this.isAvc=!0,this.mediaSource=new window.MediaSource,this.sourceBuffer=null,this.hasInit=!1,this.isInitInfo=!1,this.cacheTrack={},this.timeInit=!1,this.sequenceNumber=0,this.mediaSourceOpen=!1,this.bufferList=[],this.dropping=!1,this.player.video.$videoElement.src=window.URL.createObjectURL(this.mediaSource);const{debug:t,events:{proxy:i}}=e;i(this.mediaSource,"sourceopen",(()=>{this.mediaSourceOpen=!0,this.player.emit(R.mseSourceOpen)})),i(this.mediaSource,"sourceclose",(()=>{this.player.emit(R.mseSourceClose)})),e.debug.log("MediaSource","init")}destroy(){this.stop(),this.bufferList=[],this.mediaSource=null,this.mediaSourceOpen=!1,this.sourceBuffer=null,this.hasInit=!1,this.isInitInfo=!1,this.sequenceNumber=0,this.cacheTrack=null,this.timeInit=!1,this.off(),this.player.debug.log("MediaSource","destroy")}get state(){return this.mediaSource.readyState}get isStateOpen(){return this.state===K}get isStateClosed(){return this.state===_}get isStateEnded(){return this.state===Z}get duration(){return this.mediaSource.duration}set duration(e){this.mediaSource.duration=e}decodeVideo(e,t,i){const o=this.player;if(this.hasInit)this._decodeVideo(e,t,i);else if(i&&0===e[1]){const s=15&e[0];if(o.video.updateVideoInfo({encTypeCode:s}),s===U)return void this.emit(I.mediaSourceH265NotSupport);o._times.decodeStart||(o._times.decodeStart=be()),this._decodeConfigurationRecord(e,t,i,s),this.hasInit=!0}}_doDecode(){const e=this.bufferList.shift();e&&this._decodeVideo(e.payload,e.ts,e.isIframe)}_decodeConfigurationRecord(e,t,i,o){let s=e.slice(5),r={};o===O?r=function(e){const t={},i=new DataView(e.buffer);let o=i.getUint8(0),s=i.getUint8(1);if(i.getUint8(2),i.getUint8(3),1!==o||0===s)return;const r=1+(3&i.getUint8(4));if(3!==r&&4!==r)return;let a=31&i.getUint8(5);if(0===a)return;let n=6;for(let o=0;o<a;o++){let s=i.getUint16(n,!1);if(n+=2,0===s)continue;let r=new Uint8Array(e.buffer,n,s);n+=s;let a=xt.parseSPS(r);if(0!==o)continue;t.codecWidth=a.codec_size.width,t.codecHeight=a.codec_size.height,t.presentWidth=a.present_size.width,t.presentHeight=a.present_size.height,t.profile=a.profile_string,t.level=a.level_string,t.bitDepth=a.bit_depth,t.chromaFormat=a.chroma_format,t.sarRatio=a.sar_ratio,t.frameRate=a.frame_rate,!1!==a.frame_rate.fixed&&0!==a.frame_rate.fps_num&&0!==a.frame_rate.fps_den||(t.frameRate={});let A=t.frameRate.fps_den,c=t.frameRate.fps_num;t.refSampleDuration=t.timescale*(A/c);let l=r.subarray(1,4),d="avc1.";for(let e=0;e<3;e++){let t=l[e].toString(16);t.length<2&&(t="0"+t),d+=t}t.codec=d}let A=i.getUint8(n);if(0!==A){n++;for(let t=0;t<A;t++){let t=i.getUint16(n,!1);n+=2,0!==t&&(new Uint8Array(e.buffer,n,t),n+=t)}return t.videoType="avc",t}}(s):o===U&&(r=function(e){const t={videoType:"hevc"};let i=23;if(e[i]!==P.vps)return t;i+=2,i+=1;const o=e[i+1]|e[i]<<8;i+=2;const s=e.slice(i,i+o);if(console.log(Uint8Array.from(s)),i+=o,e[i]!==P.sps)return t;i+=2,i+=1;const r=e[i+1]|e[i]<<8;i+=2;const a=e.slice(i,i+r);if(console.log(Uint8Array.from(a)),i+=r,e[i]!==P.pps)return t;i+=2,i+=1;const n=e[i+1]|e[i]<<8;i+=2;const A=e.slice(i,i+n);console.log(Uint8Array.from(A));let c=Uint8Array.from(a),l=Dt.parseSPS(c);return t.codecWidth=l.codec_size.width,t.codecHeight=l.codec_size.height,t.presentWidth=l.present_size.width,t.presentHeight=l.present_size.height,t.profile=l.profile_string,t.level=l.level_string,t.bitDepth=l.bit_depth,t.chromaFormat=l.chroma_format,t.sarRatio=l.sar_ratio,t}(s));const a={id:1,type:"video",timescale:1e3,duration:0,avcc:s,codecWidth:r.codecWidth,codecHeight:r.codecHeight,videoType:r.videoType},n=It.generateInitSegment(a);this.isAvc=!0,this.appendBuffer(n.buffer),this.sequenceNumber=0,this.cacheTrack=null,this.timeInit=!1}_decodeVideo(e,t,i){const o=this.player;let s=e.slice(5),r=s.byteLength,a=t;const n=o.video.$videoElement;if(n.buffered.length>1&&(this.removeBuffer(n.buffered.start(0),n.buffered.end(0)),this.timeInit=!1),this.dropping&&a-this.cacheTrack.dts>1e3)this.dropping=!1,this.cacheTrack={};else if(this.cacheTrack&&a>this.cacheTrack.dts){let e=8+this.cacheTrack.size,i=new Uint8Array(e);i[0]=e>>>24&255,i[1]=e>>>16&255,i[2]=e>>>8&255,i[3]=255&e,i.set(It.types.mdat,4),i.set(this.cacheTrack.data,8),this.cacheTrack.duration=a-this.cacheTrack.dts;let s=It.moof(this.cacheTrack,this.cacheTrack.dts),r=new Uint8Array(s.byteLength+i.byteLength);r.set(s,0),r.set(i,s.byteLength),this.appendBuffer(r.buffer),o.handleRender(),o.updateStats({fps:!0,ts:t,buf:o.demux.delay}),o._times.videoStart||(o._times.videoStart=be(),o.handlePlayToRenderTimes())}else o.debug.log("MediaSource","timeInit set false , cacheTrack = {}"),this.timeInit=!1,this.cacheTrack={};this.cacheTrack.id=1,this.cacheTrack.sequenceNumber=++this.sequenceNumber,this.cacheTrack.size=r,this.cacheTrack.dts=a,this.cacheTrack.cts=0,this.cacheTrack.isKeyframe=i,this.cacheTrack.data=s,this.cacheTrack.flags={isLeading:0,dependsOn:i?2:1,isDependedOn:i?1:0,hasRedundancy:0,isNonSync:i?0:1},this.timeInit||1!==n.buffered.length||(o.debug.log("MediaSource","timeInit set true"),this.timeInit=!0,n.currentTime=n.buffered.end(0)),!this.isInitInfo&&n.videoWidth>0&&n.videoHeight>0&&(o.debug.log("MediaSource",`updateVideoInfo: ${n.videoWidth},${n.videoHeight}`),o.video.updateVideoInfo({width:n.videoWidth,height:n.videoHeight}),o.video.initCanvasViewSize(),this.isInitInfo=!0)}appendBuffer(e){const{debug:t,events:{proxy:i}}=this.player;null===this.sourceBuffer&&(this.sourceBuffer=this.mediaSource.addSourceBuffer(X.avc),i(this.sourceBuffer,"error",(e=>{this.player.emit(R.mseSourceBufferError,e)}))),!1===this.sourceBuffer.updating&&this.isStateOpen?this.sourceBuffer.appendBuffer(e):this.isStateClosed?this.player.emit(R.mseSourceBufferError,"mediaSource is not attached to video or mediaSource is closed"):this.isStateEnded?this.player.emit(R.mseSourceBufferError,"mediaSource is closed"):!0===this.sourceBuffer.updating&&this.player.emit(R.mseSourceBufferBusy)}stop(){this.isStateOpen&&this.sourceBuffer&&this.sourceBuffer.abort(),this.endOfStream()}dropSourceBuffer(e){const t=this.player.video.$videoElement;this.dropping=e,t.buffered.length>0&&t.buffered.end(0)-t.currentTime>1&&(t.currentTime=t.buffered.end(0))}removeBuffer(e,t){if(this.isStateOpen&&!1===this.sourceBuffer.updating)try{this.sourceBuffer.remove(e,t)}catch(e){console.error(e)}}endOfStream(){this.isStateOpen&&this.mediaSource.endOfStream()}}const Mt=()=>"undefined"!=typeof navigator&&parseFloat((""+(/CPU.*OS ([0-9_]{3,4})[0-9_]{0,1}|(CPU like).*AppleWebKit.*Mobile/i.exec(navigator.userAgent)||[0,""])[1]).replace("undefined","3_2").replace("_",".").replace("_",""))<10&&!window.MSStream,Ft=()=>"wakeLock"in navigator;class Ot{constructor(e){if(this.player=e,this.enabled=!1,Ft()){this._wakeLock=null;const e=()=>{null!==this._wakeLock&&"visible"===document.visibilityState&&this.enable()};document.addEventListener("visibilitychange",e),document.addEventListener("fullscreenchange",e)}else Mt()?this.noSleepTimer=null:(this.noSleepVideo=document.createElement("video"),this.noSleepVideo.setAttribute("title","No Sleep"),this.noSleepVideo.setAttribute("playsinline",""),this._addSourceToVideo(this.noSleepVideo,"webm","data:video/webm;base64,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"),this._addSourceToVideo(this.noSleepVideo,"mp4","data:video/mp4;base64,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"),this.noSleepVideo.addEventListener("loadedmetadata",(()=>{this.noSleepVideo.duration<=1?this.noSleepVideo.setAttribute("loop",""):this.noSleepVideo.addEventListener("timeupdate",(()=>{this.noSleepVideo.currentTime>.5&&(this.noSleepVideo.currentTime=Math.random())}))})))}_addSourceToVideo(e,t,i){var o=document.createElement("source");o.src=i,o.type=`video/${t}`,e.appendChild(o)}get isEnabled(){return this.enabled}enable(){const e=this.player.debug;if(Ft())return navigator.wakeLock.request("screen").then((t=>{this._wakeLock=t,this.enabled=!0,e.log("wakeLock","Wake Lock active."),this._wakeLock.addEventListener("release",(()=>{e.log("wakeLock","Wake Lock released.")}))})).catch((t=>{throw this.enabled=!1,e.error("wakeLock",`${t.name}, ${t.message}`),t}));if(Mt())return this.disable(),this.noSleepTimer=window.setInterval((()=>{document.hidden||(window.location.href=window.location.href.split("#")[0],window.setTimeout(window.stop,0))}),15e3),this.enabled=!0,Promise.resolve();return this.noSleepVideo.play().then((e=>(this.enabled=!0,e))).catch((e=>{throw this.enabled=!1,e}))}disable(){const e=this.player.debug;Ft()?(this._wakeLock&&this._wakeLock.release(),this._wakeLock=null):Mt()?this.noSleepTimer&&(e.warn("wakeLock","NoSleep now disabled for older iOS devices."),window.clearInterval(this.noSleepTimer),this.noSleepTimer=null):this.noSleepVideo.pause(),this.enabled=!1}}class Ut extends Oe{constructor(e,t){super(),this.player=e,this._showPrecision=null,this._startTime=null,this._playStartTime=null,this._playingTimestamp=null,this._fps=parseInt(t.fps,10)||e._opt.playbackFps,this._audioTimestamp=0,this._videoTimestamp=0,this._listen(),this.playbackList=[],this._totalDuration=0,this.initPlaybackList(t.playList),e.debug.log("Playback","init")}_listen(){this.player.on(R.stats,(e=>{const t=e.ts;this._playStartTime||(this._playStartTime=t);let i=t-this._playStartTime;this.setPlayingTimestamp(i)}))}destroy(){this._startTime=null,this._showPrecision=null,this._playStartTime=null,this._playingTimestamp=null,this._totalDuration=0,this._audioTimestamp=0,this._videoTimestamp=0,this.playbackList=[],this.off(),this.player.debug.log("Playback","destroy")}initPlaybackList(e){this.playbackList=e||[];let t=0;this.playbackList.forEach(((e,i)=>{10===Me(e.start)&&(e.startTimestamp=1e3*e.start,e.startTime=Ce(e.startTimestamp)),10===Me(e.end)&&(e.endTimestamp=1e3*e.end,e.endTime=Ce(e.endTimestamp)),e.duration=e.end-e.start,t+=e.duration})),this._totalDuration=t,this.player.debug.log("Playback",this.playbackList),this.playbackList.length>0&&this.setStartTime(this.playbackList[0].startTimestamp),this.setShowPrecision(se)}get startTime(){return this._startTime||0}setStartTime(e){this._startTime=e,this._playStartTime=null,this.player._opt.playbackCurrentTimeMove||(this._playingTimestamp=this.startTime,this.player.emit(R.playbackTime,this.startTime))}get fps(){return this._fps}get showPrecision(){return this._showPrecision}get is60Min(){return this.showPrecision===se}get is30Min(){return this.showPrecision===re}get is10Min(){return this.showPrecision===ae}get is5Min(){return this.showPrecision===ne}get is1Min(){return this.showPrecision===ne}setShowPrecision(e){this._showPrecision&&this._showPrecision===e||(this._showPrecision=e,this.player.emit(R.playbackPrecision,this._showPrecision,this.playbackList))}setPlayingTimestamp(e){const t=this.startTime+e;this.player._opt.playbackCurrentTimeMove&&(this._playingTimestamp=t,this.player.emit(R.playbackTime,t));const i=new Date(t);this.player.emit(R.playbackTimestamp,{ts:t,hour:i.getHours(),min:i.getMinutes(),second:i.getSeconds()})}get playingTimestamp(){return this._playingTimestamp}narrowPrecision(){const e=ce.indexOf(this.showPrecision)-1;if(e>=0){const t=ce[e];this.setShowPrecision(t)}}expandPrecision(){const e=ce.indexOf(this.showPrecision)+1;if(e<=ce.length-1){const t=ce[e];this.setShowPrecision(t)}}seek(e){if(console.log(e),"true"===e.hasRecord){let t=e.time;"min"===e.type&&(t=60*e.time),this.player.emit(R.playbackSeek,function(e){let t={};e>-1&&(t={hour:Math.floor(e/60/60)%60,min:Math.floor(e/60)%60,second:e%60});return t}(t))}}}class Vt extends Oe{constructor(e,t){var i;super(),this.$container=e,this._opt=Object.assign({},n,t),this.debug=new le(this),this._opt.useWCS&&(this._opt.useWCS="VideoEncoder"in window),this._opt.useMSE&&(this._opt.useMSE=window.MediaSource&&window.MediaSource.isTypeSupported(X.avc)),this._opt.useMSE?(this._opt.useWCS&&this.debug.log("Player","useWCS set true->false"),this._opt.forceNoOffscreen||this.debug.log("Player","forceNoOffscreen set false->true"),this._opt.useWCS=!1,this._opt.forceNoOffscreen=!0):this._opt.useWCS,this._opt.forceNoOffscreen||("undefined"==typeof OffscreenCanvas?(this._opt.forceNoOffscreen=!0,this._opt.useOffscreen=!1):this._opt.useOffscreen=!0),this._opt.hasAudio||(this._opt.operateBtns.audio=!1),this._opt.hasControl=this._hasControl(),this._loading=!1,this._playing=!1,this._hasLoaded=!1,this._checkHeartTimeout=null,this._checkLoadingTimeout=null,this._startBpsTime=null,this._isPlayingBeforePageHidden=!1,this._stats={buf:0,fps:0,abps:0,vbps:0,ts:0},this._times={playInitStart:"",playStart:"",streamStart:"",streamResponse:"",demuxStart:"",decodeStart:"",videoStart:"",playTimestamp:"",streamTimestamp:"",streamResponseTimestamp:"",demuxTimestamp:"",decodeTimestamp:"",videoTimestamp:"",allTimestamp:""},this._videoTimestamp=0,this._audioTimestamp=0,this._isPlayback()&&(this._opt.useMSE=!1,this._opt.useWCS=!1),i=this,Object.defineProperty(i,"rect",{get:()=>{const e=i.$container.getBoundingClientRect();return e.width=Math.max(e.width,i.$container.clientWidth),e.height=Math.max(e.height,i.$container.clientHeight),e}}),["bottom","height","left","right","top","width"].forEach((e=>{Object.defineProperty(i,e,{get:()=>i.rect[e]})})),this.events=new de(this),this.video=new Ne(this),this._opt.hasAudio&&(this.audio=new rt(this)),this.recorder=new dt(this),this._onlyMseOrWcsVideo()?this.loaded=!0:this.decoderWorker=new ut(this),this.stream=null,this.demux=null,this._opt.useWCS&&(this.webcodecsDecoder=new gt(this)),this._opt.useMSE&&(this.mseDecoder=new Lt(this)),this.control=new Tt(this),this._isPlayback()&&(this.playback=new Ut(this,this._opt.playbackConfig||{}),this.$container.classList.add("jessibuca-container-playback")),this.keepScreenOn=new Ot(this),(e=>{try{const t=()=>{e.emit(T.fullscreen,e.fullscreen),e.fullscreen?e._opt.useMSE&&e.resize():e.resize()};pe.on("change",t),e.events.destroys.push((()=>{pe.off("change",t)}))}catch(e){}if(e.on(R.decoderWorkerInit,(()=>{e.debug.log("player","has loaded"),e.loaded=!0})),e.on(R.play,(()=>{e.loading=!1})),e.on(R.fullscreen,(t=>{if(t)try{pe.request(e.$container).then((()=>{})).catch((t=>{e.webFullscreen=!0}))}catch(t){e.webFullscreen=!0}else try{pe.exit().then((()=>{})).catch((()=>{e.webFullscreen=!1}))}catch(t){e.webFullscreen=!1}})),e.on(R.webFullscreen,(t=>{t?e.$container.classList.add("jessibuca-fullscreen-web"):e.$container.classList.remove("jessibuca-fullscreen-web")})),e.on(R.resize,(()=>{e.video.resize()})),e._opt.debug){const t=[R.timeUpdate];Object.keys(R).forEach((i=>{e.on(R[i],(o=>{t.includes(i)||e.debug.log("player events",R[i],o)}))})),Object.keys(I).forEach((t=>{e.on(I[t],(i=>{e.debug.log("player event error",I[t],i)}))}))}})(this),(e=>{const{_opt:t,debug:i,events:{proxy:o}}=e;t.supportDblclickFullscreen&&o(e.$container,"dblclick",(t=>{const i=Fe(t).nodeName.toLowerCase();"canvas"!==i&&"video"!==i||(e.fullscreen=!e.fullscreen)})),o(document,"visibilitychange",(()=>{t.hiddenAutoPause&&(i.log("visibilitychange",document.visibilityState,e._isPlayingBeforePageHidden),"visible"===document.visibilityState?e._isPlayingBeforePageHidden&&e.play():(e._isPlayingBeforePageHidden=e.playing,e.playing&&e.pause()))})),o(window,"fullscreenchange",(()=>{null!==e.keepScreenOn&&"visible"===document.visibilityState&&e.enableWakeLock()}))})(this),this._opt.useWCS&&this.debug.log("Player","use WCS"),this._opt.useMSE&&this.debug.log("Player","use MSE"),this._opt.useOffscreen&&this.debug.log("Player","use offscreen"),this._isPlayback()&&this.debug.log("Player","use playback"),this.debug.log("Player options",this._opt)}destroy(){this._loading=!1,this._playing=!1,this._hasLoaded=!1,this._times={playInitStart:"",playStart:"",streamStart:"",streamResponse:"",demuxStart:"",decodeStart:"",videoStart:"",playTimestamp:"",streamTimestamp:"",streamResponseTimestamp:"",demuxTimestamp:"",decodeTimestamp:"",videoTimestamp:"",allTimestamp:""},this.decoderWorker&&(this.decoderWorker.destroy(),this.decoderWorker=null),this.video&&(this.video.destroy(),this.video=null),this.audio&&(this.audio.destroy(),this.audio=null),this.stream&&(this.stream.destroy(),this.stream=null),this.recorder&&(this.recorder.destroy(),this.recorder=null),this.control&&(this.control.destroy(),this.control=null),this.webcodecsDecoder&&(this.webcodecsDecoder.destroy(),this.webcodecsDecoder=null),this.mseDecoder&&(this.mseDecoder.destroy(),this.mseDecoder=null),this.demux&&(this.demux.destroy(),this.demux=null),this.events&&(this.events.destroy(),this.events=null),this.playback&&(this.playback.destroy(),this.playback=null),this.clearCheckHeartTimeout(),this.clearCheckLoadingTimeout(),this.releaseWakeLock(),this.keepScreenOn=null,this.resetStats(),this._audioTimestamp=0,this._videoTimestamp=0,this.emit("destroy"),this.off(),this.debug.log("play","destroy end")}set fullscreen(e){Be()?(this.emit(R.webFullscreen,e),setTimeout((()=>{this.updateOption({rotate:e?270:0}),this.resize()}),10)):this.emit(R.fullscreen,e)}get fullscreen(){return document.isFullScreen||document.mozIsFullScreen||document.webkitIsFullScreen||this.webFullscreen}set webFullscreen(e){this.emit(R.webFullscreen,e)}get webFullscreen(){return this.$container.classList.contains("jessibuca-fullscreen-web")}set loaded(e){this._hasLoaded=e}get loaded(){return this._hasLoaded}set playing(e){e&&(this.loading=!1),this.playing!==e&&(this._playing=e,this.emit(R.playing,e),this.emit(R.volumechange,this.volume),e?this.emit(R.play):this.emit(R.pause))}get playing(){return this._playing}get volume(){return this.audio&&this.audio.volume||0}set volume(e){this.audio&&this.audio.setVolume(e)}set loading(e){this.loading!==e&&(this._loading=e,this.emit(R.loading,this._loading))}get loading(){return this._loading}set recording(e){e?this.playing&&this.recorder.startRecord():this.recorder.stopRecordAndSave()}get recording(){return this.recorder&&this.recorder.recording}set audioTimestamp(e){null!==e&&(this._audioTimestamp=e)}get audioTimestamp(){return this._audioTimestamp}set videoTimestamp(e){null!==e&&(this._videoTimestamp=e,this._opt.useWCS||this._opt.useMSE||this.audioTimestamp&&this.videoTimestamp&&this.audio&&this.audio.emit(R.videoSyncAudio,{audioTimestamp:this.audioTimestamp,videoTimestamp:this.videoTimestamp,diff:this.audioTimestamp-this.videoTimestamp}))}get videoTimestamp(){return this._videoTimestamp}updateOption(e){this._opt=Object.assign({},this._opt,e)}init(){return new Promise(((e,t)=>{this.stream||(this.stream=new At(this)),this.demux||(this.demux=new ft(this)),this._opt.useWCS&&(this.webcodecsDecoder||(this.webcodecsDecoder=new gt(this))),this._opt.useMSE&&(this.mseDecoder||(this.mseDecoder=new Lt(this))),this.decoderWorker||this._onlyMseOrWcsVideo()?e():(this.decoderWorker=new ut(this),this.once(R.decoderWorkerInit,(()=>{e()})))}))}play(e){return new Promise(((t,i)=>{if(!e&&!this._opt.url)return i();this.loading=!0,this.playing=!1,this._times.playInitStart=be(),e||(e=this._opt.url),this._opt.url=e,this.clearCheckHeartTimeout(),this.init().then((()=>{this._times.playStart=be(),this._opt.isNotMute&&this.mute(!1),this.webcodecsDecoder&&this.webcodecsDecoder.once(I.webcodecsH265NotSupport,(()=>{this.emit(I.webcodecsH265NotSupport),this._opt.autoWasm||this.emit(R.error,I.webcodecsH265NotSupport)})),this.mseDecoder&&this.mseDecoder.once(I.mediaSourceH265NotSupport,(()=>{this.emit(I.mediaSourceH265NotSupport),this._opt.autoWasm||this.emit(R.error,I.mediaSourceH265NotSupport)})),this.enableWakeLock(),this.stream.fetchStream(e),this.checkLoadingTimeout(),this.stream.once(I.fetchError,(e=>{i(e)})),this.stream.once(I.websocketError,(e=>{i(e)})),this.stream.once(R.streamSuccess,(()=>{t(),this._times.streamResponse=be(),this.video.play()}))})).catch((e=>{i(e)}))}))}close(){return new Promise(((e,t)=>{this._close().then((()=>{this.video.clearView(),e()}))}))}_close(){return new Promise(((e,t)=>{this.stream&&(this.stream.destroy(),this.stream=null),this.demux&&(this.demux.destroy(),this.demux=null),this.decoderWorker&&(this.decoderWorker.destroy(),this.decoderWorker=null),this.webcodecsDecoder&&(this.webcodecsDecoder.destroy(),this.webcodecsDecoder=null),this.mseDecoder&&(this.mseDecoder.destroy(),this.mseDecoder=null),this.clearCheckHeartTimeout(),this.clearCheckLoadingTimeout(),this.playing=!1,this.loading=!1,this.recording=!1,this.audio&&this.audio.pause(),this.video&&this.video.pause(),this.releaseWakeLock(),this.resetStats(),this._audioTimestamp=0,this._videoTimestamp=0,this._times={playInitStart:"",playStart:"",streamStart:"",streamResponse:"",demuxStart:"",decodeStart:"",videoStart:"",playTimestamp:"",streamTimestamp:"",streamResponseTimestamp:"",demuxTimestamp:"",decodeTimestamp:"",videoTimestamp:"",allTimestamp:""},setTimeout((()=>{e()}),0)}))}pause(e){return e?this.close():this._close()}mute(e){this.audio&&this.audio.mute(e)}resize(){this.video.resize()}startRecord(e,t){this.recording||(this.recorder.setFileName(e,t),this.recording=!0)}stopRecordAndSave(){this.recording&&(this.recording=!1)}_hasControl(){let e=!1,t=!1;return Object.keys(this._opt.operateBtns).forEach((e=>{this._opt.operateBtns[e]&&(t=!0)})),(this._opt.showBandwidth||this._opt.text||t)&&(e=!0),this._isPlayback()&&(e=!0),e}_isPlayback(){return this._opt.playType===o}_onlyMseOrWcsVideo(){return!1===this._opt.hasAudio&&(this._opt.useMSE||this._opt.useWCS&&!this._opt.useOffscreen)}checkHeart(){this.clearCheckHeartTimeout(),this.checkHeartTimeout()}checkHeartTimeout(){this._checkHeartTimeout=setTimeout((()=>{this.pause(!1).then((()=>{this.emit(R.timeout,R.delayTimeout),this.emit(R.delayTimeout)}))}),1e3*this._opt.heartTimeout)}clearCheckHeartTimeout(){this._checkHeartTimeout&&(clearTimeout(this._checkHeartTimeout),this._checkHeartTimeout=null)}checkLoadingTimeout(){this._checkLoadingTimeout=setTimeout((()=>{this.pause(!1).then((()=>{this.emit(R.timeout,R.loadingTimeout),this.emit(R.loadingTimeout)}))}),1e3*this._opt.loadingTimeout)}clearCheckLoadingTimeout(){this._checkLoadingTimeout&&(clearTimeout(this._checkLoadingTimeout),this._checkLoadingTimeout=null)}handleRender(){this.loading&&(this.emit(R.start),this.loading=!1,this.clearCheckLoadingTimeout()),this.playing||(this.playing=!0),this.checkHeart()}updateStats(e){e=e||{},this._startBpsTime||(this._startBpsTime=be()),Ie(e.ts)&&(this._stats.ts=e.ts),Ie(e.buf)&&(this._stats.buf=e.buf),e.fps&&(this._stats.fps+=1),e.abps&&(this._stats.abps+=e.abps),e.vbps&&(this._stats.vbps+=e.vbps);const t=be();t-this._startBpsTime<1e3||(this.emit(R.stats,this._stats),this.emit(R.performance,function(e){let t=0;return e>=24?t=2:e>=15&&(t=1),t}(this._stats.fps)),this._stats.fps=0,this._stats.abps=0,this._stats.vbps=0,this._startBpsTime=t)}resetStats(){this._startBpsTime=null,this._stats={buf:0,fps:0,abps:0,vbps:0,ts:0}}enableWakeLock(){this._opt.keepScreenOn&&this.keepScreenOn.enable()}releaseWakeLock(){this._opt.keepScreenOn&&this.keepScreenOn.disable()}handlePlayToRenderTimes(){const e=this._times;e.playTimestamp=e.playStart-e.playInitStart,e.streamTimestamp=e.streamStart-e.playStart,e.streamResponseTimestamp=e.streamResponse-e.streamStart,e.demuxTimestamp=e.demuxStart-e.streamResponse,e.decodeTimestamp=e.decodeStart-e.demuxStart,e.videoTimestamp=e.videoStart-e.decodeStart,e.allTimestamp=e.videoStart-e.playInitStart,this.emit(R.playToRenderTimes,e)}}class Pt extends Oe{constructor(e){super();let t=e,i=e.container;if("string"==typeof e.container&&(i=document.querySelector(e.container)),!i)throw new Error("Jessibuca need container option");i.classList.add("jessibuca-container"),delete t.container,Ie(t.videoBuffer)&&(t.videoBuffer=1e3*Number(t.videoBuffer)),Ie(t.timeout)&&(Te(t.loadingTimeout)&&(t.loadingTimeout=t.timeout),Te(t.heartTimeout)&&(t.heartTimeout=t.timeout)),this._opt=t,this.$container=i,this._loadingTimeoutReplayTimes=0,this._heartTimeoutReplayTimes=0,this.events=new de(this)}destroy(){this.events&&(this.events.destroy(),this.events=null),this.player&&(this.player.destroy(),this.player=null),this.$container=null,this._resetOpt(),this._loadingTimeoutReplayTimes=0,this._heartTimeoutReplayTimes=0,this.off()}_resetOpt(){this._opt=a}_initPlayer(e,t){this.player=new Vt(e,t),this._bindEvents()}_resetPlayer(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.player&&(this.player.destroy(),this.player=null),this._opt=Object.assign(this._opt,e)}_bindEvents(){Object.keys(T).forEach((e=>{this.player.on(T[e],(t=>{this.emit(e,t)}))}))}setDebug(e){this.player.updateOption({isDebug:!!e})}mute(){this.player.mute(!0)}cancelMute(){this.player.mute(!1)}setVolume(e){this.player.volume=e}audioResume(){this.player.audio&&this.player.audio.audioEnabled(!0)}setTimeout(e){e=Number(e),this.player.updateOption({timeout:e,loadingTimeout:e,heartTimeout:e})}setScaleMode(e){let t={isFullResize:!1,isResize:!1};switch(e=Number(e)){case Q:t.isFullResize=!1,t.isResize=!1;break;case W:t.isFullResize=!1,t.isResize=!0;break;case N:t.isFullResize=!0,t.isResize=!0}this.player.updateOption(t),this.resize()}pause(){return this.player.pause()}close(){return this._resetOpt(),this.player.close()}clearView(){this.player.video.clearView()}play(e){return new Promise(((t,i)=>{if(!e&&!this._opt.url)return this.emit(R.error,I.playError),void i();if(!this.player)return e?this._play(e):this._play(this._opt.url);if(e){if(!this._opt.url)return this._play(e);e===this._opt.url?this.player.playing?t():(this.clearView(),this.player.play(this._opt.url).then((()=>{t()})).catch((()=>{this.player.pause().then((()=>{i()}))}))):this.player.pause().then((()=>(this.clearView(),this._play(e)))).catch((()=>{i()}))}else this.player.play(this._opt.url).then((()=>{t()})).catch((()=>{this.player.pause().then((()=>{i()}))}))}))}playback(e,t){return this._resetPlayer({playbackConfig:t,playType:o}),this.play(e)}forward(e){return new Promise(((t,i)=>{this.player.playing?(e=ye(Number(e),.1,8),this.player.video.setRate(e),this.player.audio.setRate(e),t()):i()}))}normal(){return new Promise(((e,t)=>{this.player.playing?(this.player.video.setRate(1),this.player.audio.setRate(1),e()):t()}))}updatePlaybackForwardMaxRateDecodeIFrame(e){e=ye(Number(e),1,8),this.player?this.player.updateOption({playbackForwardMaxRateDecodeIFrame:e}):this._opt.playbackForwardMaxRateDecodeIFrame=e}setPlaybackStartTime(e){const t=Me(e);t<10||this.player.playing&&(10===t&&(e*=1e3),this.player.video.clear(),this.player.audio.clear(),this.player.playback.setStartTime(e))}_play(i){return new Promise(((o,a)=>{this._opt.url=i;const n=0===i.indexOf("http"),A=n?t:e,c=n||-1!==i.indexOf(".flv")||this._opt.isFlv?s:r;this._initPlayer(this.$container,Object.assign(this._opt,{protocol:A,demuxType:c})),this.player.once(I.mediaSourceH265NotSupport,(()=>{this.close().then((()=>{this.player._opt.autoWasm&&(this.player.debug.log("Jessibuca","auto wasm [mse-> wasm] reset player and play"),this._resetPlayer({useMSE:!1}),this.play(i).then((()=>{this.player.debug.log("Jessibuca","auto wasm [mse-> wasm] reset player and play success")})).catch((()=>{this.player.debug.log("Jessibuca","auto wasm [mse-> wasm] reset player and play error")})))}))})),this.player.once(I.webcodecsH265NotSupport,(()=>{this.close().then((()=>{this.player._opt.autoWasm&&(this.player.debug.log("Jessibuca","auto wasm [wcs-> wasm] reset player and play"),this._resetPlayer({useWCS:!1}),this.play(i).then((()=>{this.player.debug.log("Jessibuca","auto wasm [wcs-> wasm] reset player and play success")})).catch((()=>{this.player.debug.log("Jessibuca","auto wasm [wcs-> wasm] reset player and play error")})))}))})),this.player.once(I.wasmDecodeError,(()=>{this.player._opt.wasmDecodeErrorReplay&&this.close().then((()=>{this.player.debug.log("Jessibuca","wasm decode error and reset player and play"),this._resetPlayer({useWCS:!1}),this.play(i).then((()=>{this.player.debug.log("Jessibuca","wasm decode error and reset player and play success")})).catch((()=>{this.player.debug.log("Jessibuca","wasm decode error and reset player and play error")}))}))})),this.player.once(R.delayTimeout,(()=>{this.player._opt.heartTimeoutReplay&&this._heartTimeoutReplayTimes<this.player._opt.heartTimeoutReplayTimes&&(this._heartTimeoutReplayTimes+=1,this.play(i).then((()=>{this._heartTimeoutReplayTimes=0})).catch((()=>{})))})),this.player.once(R.loadingTimeout,(()=>{this.player._opt.loadingTimeoutReplay&&this._loadingTimeoutReplayTimes<this.player._opt.loadingTimeoutReplayTimes&&(this._loadingTimeoutReplayTimes+=1,this.play(i).then((()=>{this._loadingTimeoutReplayTimes=0})).catch((()=>{})))})),this.hasLoaded()?this.player.play(i).then((()=>{o()})).catch((()=>{this.player.pause().then((()=>{a()}))})):this.player.once(R.decoderWorkerInit,(()=>{this.player.play(i).then((()=>{o()})).catch((()=>{this.player.pause().then((()=>{a()}))}))}))}))}resize(){this.player.resize()}setBufferTime(e){e=Number(e),this.player.updateOption({videoBuffer:1e3*e}),this.player.decoderWorker&&this.player.decoderWorker.updateWorkConfig({key:"videoBuffer",value:1e3*e})}setRotate(e){e=parseInt(e,10);this._opt.rotate!==e&&-1!==[0,90,270].indexOf(e)&&(this.player.updateOption({rotate:e}),this.resize())}hasLoaded(){return this.player.loaded}setKeepScreenOn(){this.player.updateOption({keepScreenOn:!0})}setFullscreen(e){const t=!!e;this.player.fullscreen!==t&&(this.player.fullscreen=t)}screenshot(e,t,i,o){return this.player.video.screenshot(e,t,i,o)}startRecord(e,t){return new Promise(((i,o)=>{this.player.playing?(this.player.startRecord(e,t),i()):o()}))}stopRecordAndSave(){this.player.recording&&this.player.stopRecordAndSave()}isPlaying(){return this.player.playing}isMute(){return!this.player.audio||this.player.audio.isMute}isRecording(){return this.player.recorder.recording}}return window.Jessibuca=Pt,Pt}));
