!function(t){"function"==typeof define&&define.amd?define(t):t()}(function(){"use strict";class t{on(t,e,i){var s=this.e||(this.e={});return(s[t]||(s[t]=[])).push({fn:e,ctx:i}),this}once(s,r,a){const n=this;function o(){n.off(s,o);for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];r.apply(a,e)}return o._=r,this.on(s,o,a)}emit(t){for(var e=((this.e||(this.e={}))[t]||[]).slice(),i=arguments.length,s=new Array(1<i?i-1:0),r=1;r<i;r++)s[r-1]=arguments[r];for(let t=0;t<e.length;t+=1)e[t].fn.apply(e[t].ctx,s);return this}off(t,i){const e=this.e||(this.e={});if(t){var s=e[t],r=[];if(s&&i)for(let t=0,e=s.length;t<e;t+=1)s[t].fn!==i&&s[t].fn._!==i&&r.push(s[t]);return r.length?e[t]=r:delete e[t],this}Object.keys(e).forEach(t=>{delete e[t]}),delete this.e}}const r="debug",n="warn",s="talkGetUserMediaSuccess",a="talkGetUserMediaFail",e="talkGetUserMediaTimeout",o="talkStreamClose",h="talkStreamError",l="talkStreamInactive",i={talkStreamClose:o,talkStreamError:h,talkStreamInactive:l,talkGetUserMediaTimeout:e},u="open",c="g711a",d="g711u",f="rtp",p="worklet",g={encType:c,packetType:f,rtpSsrc:"0000000000",numberChannels:1,sampleRate:8e3,sampleBitsWidth:16,debug:!1,debugLevel:n,testMicrophone:!1,audioBufferLength:160,engine:p,checkGetUserMediaTimeout:!1,getUserMediaTimeout:1e4};var m;function k(){return(new Date).getTime()}function w(e){let i="";if("object"==typeof e)try{i=JSON.stringify(e),i=JSON.parse(i)}catch(t){i=e}else i=e;return i}(function(t){var n,e,o,i,s;n="undefined"!=typeof window&&void 0!==window.document?window.document:{},e=t.exports,o=function(){for(var t,e=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],i=0,s=e.length,r={};i<s;i++)if((t=e[i])&&t[1]in n){for(i=0;i<t.length;i++)r[e[0][i]]=t[i];return r}return!1}(),i={change:o.fullscreenchange,error:o.fullscreenerror},s={request:function(r,a){return new Promise(function(t,e){var i=function(){this.off("change",i),t()}.bind(this),s=(this.on("change",i),(r=r||n.documentElement)[o.requestFullscreen](a));s instanceof Promise&&s.then(i).catch(e)}.bind(this))},exit:function(){return new Promise(function(t,e){var i,s;this.isFullscreen?(i=function(){this.off("change",i),t()}.bind(this),this.on("change",i),(s=n[o.exitFullscreen]())instanceof Promise&&s.then(i).catch(e)):t()}.bind(this))},toggle:function(t,e){return this.isFullscreen?this.exit():this.request(t,e)},onchange:function(t){this.on("change",t)},onerror:function(t){this.on("error",t)},on:function(t,e){t=i[t];t&&n.addEventListener(t,e,!1)},off:function(t,e){t=i[t];t&&n.removeEventListener(t,e,!1)},raw:o},o?(Object.defineProperties(s,{isFullscreen:{get:function(){return Boolean(n[o.fullscreenElement])}},element:{enumerable:!0,get:function(){return n[o.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(n[o.fullscreenEnabled])}}}),e?t.exports=s:window.screenfull=s):e?t.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}})(m={exports:{}}),m.exports.isEnabled;try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){var b=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(b instanceof WebAssembly.Module)new WebAssembly.Instance(b)instanceof WebAssembly.Instance}}catch(t){}class v{constructor(t){var{fromSampleRate:t,toSampleRate:e,channels:i,inputBufferSize:s}=t;if(!t||!e||!i)throw new Error("Invalid settings specified for the resampler.");this.resampler=null,this.fromSampleRate=t,this.toSampleRate=e,this.channels=i||0,this.inputBufferSize=s,this.initialize()}initialize(){this.fromSampleRate==this.toSampleRate?(this.resampler=t=>t,this.ratioWeight=1):(this.fromSampleRate<this.toSampleRate?(this.linearInterpolation(),this.lastWeight=1):(this.multiTap(),this.tailExists=!1,this.lastWeight=0),this.initializeBuffers(),this.ratioWeight=this.fromSampleRate/this.toSampleRate)}bufferSlice(e){try{return this.outputBuffer.subarray(0,e)}catch(t){try{return this.outputBuffer.length=e,this.outputBuffer}catch(t){return this.outputBuffer.slice(0,e)}}}initializeBuffers(){this.outputBufferSize=Math.ceil(this.inputBufferSize*this.toSampleRate/this.fromSampleRate/this.channels*1.0000004768371582)+this.channels+this.channels;try{this.outputBuffer=new Float32Array(this.outputBufferSize),this.lastOutput=new Float32Array(this.channels)}catch(t){this.outputBuffer=[],this.lastOutput=[]}}linearInterpolation(){this.resampler=t=>{let e,i,s,r,a,n,o,h,l,u=t.length,c=this.channels;if(u%c!=0)throw new Error("Buffer was of incorrect sample length.");if(u<=0)return[];for(e=this.outputBufferSize,i=this.ratioWeight,s=this.lastWeight,r=0,a=0,n=0,o=0,h=this.outputBuffer;s<1;s+=i)for(a=s%1,r=1-a,this.lastWeight=s%1,l=0;l<this.channels;++l)h[o++]=this.lastOutput[l]*r+t[l]*a;for(--s,u-=c,n=Math.floor(s)*c;o<e&&n<u;){for(a=s%1,r=1-a,l=0;l<this.channels;++l)h[o++]=t[n+(0<l?l:0)]*r+t[n+(c+l)]*a;s+=i,n=Math.floor(s)*c}for(l=0;l<c;++l)this.lastOutput[l]=t[n++];return this.bufferSlice(o)}}multiTap(){this.resampler=t=>{let e,i,s,r,a,n,o,h,l,u,c,d=t.length,f=this.channels;if(d%f!=0)throw new Error("Buffer was of incorrect sample length.");if(d<=0)return[];for(e=this.outputBufferSize,i=[],s=this.ratioWeight,r=0,n=0,h=!this.tailExists,this.tailExists=!1,l=this.outputBuffer,u=0,c=0,a=0;a<f;++a)i[a]=0;do{if(h)for(r=s,a=0;a<f;++a)i[a]=0;else{for(r=this.lastWeight,a=0;a<f;++a)i[a]=this.lastOutput[a];h=!0}for(;0<r&&n<d;){if(o=1+n-c,!(r>=o)){for(a=0;a<f;++a)i[a]+=t[n+(0<a?a:0)]*r;c+=r,r=0;break}for(a=0;a<f;++a)i[a]+=t[n++]*o;c=n,r-=o}if(0!==r){for(this.lastWeight=r,a=0;a<f;++a)this.lastOutput[a]=i[a];this.tailExists=!0;break}for(a=0;a<f;++a)l[u++]=i[a]/s}while(n<d&&u<e);return this.bufferSlice(u)}}resample(t){return this.fromSampleRate==this.toSampleRate?this.ratioWeight=1:(this.fromSampleRate<this.toSampleRate?this.lastWeight=1:(this.tailExists=!1,this.lastWeight=0),this.initializeBuffers(),this.ratioWeight=this.fromSampleRate/this.toSampleRate),this.resampler(t)}}const S=[255,511,1023,2047,4095,8191,16383,32767];function _(e,i,s){for(let t=0;t<s;t++)if(e<=i[t])return t;return s}class y{constructor(a){this.log=function(t){if(a._opt.debug&&a._opt.debugLevel==r){const r=a._opt.debugUuid?`[${a._opt.debugUuid}]`:"";for(var e=arguments.length,i=new Array(1<e?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];console.log(`JessibucaPro${r}:[✅✅✅][${t}]`,...i)}},this.warn=function(t){if(a._opt.debug&&(a._opt.debugLevel==r||a._opt.debugLevel==n)){const r=a._opt.debugUuid?`[${a._opt.debugUuid}]`:"";for(var e=arguments.length,i=new Array(1<e?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];console.log(`JessibucaPro${r}:[❗❗❗][${t}]`,...i)}},this.error=function(t){for(var e=a._opt.debugUuid?`[${a._opt.debugUuid}]`:"",i=arguments.length,s=new Array(1<i?i-1:0),r=1;r<i;r++)s[r-1]=arguments[r];console.error(`JessibucaPro${e}:[❌❌❌][${t}]`,...s)}}}class M{constructor(t){this.destroys=[],this.proxy=this.proxy.bind(this),this.master=t}proxy(e,t,i){let s=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{};if(e){if(Array.isArray(t))return t.map(t=>this.proxy(e,t,i,s));e.addEventListener(t,i,s);var r=()=>{"function"==typeof e.removeEventListener&&e.removeEventListener(t,i,s)};return this.destroys.push(r),r}}destroy(){this.master.debug&&this.master.debug.log("Events","destroy"),this.destroys.forEach(t=>t())}}class T extends t{constructor(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},t=(super(),t&&(this.player=t),this.tag="talk",w(g));this._opt=Object.assign({},t,e),this._opt.sampleRate=parseInt(this._opt.sampleRate,10),this._opt.sampleBitsWidth=parseInt(this._opt.sampleBitsWidth,10),this.audioContext=null,this.gainNode=null,this.recorder=null,this.workletRecorder=null,this.biquadFilter=null,this.userMediaStream=null,this.bufferSize=512,this._opt.audioBufferLength=this.calcAudioBufferLength(),this.audioBufferList=[],this.socket=null,this.socketStatus="notConnect",this.mediaStreamSource=null,this.heartInterval=null,this.checkGetUserMediaTimeout=null,this.wsUrl=null,this.startTimestamp=0,this.sequenceId=0,this.tempTimestamp=null,this.tempRtpBufferList=[],this.events=new M(this),this._initTalk(),this.player||(this.debug=new y(this)),this.log(this.tag,"init",this._opt)}destroy(){this.userMediaStream&&(this.userMediaStream.getTracks&&this.userMediaStream.getTracks().forEach(t=>{t.stop()}),this.userMediaStream=null),this.mediaStreamSource&&(this.mediaStreamSource.disconnect(),this.mediaStreamSource=null),this.recorder&&(this.recorder.disconnect(),this.recorder.onaudioprocess=null),this.biquadFilter&&(this.biquadFilter.disconnect(),this.biquadFilter=null),this.gainNode&&(this.gainNode.disconnect(),this.gainNode=null),this.workletRecorder&&(this.workletRecorder.disconnect(),this.workletRecorder=null),this.socket&&(this.socketStatus===u&&this._sendClose(),this.socket.close(),this.socket=null),this._stopHeartInterval(),this._stopCheckGetUserMediaTimeout(),this.audioContext=null,this.gainNode=null,this.recorder=null,this.audioBufferList=[],this.sequenceId=0,this.wsUrl=null,this.tempTimestamp=null,this.tempRtpBufferList=[],this.startTimestamp=0,this.log("talk","destroy")}addRtpToBuffer(t){var e=t.length+this.tempRtpBufferList.length,e=new Uint8Array(e);e.set(this.tempRtpBufferList,0),e.set(t,this.tempRtpBufferList.length),this.tempRtpBufferList=e}downloadRtpFile(){var t=new Blob([this.tempRtpBufferList]);try{var e=document.createElement("a");e.href=window.URL.createObjectURL(t),e.download=Date.now()+".rtp",e.click()}catch(t){console.error("downloadRtpFile",t)}}calcAudioBufferLength(){var t=this._opt["sampleRate"];return 8*t*.02/8}get socketStatusOpen(){return this.socketStatus===u}log(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._log("log",...e)}warn(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._log("warn",...e)}error(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];this._log("error",...e)}_log(t){for(var e=arguments.length,i=new Array(1<e?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];(this.player?this.player.debug:this.debug||console)[t](...i)}_getSequenceId(){return++this.sequenceId}_createWebSocket(){return new Promise((t,e)=>{var i=this.events.proxy;this.socket=new WebSocket(this.wsUrl),this.socket.binaryType="arraybuffer",this.emit("talkStreamStart"),i(this.socket,"open",()=>{this.socketStatus=u,this.log(this.tag,"websocket open -> do talk"),this.emit("talkStreamOpen"),t(),this._doTalk()}),i(this.socket,"message",t=>{this.log(this.tag,"websocket message",t.data)}),i(this.socket,"close",t=>{this.socketStatus="close",this.log(this.tag,"websocket close"),this.emit(o),e(t)}),i(this.socket,"error",t=>{this.socketStatus="error",this.error(this.tag,"websocket error",t),this.emit(h,t),e(t)})})}_sendClose(){}_initTalk(){this._initMethods(),this._opt.engine===p?this._initWorklet():"script"===this._opt.engine&&this._initScriptProcessor(),this.log(this.tag,"audioContext samplerate",this.audioContext.sampleRate)}_initMethods(){this.audioContext=new(window.AudioContext||window.webkitAudioContext)({sampleRate:48e3}),this.gainNode=this.audioContext.createGain(),this.gainNode.gain.value=1,this.biquadFilter=this.audioContext.createBiquadFilter(),this.biquadFilter.type="lowpass",this.biquadFilter.frequency.value=3e3,this.resampler=new v({fromSampleRate:this.audioContext.sampleRate,toSampleRate:this._opt.sampleRate,channels:this._opt.numberChannels,inputBufferSize:this.bufferSize})}_initScriptProcessor(){var t=this.audioContext.createScriptProcessor||this.audioContext.createJavaScriptNode;this.recorder=t.apply(this.audioContext,[this.bufferSize,this._opt.numberChannels,this._opt.numberChannels]),this.recorder.onaudioprocess=t=>this._onaudioprocess(t)}_initWorklet(){var t;this.audioContext.audioWorklet.addModule((t=function(){class t extends AudioWorkletProcessor{constructor(t){super(),this._cursor=0,this._bufferSize=t.processorOptions.bufferSize,this._buffer=new Float32Array(this._bufferSize)}process(e,t,i){if(!e.length||!e[0].length)return!0;for(let t=0;t<e[0][0].length;t++)this._cursor+=1,this._cursor===this._bufferSize&&(this._cursor=0,this.port.postMessage({eventType:"data",buffer:this._buffer})),this._buffer[this._cursor]=e[0][0][t];return!0}}registerProcessor("talk-processor",t)}.toString().trim().match(/^function\s*\w*\s*\([\w\s,]*\)\s*{([\w\W]*?)}$/)[1],t=new Blob([t],{type:"application/javascript"}),URL.createObjectURL(t))).then(()=>{var t=new AudioWorkletNode(this.audioContext,"talk-processor",{processorOptions:{bufferSize:this.bufferSize}});t.connect(this.gainNode),t.port.onmessage=t=>{"data"===t.data.eventType&&this._encodeAudioData(t.data.buffer)},this.workletRecorder=t})}_onaudioprocess(t){t=t.inputBuffer.getChannelData(0);this._encodeAudioData(new Float32Array(t))}_encodeAudioData(t){if(0===t[0]&&0===t[1])this.log(this.tag,"empty audio data");else{const s=this.resampler.resample(t);let e=s;if(16===this._opt.sampleBitsWidth?e=function(t){let e=t.length,i=new Int16Array(e);for(;e--;){var s=Math.max(-1,Math.min(1,t[e]));i[e]=s<0?32768*s:32767*s}return i}(s):8===this._opt.sampleBitsWidth&&(e=function(t){let e=t.length,i=new Int8Array(e);for(;e--;){var s=Math.max(-1,Math.min(1,t[e]));i[e]=parseInt(255/(65535/(32768+(s<0?32768*s:32767*s))),10)}return i}(s)),null!==e.buffer){let t=null;this._opt.encType===c?t=function(t){const i=[];return Array.prototype.slice.call(t).forEach((t,e)=>{i[e]=function(t){let e,i,s;return 0<=t?e=213:(e=85,(t=-t-1)<0&&(t=32767)),8<=(i=_(t,S,8))?127^e:(s=i<<4,(s|=i<2?t>>4&15:t>>i+3&15)^e)}(t)}),i}(e):this._opt.encType===d&&(t=function(t){const i=[];return Array.prototype.slice.call(t).forEach((t,e)=>{i[e]=function(t){let e=0;e=t<0?(t=132-t,127):(t+=132,255);var i=_(t,S,8);return 8<=i?127^e:(i<<4|t>>i+3&15)^e}(t)}),i}(e));const s=Uint8Array.from(t);for(let t=0;t<s.length;t++){var i=this.audioBufferList.length;this.audioBufferList[+i]=s[t],this.audioBufferList.length===this._opt.audioBufferLength&&(this._sendTalkMsg(new Uint8Array(this.audioBufferList)),this.audioBufferList=[])}}}}_parseAudioMsg(t){let e=null;return this._opt.packetType!==f||this._opt.encType!==c&&this._opt.encType!==d?"opus"===this._opt.packetType?e=this.opusPacket(t):"empty"===this._opt.packetType&&(e=t):e=this.rtpPacket(t),e}rtpPacket(t){var e=[];let i=0,s,r;var a=this._opt.rtpSsrc,n=t.length,n=(this._opt.encType===c?i=8:this._opt.encType===d&&(i=0),this.startTimestamp||(this.startTimestamp=k()),r=k()-this.startTimestamp,s=this._getSequenceId(),n+12),o=(e[0]=255&n>>8,e[1]=255&n>>0,e[2]=128,e[3]=128+i,e[4]=s/256,e[5]=s%256,e[6]=r/65536/256,e[7]=r/65536%256,e[8]=r%65536/256,e[9]=r%65536%256,e[10]=a/65536/256,e[11]=a/65536%256,e[12]=a%65536/256,e[13]=a%65536%256,e.concat([...t])),h=new Uint8Array(o.length);for(let t=0;t<o.length;t++)h[t]=o[t];return h}opusPacket(t){return t}_sendTalkMsg(t){null===this.tempTimestamp&&(this.tempTimestamp=k());var e=k(),i=e-this.tempTimestamp,s=this._parseAudioMsg(t);this.log(this.tag,`'send talk msg and diff is ${i} and byteLength is ${s.byteLength} and length is ${s.length}, and g711 length is `+t.length),this._opt.packetType===f&&this.addRtpToBuffer(s),s&&(this.socketStatusOpen?this.socket.send(s.buffer):this.emit("tallWebsocketClosedByError")),this.tempTimestamp=e}_doTalk(){this._getUserMedia()}_getUserMedia(){this.log(this.tag,"getUserMedia"),void 0===window.navigator.mediaDevices&&(window.navigator.mediaDevices={}),void 0===window.navigator.mediaDevices.getUserMedia&&(this.log(this.tag,"window.navigator.mediaDevices.getUserMedia is undefined and init function"),window.navigator.mediaDevices.getUserMedia=function(i){var s=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return s?new Promise(function(t,e){s.call(navigator,i,t,e)}):Promise.reject(new Error("getUserMedia is not implemented in this browser"))}),this._opt.checkGetUserMediaTimeout&&this._startCheckGetUserMediaTimeout(),window.navigator.mediaDevices.getUserMedia({audio:{latency:!0,noiseSuppression:!0,autoGainControl:!0,echoCancellation:!0,sampleRate:48e3,channelCount:1},video:!1}).then(t=>{this.log(this.tag,"getUserMedia success"),this.userMediaStream=t,this.mediaStreamSource=this.audioContext.createMediaStreamSource(t),this.mediaStreamSource.connect(this.biquadFilter),this.recorder?(this.biquadFilter.connect(this.recorder),this.recorder.connect(this.gainNode)):this.workletRecorder&&(this.biquadFilter.connect(this.workletRecorder),this.workletRecorder.connect(this.gainNode)),this.gainNode.connect(this.audioContext.destination),this.emit(s),null===t.oninactive&&(t.oninactive=t=>{this._handleStreamInactive(t)})}).catch(t=>{this.error(this.tag,"getUserMedia error",t.toString()),this.emit(a,t.toString())}).finally(()=>{this.log(this.tag,"getUserMedia finally"),this._stopCheckGetUserMediaTimeout()})}_getUserMedia2(){this.log(this.tag,"getUserMedia"),navigator.mediaDevices?navigator.mediaDevices.getUserMedia({audio:!0}).then(t=>{this.log(this.tag,"getUserMedia2 success")}):navigator.getUserMedia({audio:!0},this.log(this.tag,"getUserMedia2 success"),this.log(this.tag,"getUserMedia2 fail"))}async _getUserMedia3(){this.log(this.tag,"getUserMedia3");try{var t=await navigator.mediaDevices.getUserMedia({audio:{latency:!0,noiseSuppression:!0,autoGainControl:!0,echoCancellation:!0,sampleRate:48e3,channelCount:1},video:!1});console.log("getUserMedia() got stream:",t),this.log(this.tag,"getUserMedia3 success")}catch(t){this.log(this.tag,"getUserMedia3 fail")}}_handleStreamInactive(t){this.userMediaStream&&(this.error(this.tag,"stream oninactive"),this.emit(l))}_startCheckGetUserMediaTimeout(){this._stopCheckGetUserMediaTimeout(),this.checkGetUserMediaTimeout=setTimeout(()=>{this.log(this.tag,"check getUserMedia timeout"),this.emit(e)},this._opt.getUserMediaTimeout)}_stopCheckGetUserMediaTimeout(){this.checkGetUserMediaTimeout&&(this.log(this.tag,"stop checkGetUserMediaTimeout"),clearTimeout(this.checkGetUserMediaTimeout),this.checkGetUserMediaTimeout=null)}_startHeartInterval(){this.heartInterval=setInterval(()=>{this.log(this.tag,"heart interval");var t=[35,36,0,0,0,0,0,0],t=new Uint8Array(t);this.socket.send(t.buffer)},15e3)}_stopHeartInterval(){this.heartInterval&&(this.log(this.tag,"stop heart interval"),clearInterval(this.heartInterval),this.heartInterval=null)}startTalk(i){return new Promise((t,e)=>function(){let t=!1;var e=window.navigator;return t=e?(t=!(!e.mediaDevices||!e.mediaDevices.getUserMedia))||!!(e.getUserMedia||e.webkitGetUserMedia||e.mozGetUserMedia||e.msGetUserMedia):t}()?(this.wsUrl=i,this._opt.testMicrophone?(this._doTalk(),t()):(this._createWebSocket().catch(t=>{e(t)}),this.once(a,()=>{e("getUserMedia fail")}),void this.once(s,()=>{t()}))):e("not support getUserMedia"))}setVolume(t){var e;t=parseFloat(t).toFixed(2),isNaN(t)||(e=t,t=Math.max(Math.min(e,Math.max(0,1)),Math.min(0,1)),this.gainNode.gain.value=t)}getOption(){return this._opt}get volume(){return this.gainNode?parseFloat(100*this.gainNode.gain.value).toFixed(0):null}}class U extends t{constructor(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};super(),this.talk=null,this._opt=t,this.LOG_TAG="talk",this.debug=new y(this)}destroy(){this.off(),this.talk&&(this.talk.destroy(),this.talk=null)}_initTalk(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=(this.talk&&(this.talk.destroy(),this.talk=null),Object.assign({},w(this._opt),t));this.talk=new T(null,t),this.debug.log(this.LOG_TAG,"_initTalk",this.talk.getOption()),this._bindTalkEvents()}_bindTalkEvents(){Object.keys(i).forEach(e=>{this.talk.on(i[e],t=>{this.emit(e,t)})})}startTalk(i){let s=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return new Promise((t,e)=>{this._initTalk(s),this.talk.startTalk(i).then(()=>{t(),this.talk.once(o,()=>{this.stopTalk().catch(t=>{})}),this.talk.once(h,()=>{this.stopTalk().catch(t=>{})}),this.talk.once(l,()=>{this.stopTalk().catch(t=>{})})}).catch(t=>{e(t)})})}stopTalk(){return new Promise((t,e)=>{this.talk||e("talk is not init"),this.talk.destroy(),t()})}getTalkVolume(){return new Promise((t,e)=>{this.talk||e("talk is not init"),t(this.talk.volume)})}setTalkVolume(i){return new Promise((t,e)=>{this.talk||e("talk is not init"),this.talk.setVolume(i/100),t()})}downloadTempRtpFile(){return new Promise((t,e)=>{this.talk?(this.talk.downloadRtpFile(),t()):e("talk is not init")})}}U.EVENTS=i,window.JessibucaProTalk=U});
