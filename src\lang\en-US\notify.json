{"notify.channel.index.333541-0": "Channel name", "notify.channel.index.333541-1": "Please enter the channel name", "notify.channel.index.333541-2": "Channel type", "notify.channel.index.333541-3": "Please select channel type", "notify.channel.index.333541-4": "New channels added", "notify.channel.index.333541-5": "Editing channels", "notify.channel.index.333541-6": "Type of sending channel", "notify.channel.index.333541-7": "Service providers", "notify.channel.index.333541-8": "Creation time", "notify.channel.index.333541-9": "Service providers", "notify.channel.index.333541-10": "Please select a service provider", "notify.channel.index.333541-11": "Please enter the configuration content", "notify.channel.index.333541-12": "Channel name cannot be empty", "notify.channel.index.333541-13": "The sending channel type cannot be empty", "notify.channel.index.333541-14": "Service provider cannot be empty", "notify.channel.index.333541-15": "Add notification channels", "notify.channel.index.333541-16": "Modify notification channels", "notify.channel.index.333541-17": "Are you sure to delete the notification channel number as {0}？", "notify.template.index.333542-0": "Template Name", "notify.template.index.333542-1": "Please enter the template name", "notify.template.index.333542-2": "Business code", "notify.template.index.333542-3": "Please select notification business", "notify.template.index.333542-4": "New template added", "notify.template.index.333542-5": "Channel account", "notify.template.index.333542-6": "Is it enabled", "notify.template.index.333542-7": "test", "notify.template.index.333542-8": "Please select notification business", "notify.template.index.333542-9": "Please select a channel account", "notify.template.index.333542-10": "Select type", "notify.template.index.333542-11": "Please select the sending type", "notify.template.index.333542-12": "Please select the type of DingTalk notification to send", "notify.template.index.333542-13": "Please enter the configuration content", "notify.template.index.333542-14": "Send account", "notify.template.index.333542-15": "Please enter your phone number, email, or user ID", "notify.template.index.333542-16": "Please enter variable information", "notify.template.index.333542-17": "The template name cannot be empty", "notify.template.index.333542-18": "Notification channels (SMS, WeChat service number, email, etc.) cannot be empty", "notify.template.index.333542-19": "Template content, configuration channel parameters cannot be empty", "notify.template.index.333542-20": "The template content cannot be empty", "notify.template.index.333542-21": "Channel source cannot be empty", "notify.template.index.333542-22": "Channel account cannot be empty", "notify.template.index.333542-23": "Service provider cannot be empty", "notify.template.index.333542-24": "Account information cannot be empty", "notify.template.index.333542-25": "Add notification template", "notify.template.index.333542-26": "Modify notification template", "notify.template.index.333542-27": "Are you sure to delete the notification template with the number{0}？", "notify.template.index.333542-28": "The current template already has templates with the same activation conditions enabled (activation conditions: one template for the same business code can be enabled for SMS, voice, and email channels, and one for different service providers under WeChat and DingTalk channels). Do you want to continue enabling the current template?", "notify.template.index.333542-29": "Successfully enabled", "notify.template.index.333542-30": "<PERSON><PERSON><PERSON> failed", "notify.template.index.333542-31": "Disabled", "notify.template.index.333542-32": "Operation successful", "notify.template.index.333542-33": "operation failed", "notify.template.index.333542-34": "Sent successfully!", "notify.log.333543-0": "Channel number", "notify.log.333543-1": "Please enter the channel number", "notify.log.333543-2": "Notification template number", "notify.log.333543-3": "Please enter the notification template number", "notify.log.333543-4": "Send account", "notify.log.333543-5": "Please enter the sending account", "notify.log.333543-6": "Business code", "notify.log.333543-7": "Please select notification business", "notify.log.333543-8": "Sending time", "notify.log.333543-9": "Start date", "notify.log.333543-10": "End date", "notify.log.333543-11": "Channel account", "notify.log.333543-12": "Template Name", "notify.log.333543-13": "Sending status", "notify.log.333543-14": "fail", "notify.log.333543-15": "success", "notify.log.333543-16": "Sending time", "notify.log.333543-17": "Return content", "notify.log.333543-18": "Please enter the content", "notify.log.333543-19": "Message content", "notify.log.333543-20": "Add notification log", "notify.log.333543-21": "View log details", "notify.log.333543-22": "Are you sure to delete the notification log with the number {0}？"}