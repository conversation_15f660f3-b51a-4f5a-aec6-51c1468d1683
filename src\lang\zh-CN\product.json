{"product.index.091251-0": "产品名称", "product.index.091251-1": "请输入产品名称", "product.index.091251-2": "分类名称", "product.index.091251-3": "请输入产品分类名称", "product.index.091251-4": "状态", "product.index.091251-5": "请选择状态", "product.index.091251-6": "搜索", "product.index.091251-7": "重置", "product.index.091251-8": "显示上级机构产品", "product.index.091251-9": "选中后，本级可以看上级的产品", "product.index.091251-10": "新增", "product.index.091251-11": "私有", "product.index.091251-12": "取消发布", "product.index.091251-13": "已发布", "product.index.091251-14": "现在发布", "product.index.091251-15": "未发布", "product.index.091251-16": "所属分类", "product.index.091251-17": "产品类型", "product.index.091251-18": "联网方式", "product.index.091251-19": "设备授权", "product.index.091251-20": "已启用", "product.index.091251-21": "未启用", "product.index.091251-22": "查看详情", "product.index.091251-23": "删除", "product.index.091251-24": "查看设备", "product.index.091251-25": "暂无数据，请添加产品", "product.index.091251-26": "该功能暂未实现，参考教程和项目的SDK示例", "product.index.091251-27": "设备类型", "product.index.091251-28": "下 载", "product.index.091251-29": "取 消", "product.index.091251-30": "确定取消发布？", "product.index.091251-31": "没有操作权限", "product.index.091251-32": "产品发布后，可以创建对应的设备", "product.index.091251-33": "重要提示：产品下已有{0}个设备，取消发布可以修改产品信息和模型，重新发布后对应设备状态将会被重置！", "product.index.091251-34": "提示", "product.index.091251-35": "确定", "product.index.091251-38": "生成SDK", "product.index.091251-39": "是否确认删除产品编号为{0}的数据项？", "product.index.091251-40": "组态设计", "product.product-app.045891-0": "刷新", "product.product-app.045891-1": "该功能暂不可用,后面版本发布", "product.product-app.045891-2": "名称", "product.product-app.045891-3": "标识符", "product.product-app.045891-4": "物模型类别", "product.product-app.045891-5": "数据类型", "product.product-app.045891-6": "部件", "product.product-app.045891-7": "系统定义的部件", "product.product-app.045891-8": "高级功能", "product.product-app.045891-9": "自定义页面", "product.product-app.045891-10": "请输入自定义页面", "product.product-app.045891-11": "界面展示", "product.product-authorize.314975-0": "设备编号", "product.product-authorize.314975-1": "请输入设备编号", "product.product-authorize.314975-2": "授权码", "product.product-authorize.314975-3": "请输入授权码", "product.product-authorize.314975-4": "状态", "product.product-authorize.314975-5": "请选择状态", "product.product-authorize.314975-6": "搜索", "product.product-authorize.314975-7": "重置", "product.product-authorize.314975-8": "生成授权码", "product.product-authorize.314975-9": "批量删除", "product.product-authorize.314975-10": "导出", "product.product-authorize.314975-11": "Tips：双击可以复制授权码。", "product.product-authorize.314975-12": "授权时间", "product.product-authorize.314975-13": "备注", "product.product-authorize.314975-14": "操作", "product.product-authorize.314975-15": "删除", "product.product-authorize.314975-17": "设备名称", "product.product-authorize.314975-18": "请输入设备名称", "product.product-authorize.314975-19": "选择", "product.product-authorize.314975-20": "设备ID", "product.product-authorize.314975-21": "用户名称", "product.product-authorize.314975-22": "设备状态", "product.product-authorize.314975-23": "请输入内容", "product.product-authorize.314975-24": "确 定", "product.product-authorize.314975-25": "取 消", "product.product-authorize.314975-26": "设备详情", "product.product-authorize.314975-27": "提示：查找不到设备，可能已经被删除", "product.product-authorize.314975-28": "未激活", "product.product-authorize.314975-29": "禁用", "product.product-authorize.314975-30": "在线", "product.product-authorize.314975-31": "离线", "product.product-authorize.314975-32": "设备影子", "product.product-authorize.314975-33": "启用", "product.product-authorize.314975-35": "定位方式", "product.product-authorize.314975-36": "自动定位", "product.product-authorize.314975-37": "设备定位", "product.product-authorize.314975-38": "自定义位置", "product.product-authorize.314975-39": "产品名称", "product.product-authorize.314975-40": "租户名称", "product.product-authorize.314975-41": "固件版本", "product.product-authorize.314975-42": "所在地址", "product.product-authorize.314975-43": "设备经度", "product.product-authorize.314975-44": "设备纬度", "product.product-authorize.314975-45": "入网IP", "product.product-authorize.314975-46": "设备信号", "product.product-authorize.314975-47": "创建时间", "product.product-authorize.314975-48": "激活时间", "product.product-authorize.314975-49": "备注信息", "product.product-authorize.314975-50": "查看设备", "product.product-authorize.314975-51": "关 闭", "product.product-authorize.314975-52": "输入授权码数量", "product.product-authorize.314975-53": "确 定", "product.product-authorize.314975-54": "取 消", "product.product-authorize.314975-55": "数量内容不正确", "product.product-authorize.314975-56": "新增授权码成功", "product.product-authorize.314975-57": "取消新增", "product.product-authorize.314975-58": "选择设备", "product.product-authorize.314975-59": "设备授权成功", "product.product-authorize.314975-60": "请选择要授权的设备", "product.product-authorize.314975-61": "备注成功", "product.product-authorize.314975-62": "是否确认删除产品授权码编号为{0}的数据项？", "product.product-authorize.314975-63": "删除成功", "product.product-authorize.314975-64": "成功", "product.product-authorize.314975-66": "复制成功！", "product.product-authorize.314975-67": "失败", "product.product-authorize.314975-68": "复制失败！", "product.product-edit.473153-0": "基本信息", "product.product-edit.473153-1": "产品名称", "product.product-edit.473153-2": "请输入产品名称", "product.product-edit.473153-3": "产品分类", "product.product-edit.473153-4": "请选择分类", "product.product-edit.473153-5": "通讯协议", "product.product-edit.473153-6": "产品建立后，不允许修改通讯协议,修改会导致该产品下设备脏数据问题", "product.product-edit.473153-7": "请选择协议", "product.product-edit.473153-8": "当前通讯协议为modbus协议,请选择采集点模板,默认添加设备为网关设备", "product.product-edit.473153-9": "采集点模板", "product.product-edit.473153-10": "删除", "product.product-edit.473153-11": "选择模板", "product.product-edit.473153-13": "请选择设备类型", "product.product-edit.473153-14": "传输协议", "product.product-edit.473153-15": "请选择传输协议", "product.product-edit.473153-16": "联网方式", "product.product-edit.473153-17": "请选择联网方式", "product.product-edit.473153-18": "私有产品", "product.product-edit.473153-19": "设置为私有产品后,下级不能查看", "product.product-edit.473153-20": "启用授权", "product.product-edit.473153-21": "认证方式", "product.product-edit.473153-22": "请选择认证方式", "product.product-edit.473153-23": "定位方式", "product.product-edit.473153-24": "请选择设备状态", "product.product-edit.473153-25": "产品编号", "product.product-edit.473153-26": "自动生成", "product.product-edit.473153-27": "Mqtt账号", "product.product-edit.473153-28": "不填自动生成", "product.product-edit.473153-29": "Mqtt密码", "product.product-edit.473153-30": "不填则自动生成", "product.product-edit.473153-31": "产品秘钥", "product.product-edit.473153-32": "备注信息", "product.product-edit.473153-33": "请输入内容", "product.product-edit.473153-34": "产品图片", "product.product-edit.473153-35": "提示：上传后需要提交保存", "product.product-edit.473153-36": "修 改", "product.product-edit.473153-37": "新 增", "product.product-edit.473153-38": "产品模型", "product.product-edit.473153-39": "固件管理", "product.product-edit.473153-40": "设备授权", "product.product-edit.473153-41": "SIP配置", "product.product-edit.473153-42": "发布产品", "product.product-edit.473153-43": "取消发布", "product.product-edit.473153-44": "返回列表", "product.product-edit.473153-45": "模板名称", "product.product-edit.473153-46": "搜索", "product.product-edit.473153-47": "重置", "product.product-edit.473153-48": "选择采集点模板", "product.product-edit.473153-49": "物模型列表", "product.product-edit.473153-50": "从机数量:", "product.product-edit.473153-51": "变量数量:", "product.product-edit.473153-52": "采集方式:", "product.product-edit.473153-53": "物模型名称", "product.product-edit.473153-54": "寄存器", "product.product-edit.473153-55": "数值类型", "product.product-edit.473153-56": "取 消", "product.product-edit.473153-57": "确 定", "product.product-edit.473153-58": "产品名称不能为空", "product.product-edit.473153-59": "产品分类ID不能为空", "product.product-edit.473153-60": "设备协议不能为空", "product.product-edit.473153-61": "传输协议不能为空", "product.product-edit.473153-62": "修改成功", "product.product-edit.473153-63": "请选择采集点模板", "product.product-edit.473153-64": "添加成功,可以开始定义物模型或配置", "product.product-edit.473153-65": "物模型已经从采集点模板同步至产品", "product.product-edit.473153-66": "确定取消发布？", "product.product-edit.473153-67": "产品发布后，可以创建对应的设备", "product.product-edit.473153-68": "重要提示：产品下已有{0}个设备，取消发布可以修改产品信息和模型，重新发布后对应设备状态将会被重置！", "product.product-edit.473153-69": "提示", "product.product-edit.473153-70": "确定", "product.product-edit.473153-71": "取消", "product.product-edit.473153-72": "启用", "product.product-edit.473153-74": "停用", "product.product-edit.473153-75": "确认要{0}授权码吗？", "product.product-edit.473153-77": "授权码已", "product.product-edit.473153-78": "设备类型", "product.product-edit.473153-79": "组态应用", "product.product-edit.473153-80": "Modbus配置", "product.product-edit.473153-81": "通讯协议", "product.product-edit.473153-82": "请选择协议", "product.product-firmware.420545-0": "新增", "product.product-firmware.420545-1": "刷新", "product.product-firmware.420545-2": "固件名称", "product.product-firmware.420545-3": "固件版本", "product.product-firmware.420545-4": "状态", "product.product-firmware.420545-5": "最新", "product.product-firmware.420545-6": "默认", "product.product-firmware.420545-7": "创建时间", "product.product-firmware.420545-8": "下载地址", "product.product-firmware.420545-9": "固件描述", "product.product-firmware.420545-10": "操作", "product.product-firmware.420545-11": "查看", "product.product-firmware.420545-12": "删除", "product.product-firmware.420545-13": "请输入固件名称", "product.product-firmware.420545-14": "请输入固件版本", "product.product-firmware.420545-15": "最新固件", "product.product-firmware.420545-16": "提示：产品中只能有一个最新固件", "product.product-firmware.420545-17": "固件上传", "product.product-firmware.420545-18": "请输入固件信息", "product.product-firmware.420545-19": "新 增", "product.product-firmware.420545-20": "修 改", "product.product-firmware.420545-21": "取消", "product.product-firmware.420545-24": "固件名称不能为空", "product.product-firmware.420545-25": "固件版本不能为空", "product.product-firmware.420545-26": "文件路径不能为空", "product.product-firmware.420545-27": "添加产品固件", "product.product-firmware.420545-28": "修改产品固件", "product.product-firmware.420545-29": "修改成功", "product.product-firmware.420545-30": "新增成功", "product.product-firmware.420545-31": "是否确认删除产品固件编号为{0}的数据项？", "product.product-firmware.420545-32": "删除成功", "product.product-select-template.318012-0": "名称", "product.product-select-template.318012-1": "请输入物模型名称", "product.product-select-template.318012-2": "类别", "product.product-select-template.318012-3": "请选择模型类别", "product.product-select-template.318012-4": "搜索", "product.product-select-template.318012-5": "重置", "product.product-select-template.318012-6": "标识符", "product.product-select-template.318012-7": "物模型类别", "product.product-select-template.318012-8": "图表展示", "product.product-select-template.318012-9": "实时监测", "product.product-select-template.318012-10": "只读", "product.product-select-template.318012-11": "历史存储", "product.product-select-template.318012-12": "数据类型", "product.product-things-model.142341-0": "新增", "product.product-things-model.142341-1": "导入通用物模型", "product.product-things-model.142341-2": "刷新", "product.product-things-model.142341-3": "查看物模型", "product.product-things-model.142341-4": "注意：标识符不能重复", "product.product-things-model.142341-5": "请选择设备从机:", "product.product-things-model.142341-6": "请选择设备从机", "product.product-things-model.142341-7": "网关物模型", "product.product-things-model.142341-8": "名称", "product.product-things-model.142341-9": "标识符", "product.product-things-model.142341-10": "寄存器地址(10进制)", "product.product-things-model.142341-12": "图表展示", "product.product-things-model.142341-13": "实时监测", "product.product-things-model.142341-14": "只读", "product.product-things-model.142341-15": "历史存储", "product.product-things-model.142341-16": "物模型类别", "product.product-things-model.142341-17": "数据类型", "product.product-things-model.142341-18": "数据定义", "product.product-things-model.142341-19": "计算公式", "product.product-things-model.142341-20": "排序", "product.product-things-model.142341-21": "操作", "product.product-things-model.142341-22": "查看", "product.product-things-model.142341-23": "删除", "product.product-things-model.142341-24": "模型名称", "product.product-things-model.142341-25": "请输入物模型名称，例如：温度", "product.product-things-model.142341-26": "模型标识", "product.product-things-model.142341-27": "请输入标识符，例如：temperature", "product.product-things-model.142341-28": "模型排序", "product.product-things-model.142341-29": "请输入排序", "product.product-things-model.142341-30": "模型类别", "product.product-things-model.142341-31": "属性", "product.product-things-model.142341-32": "功能", "product.product-things-model.142341-33": "事件", "product.product-things-model.142341-34": "模型特性", "product.product-things-model.142341-35": "只读数据", "product.product-things-model.142341-36": "分享权限", "product.product-things-model.142341-37": "请选择数据类型", "product.product-things-model.142341-38": "整数", "product.product-things-model.142341-39": "小数", "product.product-things-model.142341-40": "布尔", "product.product-things-model.142341-41": "枚举", "product.product-things-model.142341-42": "字符串", "product.product-things-model.142341-43": "数组", "product.product-things-model.142341-44": "对象", "product.product-things-model.142341-45": "取值范围", "product.product-things-model.142341-46": "最小值", "product.product-things-model.142341-47": "到", "product.product-things-model.142341-48": "最大值", "product.product-things-model.142341-49": "单位", "product.product-things-model.142341-50": "请输入单位，例如：℃", "product.product-things-model.142341-51": "步长", "product.product-things-model.142341-52": "请输入步长，例如：1", "product.product-things-model.142341-53": "设备上行数据经计算公式计算后显示 。", "product.product-things-model.142341-54": "公式中的%s为占位符，是固定字段。", "product.product-things-model.142341-55": "如：", "product.product-things-model.142341-56": "加：%s+10", "product.product-things-model.142341-57": "减：%s-10", "product.product-things-model.142341-58": "乘：%s*10", "product.product-things-model.142341-59": "除：%s/10", "product.product-things-model.142341-60": "除(保留小数)：%s%10.00", "product.product-things-model.142341-61": "保留小数", "product.product-things-model.142341-62": "控制公式", "product.product-things-model.142341-63": "布尔值", "product.product-things-model.142341-64": "例如：关闭", "product.product-things-model.142341-65": "（0 值对应文本）", "product.product-things-model.142341-66": "例如：打开", "product.product-things-model.142341-67": "（1 值对应文本）", "product.product-things-model.142341-68": "展示方式", "product.product-things-model.142341-69": "请选择展示方式", "product.product-things-model.142341-70": "下拉框", "product.product-things-model.142341-71": "按钮", "product.product-things-model.142341-72": "枚举项", "product.product-things-model.142341-73": "参数值，例如：0", "product.product-things-model.142341-74": "参数描述，例如：中速档位", "product.product-things-model.142341-75": "添加枚举项", "product.product-things-model.142341-76": "最大长度", "product.product-things-model.142341-77": "例如：1024", "product.product-things-model.142341-78": "（字符串的最大长度）", "product.product-things-model.142341-79": "元素个数", "product.product-things-model.142341-80": "例如：5", "product.product-things-model.142341-81": "数组类型", "product.product-things-model.142341-82": "对象参数", "product.product-things-model.142341-83": "请选择设备", "product.product-things-model.142341-84": "编辑", "product.product-things-model.142341-85": "添加参数", "product.product-things-model.142341-86": "删除", "product.product-things-model.142341-87": "对象参数", "product.product-things-model.142341-88": "请选择设备", "product.product-things-model.142341-89": "修 改", "product.product-things-model.142341-90": "新 增", "product.product-things-model.142341-91": "取消", "product.product-things-model.142341-92": "复制", "product.product-things-model.142341-94": "物模型名称不能为空", "product.product-things-model.142341-95": "标识符，产品下唯一不能为空", "product.product-things-model.142341-96": "模型排序不能为空", "product.product-things-model.142341-97": "模型类别不能为空", "product.product-things-model.142341-98": "数据类型不能为空", "product.product-things-model.142341-99": "添加物模型", "product.product-things-model.142341-100": "修改物模型", "product.product-things-model.142341-101": "物模型", "product.product-things-model.142341-102": "对象的参数不能为空", "product.product-things-model.142341-103": "对象类型模型标识输入不能包含下划线，请重新填写模型标识！", "product.product-things-model.142341-104": "参数标识", "product.product-things-model.142341-105": "重复", "product.product-things-model.142341-106": "请重新选择数据类型！", "product.product-things-model.142341-107": "修改成功", "product.product-things-model.142341-108": "新增成功", "product.product-things-model.142341-109": "是否确认删除物模型编号为{0}的数据项？", "product.product-things-model.142341-111": "删除成功", "product.product-things-model.142341-112": "采集点删除请在采集点模板修改", "product.product-things-model.142341-113": "关闭", "product.product-things-model.142341-114": "打开", "product.product-things-model.142341-115": "低", "product.product-things-model.142341-116": "高", "product.product-things-model.142341-117": "最大值：", "product.product-things-model.142341-118": "最小值：", "product.product-things-model.142341-119": "步长：", "product.product-things-model.142341-120": "单位：", "product.product-things-model.142341-121": "最大长度：", "product.product-things-model.142341-122": "数组类型：", "product.product-things-model.142341-123": "元素个数：", "product.product-things-model.142341-124": "寄存器地址", "product.product-things-model.142341-125": "删除", "product.product-things-model.142341-126": "导入", "product.product-things-model.142341-127": "APP展示", "product.product-things-model.142341-128": "请选择是否在App展示", "product.product-things-model.142341-129": "物模型类型", "product.product-things-model.142341-130": "请选择物模型类型", "product.product-things-model.142341-131": "是否历史存储", "product.product-things-model.142341-132": "请选择是否历史存储", "product.product-things-model.142341-133": "搜索", "product.product-things-model.142341-134": "重置", "product.product-things-model.142341-135": "分组", "product.product-modbus.562372-0": "网关子设备配置", "product.product-modbus.562372-1": "网关子设备配置默认的子设备地址,轮询方式", "product.product-modbus.562372-2": "编辑设备配置", "product.product-modbus.562372-3": "保存", "product.product-modbus.562372-4": "取消", "product.product-modbus.562372-5": "设备状态 :", "product.product-modbus.562372-6": "配置设备状态是来自网关还是设备下变量通讯是否正常；", "product.product-modbus.562372-7": "如选择网关则设备的在离线状态取自网关的在离线状态，如果选择设备数据，则设备在所选时长内无变量通讯则判定设备离线。", "product.product-modbus.562372-8": "                  当选择设备数据时，建议时长大于模板内最小采集频率，否则设备将频繁被动离线。", "product.product-modbus.562372-9": "选择时长 :", "product.product-modbus.562372-10": "使用设备数据判断方式,要确认在轮训时间内有数据上报", "product.product-modbus.562372-11": "请选择默认时长", "product.product-modbus.562372-12": " 设备下多久无数据则判定该设备离线", "product.product-modbus.562372-13": "采集方式 :", "product.product-modbus.562372-14": "默认地址 :", "product.product-modbus.562372-15": "从机地址", "product.product-modbus.562372-16": "子设备的默认地址,若在设备详情的子设备中设置了子设备地址,则默认值会被覆盖", "product.product-modbus.562372-17": "IO寄存器", "product.product-modbus.562372-18": "IO 寄存器包括线圈、离散寄存器，可以和开关量属性绑定", "product.product-modbus.562372-19": "编辑寄存器", "product.product-modbus.562372-20": "批量导入", "product.product-modbus.562372-21": "导出", "product.product-modbus.562372-22": "保存", "product.product-modbus.562372-23": "取消", "product.product-modbus.562372-24": "序号", "product.product-modbus.562372-25": "属性", "product.product-modbus.562372-26": "关联属性", "product.product-modbus.562372-27": "物模型", "product.product-modbus.562372-28": "标识符", "product.product-modbus.562372-29": "从机地址", "product.product-modbus.562372-30": "寄存器地址", "product.product-modbus.562372-31": "是否只读", "product.product-modbus.562372-32": "只读", "product.product-modbus.562372-33": "读写", "product.product-modbus.562372-34": "操作", "product.product-modbus.562372-35": "删除", "product.product-modbus.562372-36": "添加一个IO寄存器", "product.product-modbus.562372-37": "添加多个IO寄存器", "product.product-modbus.562372-38": "数据寄存器", "product.product-modbus.562372-39": "数据寄存器包括输入寄存器、保持寄存器，可以和数值型属性绑定", "product.product-modbus.562372-40": "批量导入", "product.product-modbus.562372-41": "导出", "product.product-modbus.562372-42": "数据类型", "product.product-modbus.562372-43": "读取个数", "product.product-modbus.562372-44": "添加单个数据寄存器", "product.product-modbus.562372-45": "添加多个数据寄存器", "product.product-modbus.562372-46": "关联属性不能为空", "product.product-modbus.562372-47": "寄存器地址不能为空", "product.product-modbus.562372-48": "是否只读(0-否，1-是)不能为空", "product.product-modbus.562372-49": "modbus数据类型不能为空", "product.product-modbus.562372-50": "读取个数不能为空", "product.product-modbus.562372-51": "寄存器类型 1-IO寄存器 2-数据寄存器不能为空", "product.product-modbus.562372-52": "保存成功", "product.product-modbus.562372-53": "修改成功", "product.product-modbus.562372-311": "位偏移量", "product.product-scada.638785-0": "暂无组态", "product.thimgs-mopdel-list.738493-0": "选择物模型", "product.thimgs-mopdel-list.738493-1": "名称", "product.thimgs-mopdel-list.738493-2": "请输入物模型名称", "product.thimgs-mopdel-list.738493-3": "搜索", "product.thimgs-mopdel-list.738493-4": "重置", "product.thimgs-mopdel-list.738493-5": "物模型", "product.thimgs-mopdel-list.738493-6": "标识符", "product.thimgs-mopdel-list.738493-7": "确定", "product.thimgs-mopdel-list.738493-8": "关 闭", "product.components.batchImportModbus.745343-0": "IO寄存器模板", "product.components.batchImportModbus.745343-1": "数据寄存器模板", "product.category.142342-0": "产品分类名称", "product.category.142342-1": "显示顺序", "product.category.142342-2": "请输入显示顺序", "product.category.142342-3": "请输入内容", "product.category.142342-4": "产品分类名称不能为空", "product.category.142342-5": "是否系统通用不能为空", "product.category.142342-6": "添加产品分类", "product.category.142342-7": "修改产品分类", "product.category.142342-8": "是否确认删除产品分类编号为{0}的数据项？", "product.product-scada.034908-0": "创建组态"}