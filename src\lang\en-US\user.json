{"user.index.098976-0": "Please enter the affiliation institution", "user.index.098976-1": "User account", "user.index.098976-2": "Please enter the user account", "user.index.098976-3": "Mobile phone number", "user.index.098976-4": "Please enter your phone number", "user.index.098976-5": "status", "user.index.098976-6": "User status", "user.index.098976-7": "New Users", "user.index.098976-8": "Display subordinate institution data", "user.index.098976-9": "After selection, this level can view the data of subordinates", "user.index.098976-10": "User account", "user.index.098976-11": "User nickname", "user.index.098976-12": "Belonging organization", "user.index.098976-13": "Mobile phone number", "user.index.098976-14": "more", "user.index.098976-15": "reset password ", "user.index.098976-16": "Please enter the user nickname", "user.index.098976-17": "Please select the affiliation institution", "user.index.098976-18": "Please enter your phone number", "user.index.098976-19": "Email", "user.index.098976-20": "Please enter your email address", "user.index.098976-21": "User password", "user.index.098976-22": "Please enter the user password", "user.index.098976-23": "role", "user.index.098976-24": "Please select a role", "user.index.098976-25": "Do you want to update existing user data", "user.index.098976-26": "Only xls and xlsx format files are allowed to be imported.", "user.index.098976-27": "Download template", "user.index.098976-28": "terminal", "user.index.098976-29": "mechanism", "user.index.098976-30": "User ID", "user.index.098976-31": "User account cannot be empty", "user.index.098976-32": "The length of the user account must be between 2 and 20", "user.index.098976-33": "User nickname cannot be empty", "user.index.098976-34": "User password cannot be empty", "user.index.098976-35": "User password length must be between 5 and 20", "user.index.098976-36": "The role cannot be empty", "user.index.098976-37": "Please enter the correct email address", "user.index.098976-38": "Mobile phone number cannot be empty", "user.index.098976-39": "Please enter the correct phone number", "user.index.098976-40": "Confirm to", "user.index.098976-41": "Is it a user?", "user.index.098976-42": "Add user", "user.index.098976-43": "Modify Users", "user.index.098976-44": "Please enter", "user.index.098976-45": "New password for", "user.index.098976-46": "prompt", "user.index.098976-47": "Successfully modified, the new password is:", "user.index.098976-48": "Are you sure to delete the data item with user ID {0}?", "user.index.098976-49": "User import", "user.profile.index.894502-0": "personal information", "user.profile.index.894502-1": "User Name", "user.profile.index.894502-2": "User email", "user.profile.index.894502-3": "Department", "user.profile.index.894502-4": "Belonging role", "user.profile.index.894502-5": "Creation date", "user.profile.index.894502-6": "WeChat binding", "user.profile.index.894502-7": "Bound, click to unbind", "user.profile.index.894502-8": "Unbound, click to bind", "user.profile.index.894502-9": "Basic information", "user.profile.index.894502-10": "Change password", "user.profile.index.894502-11": "Please enter your password", "user.profile.index.894502-12": "password", "user.resetPwd.450986-0": "Old password", "user.resetPwd.450986-1": "Please enter your old password", "user.resetPwd.450986-2": "New password", "user.resetPwd.450986-3": "Please enter a new password", "user.resetPwd.450986-4": "Confirm password", "user.resetPwd.450986-5": "Please confirm the new password", "user.resetPwd.450986-6": "The passwords entered twice are inconsistent", "user.resetPwd.450986-7": "Old password cannot be empty", "user.resetPwd.450986-8": "The new password cannot be empty", "user.resetPwd.450986-9": "Length between 6 and 20 characters", "user.resetPwd.450986-10": "Confirm password cannot be empty", "user.resetPwd.450986-11": "Click to upload avatar", "user.resetPwd.450986-12": "Modify avatar", "user.resetPwd.450986-13": "The file format is incorrect. Please upload an image type, such as a file with JPG or PNG suffix.", "user.userInfo.560923-0": "Email address cannot be empty", "user.userInfo.560923-1": "Sex", "user.userInfo.560923-2": "men", "user.userInfo.560923-3": "women"}