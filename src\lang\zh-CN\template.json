{"template.index.891112-0": "名称", "template.index.891112-1": "请输入物模型名称", "template.index.891112-2": "类别", "template.index.891112-3": "请选择模型类别", "template.index.891112-7": "标识符", "template.index.891112-8": "图表展示", "template.index.891112-9": "实时监测", "template.index.891112-10": "只读", "template.index.891112-11": "历史存储", "template.index.891112-12": "系统定义", "template.index.891112-13": "物模型类别", "template.index.891112-14": "数据类型", "template.index.891112-15": "数据定义", "template.index.891112-16": "排序", "template.index.891112-17": "创建时间", "template.index.891112-21": "系统定义，不能修改", "template.index.891112-22": "模型名称", "template.index.891112-23": "请输入物模型名称，例如：温度", "template.index.891112-24": "模型标识", "template.index.891112-25": "请输入标识符，例如：temperature", "template.index.891112-26": "模型排序", "template.index.891112-27": "请输入排序", "template.index.891112-28": "模型类别", "template.index.891112-29": "属性", "template.index.891112-30": "功能", "template.index.891112-31": "事件", "template.index.891112-32": "模型特性", "template.index.891112-33": "设备详情中以图表方式展示", "template.index.891112-34": "实时显示监测数据，但是不会存储到数据库", "template.index.891112-35": "设备上报数据，但是平台不能下发指令", "template.index.891112-36": "只读数据", "template.index.891112-37": "设备上报的数据会存储到数据库作为历史数据", "template.index.891112-38": "设备分享时需要指定是否拥有该权限", "template.index.891112-39": "分享权限", "template.index.891112-40": "请选择数据类型", "template.index.891112-41": "整数", "template.index.891112-42": "小数", "template.index.891112-43": "布尔", "template.index.891112-44": "枚举", "template.index.891112-45": "字符串", "template.index.891112-46": "数组", "template.index.891112-47": "对象", "template.index.891112-48": "取值范围", "template.index.891112-49": "最小值", "template.index.891112-50": "到", "template.index.891112-51": "最大值", "template.index.891112-52": "单位", "template.index.891112-53": "请输入单位，例如：℃", "template.index.891112-54": "步长", "template.index.891112-55": "请输入步长，例如：1", "template.index.891112-56": "布尔值", "template.index.891112-57": "例如：关闭", "template.index.891112-58": "（0 值对应文本）", "template.index.891112-59": "例如：打开", "template.index.891112-60": "（1 值对应文本）", "template.index.891112-61": "展示方式", "template.index.891112-62": "请选择展示方式", "template.index.891112-63": "下拉框", "template.index.891112-64": "按钮", "template.index.891112-65": "枚举项", "template.index.891112-66": "参数值，例如：0", "template.index.891112-67": "参数描述，例如：中速档位", "template.index.891112-68": "添加枚举项", "template.index.891112-69": "最大长度", "template.index.891112-70": "例如：1024", "template.index.891112-71": "（字符串的最大长度）", "template.index.891112-72": "元素个数", "template.index.891112-73": "例如：5", "template.index.891112-74": "数组类型", "template.index.891112-75": "对象参数", "template.index.891112-76": "请选择设备", "template.index.891112-78": "添加参数", "template.index.891112-84": "物模型名称不能为空", "template.index.891112-85": "标识符，产品下唯一不能为空", "template.index.891112-86": "模型排序不能为空", "template.index.891112-87": "模型类别不能为空", "template.index.891112-88": "数据类型不能为空", "template.index.891112-89": "添加通用物模型", "template.index.891112-90": "修改通用物模型", "template.index.891112-91": "对象的参数不能为空", "template.index.891112-92": "对象类型模型标识输入不能包含下划线，请重新填写模型标识！", "template.index.891112-93": "参数标识", "template.index.891112-95": "请重新选择数据类型！", "template.index.891112-98": "是否确认删除通用物模型编号为{0}的数据项？", "template.index.891112-103": "低", "template.index.891112-104": "高", "template.index.891112-105": "最大值：", "template.index.891112-106": "最小值：", "template.index.891112-107": "步长：", "template.index.891112-108": "单位：", "template.index.891112-109": "最大长度：", "template.index.891112-110": "数组类型：", "template.index.891112-111": "元素个数：", "template.index.891112-112": "展开", "template.index.891112-113": "收起", "template.paramter.038405-0": "编辑参数", "template.paramter.038405-1": "请输入物模型名称", "template.paramter.038405-2": "搜索", "template.paramter.038405-3": "单击应用模板", "template.paramter.038405-4": "选择", "template.paramter.038405-5": "名称", "template.paramter.038405-6": "标识符", "template.paramter.038405-7": "数据类型", "template.paramter.038405-8": "参数名称", "template.paramter.038405-9": "例如：温度", "template.paramter.038405-10": "参数标识", "template.paramter.038405-11": "例如：temperature", "template.paramter.038405-12": "参数排序", "template.paramter.038405-13": "请输入排序", "template.paramter.038405-14": "参数特性", "template.paramter.038405-15": "图表展示", "template.paramter.038405-16": "实时监测", "template.paramter.038405-17": "只读数据", "template.paramter.038405-18": "历史存储", "template.paramter.038405-19": "分享权限", "template.paramter.038405-20": "数据类型", "template.paramter.038405-21": "请选择数据类型", "template.paramter.038405-22": "整数", "template.paramter.038405-23": "小数", "template.paramter.038405-24": "布尔", "template.paramter.038405-25": "枚举", "template.paramter.038405-26": "字符串", "template.paramter.038405-27": "取值范围", "template.paramter.038405-28": "最小值", "template.paramter.038405-29": "到", "template.paramter.038405-30": "最大值", "template.paramter.038405-31": "单位", "template.paramter.038405-32": "例如：℃", "template.paramter.038405-33": "步长", "template.paramter.038405-34": "例如：1", "template.paramter.038405-35": "布尔值", "template.paramter.038405-36": "例如：关闭", "template.paramter.038405-37": "（0 值对应文本）", "template.paramter.038405-38": "例如：打开", "template.paramter.038405-39": "（1 值对应文本）", "template.paramter.038405-40": "展示方式", "template.paramter.038405-41": "请选择展示方式", "template.paramter.038405-42": "下拉框", "template.paramter.038405-43": "按钮", "template.paramter.038405-44": "枚举项", "template.paramter.038405-45": "例如：0", "template.paramter.038405-46": "例如：中速挡位", "template.paramter.038405-47": "删除", "template.paramter.038405-48": "添加枚举项", "template.paramter.038405-49": "最大长度", "template.paramter.038405-50": "例如：1024", "template.paramter.038405-51": "确 定", "template.paramter.038405-52": "取 消", "template.paramter.038405-53": "参数名称不能为空", "template.paramter.038405-54": "参数标识符不能为空", "template.paramter.038405-55": "模型排序不能为空", "template.paramter.038405-56": "数据类型不能为空", "template.paramter.038405-57": "关闭", "template.paramter.038405-58": "打开", "template.paramter.038405-59": "低", "template.paramter.038405-60": "高"}