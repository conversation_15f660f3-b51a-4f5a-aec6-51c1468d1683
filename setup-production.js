#!/usr/bin/env node

/**
 * 正式环境配置向导
 * 用于配置前端项目连接到正式环境后端服务
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function setupProduction() {
    console.log('🚀 正式环境配置向导');
    console.log('='.repeat(50));
    
    try {
        // 获取正式环境服务器地址
        const serverUrl = await question('请输入正式环境后端服务地址 (例如: http://192.168.1.100:8080): ');
        
        if (!serverUrl.trim()) {
            console.log('❌ 服务器地址不能为空');
            process.exit(1);
        }
        
        // 确保URL格式正确
        let formattedUrl = serverUrl.trim();
        if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
            formattedUrl = 'http://' + formattedUrl;
        }
        if (!formattedUrl.endsWith('/')) {
            formattedUrl += '/';
        }
        
        // 获取MQTT服务器地址
        const mqttUrl = await question(`请输入MQTT服务器地址 (默认: ${formattedUrl.replace('http', 'ws').replace('8080/', '9903/mqtt')}): `);
        
        const finalMqttUrl = mqttUrl.trim() || formattedUrl.replace('http', 'ws').replace('8080/', '9903/mqtt');
        
        // 询问API基础路径
        const apiBase = await question('请输入API基础路径 (默认: /dev-api): ');
        const finalApiBase = apiBase.trim() || '/dev-api';
        
        // 生成配置文件内容
        const envContent = `# 页面标题
VUE_APP_TITLE = BYC物联网系统

# 本地开发连接正式环境配置
ENV = 'production-local'

# API基础路径
VUE_APP_BASE_API = '${finalApiBase}'

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true

# 正式环境后端接口地址
VUE_APP_SERVER_API_URL = '${formattedUrl}'

# 正式环境Mqtt消息服务器连接地址
VUE_APP_MQTT_SERVER_URL = '${finalMqttUrl}'

# 百度地图AK
VUE_APP_BAI_DU_AK = 'nAtaBg9FYzav6c8P9rF9qzsWZfT8O0PD'
`;

        // 写入配置文件
        fs.writeFileSync('.env.production-local', envContent);
        
        console.log('\n✅ 配置完成！');
        console.log('='.repeat(50));
        console.log('📝 配置信息:');
        console.log(`   后端服务地址: ${formattedUrl}`);
        console.log(`   MQTT服务地址: ${finalMqttUrl}`);
        console.log(`   API基础路径: ${finalApiBase}`);
        console.log('\n🚀 启动命令:');
        console.log('   npm run dev:prod');
        console.log('\n📋 配置文件已保存到: .env.production-local');
        
    } catch (error) {
        console.error('❌ 配置过程中出现错误:', error.message);
        process.exit(1);
    } finally {
        rl.close();
    }
}

// 检查网络连接的函数
async function testConnection(url) {
    const axios = require('axios');
    try {
        console.log(`🔍 测试连接: ${url}`);
        const response = await axios.get(url + 'captchaImage', { timeout: 5000 });
        console.log('✅ 连接成功！');
        return true;
    } catch (error) {
        console.log(`❌ 连接失败: ${error.message}`);
        return false;
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--test')) {
        // 测试模式
        if (!fs.existsSync('.env.production-local')) {
            console.log('❌ 配置文件不存在，请先运行配置向导');
            process.exit(1);
        }
        
        const envContent = fs.readFileSync('.env.production-local', 'utf8');
        const serverUrl = envContent.match(/VUE_APP_SERVER_API_URL = '(.+)'/)?.[1];
        
        if (serverUrl) {
            await testConnection(serverUrl);
        } else {
            console.log('❌ 无法从配置文件中读取服务器地址');
        }
    } else {
        // 配置模式
        await setupProduction();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { setupProduction, testConnection };
