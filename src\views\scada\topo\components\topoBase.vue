<script>
import ViewText from './control/ViewText';
import View3DModel from './control/View3DModel';
import ViewImage from './control/ViewImage';
import ViewImageSwitch from './control/ViewImageSwitch';

import ViewCircular from './control/canvas/ViewCircular';
import ViewLine from './control/canvas/ViewLine';
import ViewLineArrow from './control/canvas/ViewLineArrow';
import ViewLineWave from './control/canvas/ViewLineWave';
import ViewBizierCurveArrow from './control/canvas/ViewBizierCurveArrow';
import ViewRect from './control/canvas/ViewRect';
import ViewTriangle from './control/canvas/ViewTriangle';

import ViewChart from './control/chart/ViewChart';
import ViewChartPie from './control/chart/ViewChartPie';
import ViewChartGauge from './control/chart/ViewChartGauge';
import ViewChartWater from './control/chart/ViewChartWater';
import ViewChartTemp from './control/chart/ViewChartTemp';
import View<PERSON>hartMap from './control/chart/ViewChartMap';
import <PERSON><PERSON><PERSON><PERSON>rapper from './control/chart/ViewChartWrapper';

import ViewSvgImage from './control/svg/ViewSvgImage';
import ViewSvgStatic from './control/svg/ViewSvgStatic';
import ViewVideo from './control/ViewVideo';
import ViewTimer from './control/ViewTimer';
import ViewMap from './control/ViewMap';
import ViewWeather from './control/ViewWeather';
import ViewWarn from './control/ViewWarn';
import ViewRealData from './control/ViewRealData';
import ViewHistory from './control/ViewHistory';
import ViewFlowBar from './control/ViewFlowBar';
import ViewFlowBarDynamic from './control/ViewFlowBarDynamic';
import ViewThreeJs from './control/three/ViewThreeJs';
import ViewReliefBall from './control/three/ViewReliefBall';
import ViewKnobSwitch from './control/ViewKnobSwitch';
import ViewTextStatic from './control/ViewTextStatic';
import ViewPanel from './control/ViewPanel';
import ViewOrder from './control/ViewOrder';
import ViewVideoPlay from './control/ViewVideoPlay';
import ViewVideoMp4 from './control/ViewVideoMp4';
import ViewLuckDraw from './control/ViewLuckDraw';
import ViewVR from './control/ViewVR';
import ViewComponent from './control/ViewComponent';

export default {
    name: 'TopoBase',
    components: {
        ViewText,
        View3DModel,
        ViewImage,
        ViewImageSwitch,
        ViewCircular,
        ViewLine,
        ViewLineArrow,
        ViewLineWave,
        ViewBizierCurveArrow,
        ViewRect,
        ViewTriangle,
        ViewChart,
        ViewChartPie,
        ViewChartGauge,
        ViewChartWater,
        ViewChartTemp,
        ViewChartMap,
        ViewChartWrapper,
        ViewSvgImage,
        ViewSvgStatic,
        ViewVideo,
        ViewTimer,
        ViewMap,
        ViewWeather,
        ViewWarn,
        ViewRealData,
        ViewHistory,
        ViewFlowBar,
        ViewFlowBarDynamic,
        ViewThreeJs,
        ViewReliefBall,
        ViewKnobSwitch,
        ViewTextStatic,
        ViewPanel,
        ViewOrder,
        ViewVideoPlay,
        ViewVideoMp4,
        ViewLuckDraw,
        ViewVR,
        ViewComponent,
    },
};
</script>
