<template>
    <div class="model-wrap">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="模型名称" prop="modelName">
                <el-input v-model="queryParams.modelName" placeholder="请输入模型名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="是否弃用" prop="success">
                <el-select v-model="queryParams.success" placeholder="请选择状态" clearable>
                    <el-option label="否" :value="0"></el-option>
                    <el-option label="是" :value="1"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="handleResetQuery">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['scada:model:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['scada:model:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['scada:model:remove']">删除</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['scada:model:export']">导出</el-button>
            </el-col>
            <span class="ml20" style="color: #f56c6c">3D物模管管理，暂时不可用。</span>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="modelList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="模型名称" align="center" prop="modelName" width="200" />
            <el-table-column label="模型地址" align="center" prop="modelUrl" />
            <el-table-column label="是否弃用" align="center" prop="status" width="100">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">{{ scope.row.status === 0 ? '否' : '是' }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="缩略图" align="center" prop="imageUrl" width="120">
                <template slot-scope="scope">
                    <image-preview :src="scope.row.imageUrl" :width="50" :height="50" />
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['scada:model:edit']">修改</el-button>
                    <el-button style="color: #e6a23c" size="mini" type="text" icon="el-icon-view" @click="handlePreview(scope.row)" v-hasPermi="['scada:model:preview']">预览</el-button>
                    <el-button style="color: #f56c6c" size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['scada:model:remove']">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改模型管理对话框 -->
        <el-dialog :title="dialog.title" :visible.sync="dialog.open" width="440px" append-to-body>
            <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
            <el-form ref="dialogForm" :model="dialog.form" :rules="dialog.rules" label-width="78px">
                <el-form-item label="缩略图" prop="imageUrl">
                    <image-upload v-model="dialog.form.imageUrl" :multiple="false" :class="{ disable: uploadDisabled }" />
                </el-form-item>
                <el-form-item label="模型名称" prop="modelName">
                    <el-input v-model="dialog.form.modelName" placeholder="请输入模型名称" clearable />
                </el-form-item>
                <el-form-item label="是否弃用" prop="success">
                    <el-select v-model="dialog.form.success" placeholder="请选择状态" clearable style="width: 100%">
                        <el-option label="否" :value="0"></el-option>
                        <el-option label="是" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="模型地址" prop="modelUrl">
                    <el-input v-model="dialog.form.modelUrl" type="textarea" placeholder="请输入模型地址" clearable />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleDialogSubmit">确 定</el-button>
                <el-button @click="handleDialogCancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { listModel, getModel, delModel, addModel, updateModel } from '@/api/scada/model';

export default {
    name: 'Model',
    computed: {
        uploadDisabled: function () {
            return this.dialog.form.imageUrl !== '';
        },
    },
    data() {
        return {
            loading: true, // 遮罩层
            ids: [], // 选中数组
            single: true, // 非单个禁用
            multiple: true, // 非多个禁用
            showSearch: true, // 显示搜索条件
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                modelName: null,
                status: null,
            },
            modelList: [], // 模型管理表格数据
            total: 0, // 总条数
            dialog: {
                open: false, // 弹出层标题
                title: '', // 对话框标题
                // 表单参数
                form: {
                    imageUrl: '',
                    modelName: '',
                    success: 0,
                    modelUrl: '',
                },
                // 表单校验
                rules: {
                    modelName: [{ required: true, message: '请输入模型名称', trigger: 'change' }],
                    success: [{ required: true, message: '请选择状态', trigger: 'change' }],
                    modelUrl: [{ required: true, message: '请输入模型地址', trigger: 'change' }],
                },
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        // 查询模型管理列表
        getList() {
            this.loading = true;
            listModel(this.queryParams).then((res) => {
                if (res.code === 200) {
                    this.modelList = res.rows;
                    this.total = res.total;
                }
                this.loading = false;
            });
        },
        // 搜索按钮操作
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        // 重置按钮操作
        handleResetQuery() {
            this.resetForm('queryForm');
            this.handleQuery();
        },
        // 新增按钮操作
        handleAdd() {
            this.reset();
            this.dialog.open = true;
            this.dialog.title = '添加模型管理';
        },
        // 表单重置
        reset() {
            this.dialog.form = {
                imageUrl: '',
                modelName: '',
                success: 0,
                modelUrl: '',
            };
            this.resetForm('dialogForm');
        },
        // 取消按钮
        handleDialogCancel() {
            this.dialog.open = false;
        },
        // 提交按钮
        handleDialogSubmit() {
            this.$refs['dialogForm'].validate((valid) => {
                if (valid) {
                    if (this.dialog.form.id != null) {
                        updateModel(this.dialog.form).then((res) => {
                            if (res.code === 200) {
                                this.$modal.msgSuccess('修改成功');
                                this.dialog.open = false;
                                this.getList();
                            }
                        });
                    } else {
                        addModel(this.dialog.form).then((res) => {
                            if (res.code === 200) {
                                this.$modal.msgSuccess('新增成功');
                                this.dialog.open = false;
                                this.getList();
                            }
                        });
                    }
                }
            });
        },
        // 修改按钮操作
        handleUpdate(row) {
            this.dialog.title = '修改模型管理';
            const id = row.id || this.ids;
            getModel(id).then((res) => {
                if (res.code === 200) {
                    this.dialog.form = res.data;
                    this.dialog.open = true;
                }
            });
        },
        // 预览
        handlePreview(row) {
            window.open('http://example.com');
        },
        // 删除按钮操作
        handleDelete(row) {
            const ids = row.id || this.ids;
            this.$modal
                .confirm('是否确认删除模型编号为"' + ids + '"的数据项？')
                .then(function () {
                    return delModel(ids);
                })
                .then(() => {
                    this.getList();
                    this.$modal.msgSuccess('删除成功');
                })
                .catch(() => {});
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map((item) => item.id);
            this.single = selection.length !== 1;
            this.multiple = !selection.length;
        },
        // 导出按钮操作
        handleExport() {
            this.download(
                'scada/model/export',
                {
                    ...this.queryParams,
                },
                `模型${new Date().getTime()}.xlsx`
            );
        },
    },
};
</script>
<style lang="scss" scoped>
.model-wrap {
    padding: 20px;
}

.disable {
    ::v-deep .el-upload--picture-card {
        display: none !important;
    }
}
</style>
