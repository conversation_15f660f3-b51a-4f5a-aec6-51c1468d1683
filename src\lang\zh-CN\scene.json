{"script.349087-0": "脚本标识", "script.349087-1": "请输入脚本标识", "script.349087-2": "脚本名", "script.349087-3": "请输入脚本名", "script.349087-4": "脚本名称", "script.349087-5": "所属产品", "script.349087-6": "脚本事件", "script.349087-7": "脚本动作", "script.349087-8": "脚本语言", "script.349087-9": "执行顺序", "script.349087-10": "脚本事件", "script.349087-11": "请选择脚本事件", "script.349087-12": "请选择脚本动作", "script.349087-13": "脚本状态", "script.349087-14": "请选择产品", "script.349087-15": "选择产品", "script.349087-16": "脚本使用Groovy引擎，查看教程>>>", "script.349087-17": "验 证", "script.349087-18": "脚本标识只能输入字母和数字，且字母开头", "script.349087-19": "脚本标识不能为空", "script.349087-20": "所属产品不能为空", "script.349087-21": "脚本名不能为空", "script.349087-22": "脚本类型不能为空", "script.349087-23": "脚本语言不能为空", "script.349087-24": "状态不能为空", "script.349087-25": "编辑规则脚本", "script.349087-26": "修改规则引擎脚本", "script.349087-27": "是否确认删除规则引擎脚本编号为{0}的数据项？", "script.349087-28": "脚本生成", "script.349087-29": "HTTP服务脚本", "script.349087-30": "MQTT桥接脚本", "script.349087-31": "数据库存储脚本", "script.349087-32": "接入点", "script.349087-33": "请选择接入点", "script.349087-34": "选择接入点", "script.349087-35": "接入点不能为空", "script.349087-36": "日志", "script.349087-37": "刷新", "script.349087-38": "关闭", "script.349087-39": "日志读取中", "script.349087-40": "日志信息", "scene.index.670805-0": "是否告警", "scene.index.670805-1": "是", "scene.index.670805-2": "否", "scene.index.670805-3": "静默时间", "scene.index.670805-4": "分钟", "scene.index.670805-5": "秒钟", "scene.index.670805-6": "延时执行", "scene.index.670805-7": "执行一次", "scene.index.670805-8": "场景状态", "scene.index.670805-9": "触发器", "scene.index.670805-10": "触发条件：", "scene.index.670805-11": "请选择触发条件", "scene.index.670805-12": "不满足", "scene.index.670805-13": "提示：触发器有且只有一个定时，执行动作中的告警无效", "scene.index.670805-14": "数量", "scene.index.670805-15": "自定义CRON", "scene.index.670805-16": "cron执行表达式", "scene.index.670805-17": "生成表达式", "scene.index.670805-18": "请选择类型", "scene.index.670805-19": "请选择父级物模型", "scene.index.670805-20": "请选择操作符", "scene.index.670805-21": "值", "scene.index.670805-22": "请输入字符串", "scene.index.670805-23": "添加触发器", "scene.index.670805-24": "执行动作", "scene.index.670805-25": "在设定的时间范围内将不再重复执行", "scene.index.670805-26": "静默时间：", "scene.index.670805-27": "执行方式：", "scene.index.670805-28": "请选择执行方式", "scene.index.670805-29": "串行(顺序执行)", "scene.index.670805-30": "并行(同时执行)", "scene.index.670805-31": "延时不会存储，限制为90秒内", "scene.index.670805-32": "延时执行：", "scene.index.670805-33": "请选择设备", "scene.index.670805-34": "选择设备", "scene.index.670805-35": "请选择产品", "scene.index.670805-36": "选择产品", "scene.index.670805-37": "添加执行动作", "scene.index.670805-38": "Cron表达式生成器", "scene.index.670805-39": "周一", "scene.index.670805-40": "周二", "scene.index.670805-41": "周三", "scene.index.670805-42": "周四", "scene.index.670805-43": "周五", "scene.index.670805-44": "周六", "scene.index.670805-45": "周日", "scene.index.670805-46": "设备触发", "scene.index.670805-47": "定时触发", "scene.index.670805-48": "产品触发", "scene.index.670805-49": "自定义触发", "scene.index.670805-50": "属性", "scene.index.670805-51": "功能", "scene.index.670805-52": "事件", "scene.index.670805-53": "设备上线", "scene.index.670805-54": "设备下线", "scene.index.670805-55": "设备执行", "scene.index.670805-56": "产品执行", "scene.index.670805-57": "告警执行", "scene.index.670805-58": "告警恢复", "scene.index.670805-59": "场景名称不能为空", "scene.index.670805-60": "延时0-90", "scene.index.670805-61": "延时0-600", "scene.index.670805-62": "添加场景联动", "scene.index.670805-63": "修改场景联动", "scene.index.670805-64": "无单位", "scene.index.670805-65": "是否确认删除场景联动编号为{0}的数据项？", "scene.index.670805-66": "删除成功", "scene.index.670805-67": "触发器中的选项和值不能为空", "scene.index.670805-68": "触发器中区间值不能为空", "scene.index.670805-69": "执行时间不能空", "scene.index.670805-70": "请选择要执行的星期", "scene.index.670805-71": "cron表达式不能为空", "scene.index.670805-72": "执行动作中的选项和值不能为空", "scene.index.670805-73": "修改成功", "scene.index.670805-74": "新增成功", "scene.index.670805-75": "请选择恢复告警场景", "scene.index.670805-76": "选择恢复告警场景", "scene.detail.index.209809-0": "场景概况", "scene.detail.index.209809-1": "组态应用", "scene.detail.index.209809-2": "视频监控", "scene.detail.index.209809-3": "正在加载，请稍候...", "scene.detail.index.209809-4": "全部数据源", "scene.overview.324354-0": "场景信息", "scene.overview.324354-1": "所属组织：", "scene.overview.324354-2": "关联设备：", "scene.overview.324354-3": "更新时间：", "scene.overview.324354-4": "场景基本属性", "scene.overview.324354-5": "场景基本属性还没有维护哦，请前往场景模型中并参考下方流程配置！", "scene.overview.324354-6": "变量概况", "scene.overview.324354-7": "数据来源", "scene.overview.324354-8": "请选择数据来源", "scene.overview.324354-9": "从机名称", "scene.overview.324354-10": "请输入从机名称", "scene.overview.324354-11": "变量名称", "scene.overview.324354-12": "请输入变量名称", "scene.overview.324354-13": "变量ID", "scene.overview.324354-14": "更新时间", "scene.overview.324354-15": "当前值", "scene.overview.324354-16": "历史查询", "scene.overview.324354-17": "请输入数据", "scene.overview.324354-18": "设备未激活", "scene.overview.324354-19": "设备处于禁用状态", "scene.overview.324354-20": "设备处于离线状态", "scene.scada.433893-0": "暂无组态", "scene.scada.433893-1": "暂无视频", "scene.edit.202832-0": "基本信息", "scene.edit.202832-1": "场景名称", "scene.edit.202832-2": "所属机构", "scene.edit.202832-3": "场景图片", "scene.edit.202832-4": "场景描述", "scene.edit.202832-5": "请输入场景名称", "scene.edit.202832-6": "请选择所属机构", "scene.edit.202832-7": "请输入场景描述", "scene.edit.202832-8": "场景配置", "scene.edit.202832-9": "设备配置", "scene.edit.202832-10": "录入型变量", "scene.edit.202832-11": "运算型变量", "scene.edit.202832-12": "选择设备", "scene.edit.202832-13": "序号", "scene.edit.202832-14": "设备名称", "scene.edit.202832-15": "请选择设备", "scene.edit.202832-16": "变量列表", "scene.edit.202832-17": "全部启用", "scene.edit.202832-18": "启用", "scene.edit.202832-19": "变量单位", "scene.edit.202832-20": "变量类型", "scene.edit.202832-21": "数值", "scene.edit.202832-22": "字符串", "scene.edit.202832-23": "存储方式", "scene.edit.202832-24": "不存储", "scene.edit.202832-25": "存储", "scene.edit.202832-26": "读写方式", "scene.edit.202832-27": "读写", "scene.edit.202832-28": "只读", "scene.edit.202832-29": "请输入变量单位", "scene.edit.202832-30": "数据类型", "scene.edit.202832-31": "请选择数据类型", "scene.edit.202832-32": "默认值", "scene.edit.202832-33": "请输入默认值", "scene.edit.202832-34": "时间周期", "scene.edit.202832-35": "周期循环", "scene.edit.202832-36": "适用周期规则的场景，自然日固定周期循环运算一次。", "scene.edit.202832-37": "例如：每天07点运算一次（取值时间范围：今日07点-昨日07点）", "scene.edit.202832-38": "每", "scene.edit.202832-39": "运算一次", "scene.edit.202832-40": "自定义时间段", "scene.edit.202832-41": "适用时间周期不规则的场景，设置时段内的数据参与运算。", "scene.edit.202832-42": "例如：每天02点至次日00点运算一次。", "scene.edit.202832-43": "日", "scene.edit.202832-44": "周", "scene.edit.202832-45": "月", "scene.edit.202832-46": "至", "scene.edit.202832-47": "当日", "scene.edit.202832-48": "次日", "scene.edit.202832-49": "本周", "scene.edit.202832-50": "本月", "scene.edit.202832-51": "运算一次", "scene.edit.202832-52": "添加时段", "scene.edit.202832-53": "变量及计算公式", "scene.edit.202832-54": "支持“引用变量”、运算符号或数字结合使用，运算变量数据经运算公式计算后显示。", "scene.edit.202832-55": "操作说明：点击前方所引用的某个变量前面编号，会自动回填至“运算公式”输入框中，", "scene.edit.202832-56": "如引用变量为“电压”,运算公式中会回填“A”，也支持手动写入大写字母A,运算符号使用规则参考下方说明：", "scene.edit.202832-57": " 加：A+B+10", "scene.edit.202832-58": "减：A-B-10", "scene.edit.202832-59": "乘：A*B*10", "scene.edit.202832-60": "除：A/B/10", "scene.edit.202832-61": " 余数：A%10", "scene.edit.202832-62": "括号：(A+B)*10", "scene.edit.202832-63": "注：请至少输入一个变量编号，支持只输入单个变量编号，", "scene.edit.202832-64": "例如：A 适用时间周期不规则的场景，设置时段内的数据参与运算。", "scene.edit.202832-65": "计算公式", "scene.edit.202832-66": "变量", "scene.edit.202832-67": "请选择变量", "scene.edit.202832-68": "统计方式", "scene.edit.202832-69": "请选择统计方式", "scene.edit.202832-70": "插入变量", "scene.edit.202832-71": "存储方式", "scene.edit.202832-72": "是", "scene.edit.202832-73": "否", "scene.edit.202832-74": "添加变量", "scene.edit.202832-75": "请选择时间周期", "scene.edit.202832-76": "请输入计算公式", "scene.edit.202832-77": "请选择存储方式", "scene.edit.202832-78": "新增变量", "scene.edit.202832-79": "编辑变量", "scene.edit.202832-80": "请插入变量", "scene.edit.202832-81": "运算公式错误", "scene.edit.202832-82": "最大长度支持200个字符", "scene.edit.202832-83": "请输入计算公式", "scene.list.index.079839-0": "机构名称", "scene.list.index.079839-1": "请选择状态", "scene.list.index.079839-2": "停用", "scene.list.index.079839-3": "关联设备数", "scene.list.index.079839-4": "场景描述", "scene.list.index.079839-5": "创建人", "scene.list.index.079839-6": "更新时间", "scene.list.index.079839-7": "组态设计", "scene.list.index.079839-8": "运行组态", "scene.list.index.079839-9": "上传图片", "scene.list.index.079839-10": "添加场景", "scene.list.index.079839-11": "修改场景", "scene.list.index.079839-12": "是否确认删除场景编号为{0}的数据项？", "scene.list.index.079839-13": "该场景暂未创建组态", "scene.list.index.079839-14": "请选择机构名称", "scene.bridgelist.784127-0": "选择桥接", "scene.bridgelist.784127-1": "连接器名称", "scene.bridgelist.784127-2": "请输入连接器名称", "scene.bridgelist.784127-3": "搜索", "scene.bridgelist.784127-4": "重置", "scene.bridgelist.784127-5": "选择", "scene.bridgelist.784127-6": "连接器名称", "scene.bridgelist.784127-7": "是否生效", "scene.bridgelist.784127-8": "状态", "scene.bridgelist.784127-9": "未连接", "scene.bridgelist.784127-10": "连接中", "scene.bridgelist.784127-11": "桥接类型", "scene.bridgelist.784127-12": "Http推送", "scene.bridgelist.784127-13": "Mqtt桥接", "scene.bridgelist.784127-14": "桥接方向", "scene.bridgelist.784127-15": "输入", "scene.bridgelist.784127-16": "取 消", "scene.bridgelist.784127-17": "确 定", "scene.bridgelist.784127-18": "输出"}