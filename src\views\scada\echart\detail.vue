<template>
    <div class="echart-detail-wrap">
        <el-row :gutter="12">
            <el-col :span="12">
                <el-card class="card-wrap">
                    <div slot="header">
                        <span>图表编辑&nbsp;-&nbsp;</span>
                        <a href="https://www.isqqw.com/" target="_blank" style="color: #1890ff">图表库</a>
                        <el-button style="float: right; padding: 3px 0" type="text" @click="loadEchartDatas"
                            icon="el-icon-refresh">运行</el-button>
                        <el-button style="float: right; padding: 3px 0; margin-right: 10px; color: #e6a23c" type="text"
                            @click="handleSubmitForm" v-hasPermi="['scada:echart:edit']">
                            <svg-icon icon-class="save" />
                            保存
                        </el-button>
                    </div>
                    <monaco-editor ref="editor" height="75vh" @change="handleEditorChange"></monaco-editor>
                </el-card>
            </el-col>
            <el-col :span="12">
                <el-card class="card-wrap">
                    <div slot="header">
                        <span>图表展示</span>
                        <el-button style="float: right; padding: 3px 0" type="text" @click="handleDownloadImage"
                            icon="el-icon-download">下载</el-button>
                    </div>
                    <div ref="echart" style="height: 75vh; width: 100%"></div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import { getEchart, updateEchart } from '@/api/scada/echart';
import html2canvas from 'html2canvas';

export default {
    name: 'EchatDetail',
    data() {
        return {
            // 表单参数
            form: {
                echartData: '',
            },
            echart: null,
        };
    },
    mounted() {
        this.getDetail();
    },
    methods: {
        // 获取详情
        getDetail() {
            getEchart(this.$route.query.id).then((res) => {
                if (res.code === 200) {
                    this.form = res.data;
                    this.$refs.editor.setValue(this.form.echartData);
                    this.loadEchartDatas();
                }
            });
        },
        // 数据加载
        loadEchartDatas() {
            if (this.echart) {
                this.echart.dispose(); // 销毁ECharts实例
            }
            let funStr = this.getFun(this.form.echartData);
            let fun = eval('(' + funStr + ')');
            let option = fun(this.echarts);
            let view = this.$refs.echart;
            this.echart = this.$echarts.init(view, 'light');
            this.echart.setOption(option);
        },
        getFun(optionStr) {
            let funStr = 'function (echarts) {\n' + optionStr + '\n' + '    return option;\n' + '}';
            return funStr;
        },
        //编辑器
        handleEditorChange(data) {
            this.form.echartData = data;
        },
        // 提交按钮
        handleSubmitForm() {
            let canvasBox = this.$refs.echart;
            let _this = this;
            _this.$modal.loading('保存中，请稍候...');
            html2canvas(canvasBox).then(function (canvas) {
                _this.form.base64 = canvas.toDataURL('image/png');
                updateEchart(_this.form).then((res) => {
                    if (res.code === 200) {
                        _this.$modal.msgSuccess('修改成功');
                    }
                    _this.$modal.closeLoading();
                });
            });
        },
        // 页面元素转图片
        handleDownloadImage() {
            // 转换成canvas
            let canvasBox = this.$refs.echart;
            let _this = this;
            html2canvas(canvasBox).then(function (canvas) {
                var img = canvas.toDataURL('image/png').replace('image/png', 'image/octet-stream');
                // 创建a标签，实现下载
                var creatIMg = document.createElement('a');
                creatIMg.download = `${_this.form.echartName}.png`; // 设置下载的文件名，
                creatIMg.href = img; // 下载url
                document.body.appendChild(creatIMg);
                creatIMg.click();
                creatIMg.remove(); // 下载之后把创建的元素删除
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.echart-detail-wrap {
    padding: 20px;

    .card-wrap {
        height: 86vh;
    }
}
</style>
