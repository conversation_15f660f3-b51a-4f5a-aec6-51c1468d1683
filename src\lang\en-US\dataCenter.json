{"dataCenter.analysis.349202-0": "Device Name", "dataCenter.analysis.349202-1": "Please select a device name", "dataCenter.analysis.349202-2": "time frame", "dataCenter.analysis.349202-3": "Start date", "dataCenter.analysis.349202-4": "End date", "dataCenter.analysis.349202-5": "search", "dataCenter.analysis.349202-6": "Reset", "dataCenter.analysis.349202-7": "There is currently no data available", "dataCenter.analysis.349202-8": "Last 2 hours", "dataCenter.analysis.349202-9": "Last day", "dataCenter.analysis.349202-10": "In the past seven days", "dataCenter.analysis.349202-11": "Last 30 days", "dataCenter.analysis.349202-12": "total", "dataCenter.analysis.349202-13": "Alarm processing information statistics", "dataCenter.analysis.349202-14": "Equipment usage statistics", "dataCenter.analysis.349202-15": "Variable Name", "dataCenter.analysis.349202-16": "Current value", "dataCenter.analysis.349202-17": "Update time", "dataCenter.analysis.349202-18": "Alarm name ", "dataCenter.analysis.349202-19": "Alarm level", "dataCenter.analysis.349202-20": "Alarm time", "dataCenter.history.384934-0": "equipment", "dataCenter.history.384934-1": "Device Name", "dataCenter.history.384934-2": "Please select a device name", "dataCenter.history.384934-3": "Variable Name", "dataCenter.history.384934-4": "Please select a variable name", "dataCenter.history.384934-5": "time frame", "dataCenter.history.384934-6": "Start date", "dataCenter.history.384934-7": "End date", "dataCenter.history.384934-8": "search", "dataCenter.history.384934-9": "Reset", "dataCenter.history.384934-10": "Curve trend chart", "dataCenter.history.384934-11": "When a single variable exceeds 500 numerical values in a single query, the curve trend graph presents aggregated data", "dataCenter.history.384934-12": "There is currently no data available", "dataCenter.history.384934-13": "Update time", "dataCenter.history.384934-14": "scene", "dataCenter.history.384934-15": "Scene Name", "dataCenter.history.384934-16": "Please select a scene name", "dataCenter.history.384934-17": "data sources ", "dataCenter.history.384934-18": "Please select the data source", "dataCenter.history.384934-19": "Last 2 hours", "dataCenter.history.384934-20": "Last day", "dataCenter.history.384934-21": "In the past seven days", "dataCenter.history.384934-22": "Last 30 days", "dataCenter.history.384934-23": "total"}