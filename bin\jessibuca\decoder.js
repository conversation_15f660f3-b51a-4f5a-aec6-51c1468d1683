!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(require("path"),require("fs"),require("crypto")):"function"==typeof define&&define.amd?define(["path","fs","crypto"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).path,e.fs,e.crypto$1)}(this,(function(e,r,t){"use strict";function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var o=n(e),a=n(r),i=n(t);var s=function(e,r){return e(r={exports:{}},r.exports),r.exports}((function(e){var r,t=void 0!==t?t:{},n=(t={print:function(e){console.log("Jessibuca: [worker]:",e)},printErr:function(e){console.warn("Jessibuca: [worker]:",e),postMessage({cmd:"wasmError",message:e})}},{});for(r in t)t.hasOwnProperty(r)&&(n[r]=t[r]);var s,u="./this.program",c=!1,l=!1,f=!1;c="object"==typeof window,l="function"==typeof importScripts,f="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,s=!c&&!f&&!l;var d,p,h,m,v,g="";f?(g=l?o.default.dirname(g)+"/":__dirname+"/",d=function(e,r){return m||(m=a.default),v||(v=o.default),e=v.normalize(e),m.readFileSync(e,r?null:"utf8")},h=function(e){var r=d(e,!0);return r.buffer||(r=new Uint8Array(r)),P(r.buffer),r},process.argv.length>1&&(u=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),e.exports=t,process.on("uncaughtException",(function(e){if(!(e instanceof ot))throw e})),process.on("unhandledRejection",ne),t.inspect=function(){return"[Emscripten Module object]"}):s?("undefined"!=typeof read&&(d=function(e){return read(e)}),h=function(e){var r;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(P("object"==typeof(r=read(e,"binary"))),r)},"undefined"!=typeof scriptArgs&&scriptArgs,"undefined"!=typeof print&&("undefined"==typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!=typeof printErr?printErr:print)):(c||l)&&(l?g=self.location.href:"undefined"!=typeof document&&document.currentScript&&(g=document.currentScript.src),g=0!==g.indexOf("blob:")?g.substr(0,g.lastIndexOf("/")+1):"",d=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.send(null),r.responseText},l&&(h=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)}),p=function(e,r,t){var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?r(n.response):t()},n.onerror=t,n.send(null)});var y=t.print||console.log.bind(console),w=t.printErr||console.warn.bind(console);for(r in n)n.hasOwnProperty(r)&&(t[r]=n[r]);n=null,t.arguments&&t.arguments,t.thisProgram&&(u=t.thisProgram),t.quit&&t.quit;var E,b,_=16;function k(e){k.shown||(k.shown={}),k.shown[e]||(k.shown[e]=1,w(e))}t.wasmBinary&&(E=t.wasmBinary),t.noExitRuntime,"object"!=typeof WebAssembly&&ne("no native wasm support detected");var T=!1;function P(e,r){e||ne("Assertion failed: "+r)}var D="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function C(e,r,t){for(var n=r+t,o=r;e[o]&&!(o>=n);)++o;if(o-r>16&&e.subarray&&D)return D.decode(e.subarray(r,o));for(var a="";r<o;){var i=e[r++];if(128&i){var s=63&e[r++];if(192!=(224&i)){var u=63&e[r++];if((i=224==(240&i)?(15&i)<<12|s<<6|u:(7&i)<<18|s<<12|u<<6|63&e[r++])<65536)a+=String.fromCharCode(i);else{var c=i-65536;a+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else a+=String.fromCharCode((31&i)<<6|s)}else a+=String.fromCharCode(i)}return a}function A(e,r){return e?C(M,e,r):""}function S(e,r,t,n){if(!(n>0))return 0;for(var o=t,a=t+n-1,i=0;i<e.length;++i){var s=e.charCodeAt(i);if(s>=55296&&s<=57343)s=65536+((1023&s)<<10)|1023&e.charCodeAt(++i);if(s<=127){if(t>=a)break;r[t++]=s}else if(s<=2047){if(t+1>=a)break;r[t++]=192|s>>6,r[t++]=128|63&s}else if(s<=65535){if(t+2>=a)break;r[t++]=224|s>>12,r[t++]=128|s>>6&63,r[t++]=128|63&s}else{if(t+3>=a)break;r[t++]=240|s>>18,r[t++]=128|s>>12&63,r[t++]=128|s>>6&63,r[t++]=128|63&s}}return r[t]=0,t-o}function F(e,r,t){return S(e,M,r,t)}function x(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++t)),n<=127?++r:r+=n<=2047?2:n<=65535?3:4}return r}var R,$,M,O,I,j,U,N,B,W,L="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function z(e,r){for(var t=e,n=t>>1,o=n+r/2;!(n>=o)&&I[n];)++n;if((t=n<<1)-e>32&&L)return L.decode(M.subarray(e,t));for(var a="",i=0;!(i>=r/2);++i){var s=O[e+2*i>>1];if(0==s)break;a+=String.fromCharCode(s)}return a}function H(e,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var n=r,o=(t-=2)<2*e.length?t/2:e.length,a=0;a<o;++a){var i=e.charCodeAt(a);O[r>>1]=i,r+=2}return O[r>>1]=0,r-n}function V(e){return 2*e.length}function X(e,r){for(var t=0,n="";!(t>=r/4);){var o=j[e+4*t>>2];if(0==o)break;if(++t,o>=65536){var a=o-65536;n+=String.fromCharCode(55296|a>>10,56320|1023&a)}else n+=String.fromCharCode(o)}return n}function G(e,r,t){if(void 0===t&&(t=2147483647),t<4)return 0;for(var n=r,o=n+t-4,a=0;a<e.length;++a){var i=e.charCodeAt(a);if(i>=55296&&i<=57343)i=65536+((1023&i)<<10)|1023&e.charCodeAt(++a);if(j[r>>2]=i,(r+=4)+4>o)break}return j[r>>2]=0,r-n}function q(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&++t,r+=4}return r}t.INITIAL_MEMORY;var J=[],Y=[],K=[],Q=[];var Z=0,ee=null;function re(e){Z++,t.monitorRunDependencies&&t.monitorRunDependencies(Z)}function te(e){if(Z--,t.monitorRunDependencies&&t.monitorRunDependencies(Z),0==Z&&ee){var r=ee;ee=null,r()}}function ne(e){throw t.onAbort&&t.onAbort(e),w(e+=""),T=!0,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.",new WebAssembly.RuntimeError(e)}function oe(e,r){return String.prototype.startsWith?e.startsWith(r):0===e.indexOf(r)}t.preloadedImages={},t.preloadedAudios={};var ae="data:application/octet-stream;base64,";function ie(e){return oe(e,ae)}var se="file://";function ue(e){return oe(e,se)}var ce,le,fe="decoder.wasm";function de(e){try{if(e==fe&&E)return new Uint8Array(E);if(h)return h(e);throw"both async and sync fetching of the wasm failed"}catch(e){ne(e)}}function pe(e){for(;e.length>0;){var r=e.shift();if("function"!=typeof r){var n=r.func;"number"==typeof n?void 0===r.arg?W.get(n)():W.get(n)(r.arg):n(void 0===r.arg?null:r.arg)}else r(t)}}ie(fe)||(fe=function(e){return t.locateFile?t.locateFile(e,g):g+e}(fe));var he={splitPath:function(e){return/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1)},normalizeArray:function(e,r){for(var t=0,n=e.length-1;n>=0;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),t++):t&&(e.splice(n,1),t--)}if(r)for(;t;t--)e.unshift("..");return e},normalize:function(e){var r="/"===e.charAt(0),t="/"===e.substr(-1);return(e=he.normalizeArray(e.split("/").filter((function(e){return!!e})),!r).join("/"))||r||(e="."),e&&t&&(e+="/"),(r?"/":"")+e},dirname:function(e){var r=he.splitPath(e),t=r[0],n=r[1];return t||n?(n&&(n=n.substr(0,n.length-1)),t+n):"."},basename:function(e){if("/"===e)return"/";var r=(e=(e=he.normalize(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===r?e:e.substr(r+1)},extname:function(e){return he.splitPath(e)[3]},join:function(){var e=Array.prototype.slice.call(arguments,0);return he.normalize(e.join("/"))},join2:function(e,r){return he.normalize(e+"/"+r)}};var me={resolve:function(){for(var e="",r=!1,t=arguments.length-1;t>=-1&&!r;t--){var n=t>=0?arguments[t]:we.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,r="/"===n.charAt(0)}return(r?"/":"")+(e=he.normalizeArray(e.split("/").filter((function(e){return!!e})),!r).join("/"))||"."},relative:function(e,r){function t(e){for(var r=0;r<e.length&&""===e[r];r++);for(var t=e.length-1;t>=0&&""===e[t];t--);return r>t?[]:e.slice(r,t-r+1)}e=me.resolve(e).substr(1),r=me.resolve(r).substr(1);for(var n=t(e.split("/")),o=t(r.split("/")),a=Math.min(n.length,o.length),i=a,s=0;s<a;s++)if(n[s]!==o[s]){i=s;break}var u=[];for(s=i;s<n.length;s++)u.push("..");return(u=u.concat(o.slice(i))).join("/")}},ve={ttys:[],init:function(){},shutdown:function(){},register:function(e,r){ve.ttys[e]={input:[],output:[],ops:r},we.registerDevice(e,ve.stream_ops)},stream_ops:{open:function(e){var r=ve.ttys[e.node.rdev];if(!r)throw new we.ErrnoError(43);e.tty=r,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,r,t,n,o){if(!e.tty||!e.tty.ops.get_char)throw new we.ErrnoError(60);for(var a=0,i=0;i<n;i++){var s;try{s=e.tty.ops.get_char(e.tty)}catch(e){throw new we.ErrnoError(29)}if(void 0===s&&0===a)throw new we.ErrnoError(6);if(null==s)break;a++,r[t+i]=s}return a&&(e.node.timestamp=Date.now()),a},write:function(e,r,t,n,o){if(!e.tty||!e.tty.ops.put_char)throw new we.ErrnoError(60);try{for(var a=0;a<n;a++)e.tty.ops.put_char(e.tty,r[t+a])}catch(e){throw new we.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),a}},default_tty_ops:{get_char:function(e){if(!e.input.length){var r=null;if(f){var t=Buffer.alloc?Buffer.alloc(256):new Buffer(256),n=0;try{n=m.readSync(process.stdin.fd,t,0,256,null)}catch(e){if(-1==e.toString().indexOf("EOF"))throw e;n=0}r=n>0?t.slice(0,n).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(r=window.prompt("Input: "))&&(r+="\n"):"function"==typeof readline&&null!==(r=readline())&&(r+="\n");if(!r)return null;e.input=Yr(r,!0)}return e.input.shift()},put_char:function(e,r){null===r||10===r?(y(C(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&e.output.length>0&&(y(C(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,r){null===r||10===r?(w(C(e.output,0)),e.output=[]):0!=r&&e.output.push(r)},flush:function(e){e.output&&e.output.length>0&&(w(C(e.output,0)),e.output=[])}}};function ge(e){for(var r=function(e,r){return r||(r=_),Math.ceil(e/r)*r}(e,16384),t=et(r);e<r;)$[t+e++]=0;return t}var ye={ops_table:null,mount:function(e){return ye.createNode(null,"/",16895,0)},createNode:function(e,r,t,n){if(we.isBlkdev(t)||we.isFIFO(t))throw new we.ErrnoError(63);ye.ops_table||(ye.ops_table={dir:{node:{getattr:ye.node_ops.getattr,setattr:ye.node_ops.setattr,lookup:ye.node_ops.lookup,mknod:ye.node_ops.mknod,rename:ye.node_ops.rename,unlink:ye.node_ops.unlink,rmdir:ye.node_ops.rmdir,readdir:ye.node_ops.readdir,symlink:ye.node_ops.symlink},stream:{llseek:ye.stream_ops.llseek}},file:{node:{getattr:ye.node_ops.getattr,setattr:ye.node_ops.setattr},stream:{llseek:ye.stream_ops.llseek,read:ye.stream_ops.read,write:ye.stream_ops.write,allocate:ye.stream_ops.allocate,mmap:ye.stream_ops.mmap,msync:ye.stream_ops.msync}},link:{node:{getattr:ye.node_ops.getattr,setattr:ye.node_ops.setattr,readlink:ye.node_ops.readlink},stream:{}},chrdev:{node:{getattr:ye.node_ops.getattr,setattr:ye.node_ops.setattr},stream:we.chrdev_stream_ops}});var o=we.createNode(e,r,t,n);return we.isDir(o.mode)?(o.node_ops=ye.ops_table.dir.node,o.stream_ops=ye.ops_table.dir.stream,o.contents={}):we.isFile(o.mode)?(o.node_ops=ye.ops_table.file.node,o.stream_ops=ye.ops_table.file.stream,o.usedBytes=0,o.contents=null):we.isLink(o.mode)?(o.node_ops=ye.ops_table.link.node,o.stream_ops=ye.ops_table.link.stream):we.isChrdev(o.mode)&&(o.node_ops=ye.ops_table.chrdev.node,o.stream_ops=ye.ops_table.chrdev.stream),o.timestamp=Date.now(),e&&(e.contents[r]=o,e.timestamp=o.timestamp),o},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,r){var t=e.contents?e.contents.length:0;if(!(t>=r)){r=Math.max(r,t*(t<1048576?2:1.125)>>>0),0!=t&&(r=Math.max(r,256));var n=e.contents;e.contents=new Uint8Array(r),e.usedBytes>0&&e.contents.set(n.subarray(0,e.usedBytes),0)}},resizeFileStorage:function(e,r){if(e.usedBytes!=r)if(0==r)e.contents=null,e.usedBytes=0;else{var t=e.contents;e.contents=new Uint8Array(r),t&&e.contents.set(t.subarray(0,Math.min(r,e.usedBytes))),e.usedBytes=r}},node_ops:{getattr:function(e){var r={};return r.dev=we.isChrdev(e.mode)?e.id:1,r.ino=e.id,r.mode=e.mode,r.nlink=1,r.uid=0,r.gid=0,r.rdev=e.rdev,we.isDir(e.mode)?r.size=4096:we.isFile(e.mode)?r.size=e.usedBytes:we.isLink(e.mode)?r.size=e.link.length:r.size=0,r.atime=new Date(e.timestamp),r.mtime=new Date(e.timestamp),r.ctime=new Date(e.timestamp),r.blksize=4096,r.blocks=Math.ceil(r.size/r.blksize),r},setattr:function(e,r){void 0!==r.mode&&(e.mode=r.mode),void 0!==r.timestamp&&(e.timestamp=r.timestamp),void 0!==r.size&&ye.resizeFileStorage(e,r.size)},lookup:function(e,r){throw we.genericErrors[44]},mknod:function(e,r,t,n){return ye.createNode(e,r,t,n)},rename:function(e,r,t){if(we.isDir(e.mode)){var n;try{n=we.lookupNode(r,t)}catch(e){}if(n)for(var o in n.contents)throw new we.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=t,r.contents[t]=e,r.timestamp=e.parent.timestamp,e.parent=r},unlink:function(e,r){delete e.contents[r],e.timestamp=Date.now()},rmdir:function(e,r){var t=we.lookupNode(e,r);for(var n in t.contents)throw new we.ErrnoError(55);delete e.contents[r],e.timestamp=Date.now()},readdir:function(e){var r=[".",".."];for(var t in e.contents)e.contents.hasOwnProperty(t)&&r.push(t);return r},symlink:function(e,r,t){var n=ye.createNode(e,r,41471,0);return n.link=t,n},readlink:function(e){if(!we.isLink(e.mode))throw new we.ErrnoError(28);return e.link}},stream_ops:{read:function(e,r,t,n,o){var a=e.node.contents;if(o>=e.node.usedBytes)return 0;var i=Math.min(e.node.usedBytes-o,n);if(i>8&&a.subarray)r.set(a.subarray(o,o+i),t);else for(var s=0;s<i;s++)r[t+s]=a[o+s];return i},write:function(e,r,t,n,o,a){if(!n)return 0;var i=e.node;if(i.timestamp=Date.now(),r.subarray&&(!i.contents||i.contents.subarray)){if(a)return i.contents=r.subarray(t,t+n),i.usedBytes=n,n;if(0===i.usedBytes&&0===o)return i.contents=r.slice(t,t+n),i.usedBytes=n,n;if(o+n<=i.usedBytes)return i.contents.set(r.subarray(t,t+n),o),n}if(ye.expandFileStorage(i,o+n),i.contents.subarray&&r.subarray)i.contents.set(r.subarray(t,t+n),o);else for(var s=0;s<n;s++)i.contents[o+s]=r[t+s];return i.usedBytes=Math.max(i.usedBytes,o+n),n},llseek:function(e,r,t){var n=r;if(1===t?n+=e.position:2===t&&we.isFile(e.node.mode)&&(n+=e.node.usedBytes),n<0)throw new we.ErrnoError(28);return n},allocate:function(e,r,t){ye.expandFileStorage(e.node,r+t),e.node.usedBytes=Math.max(e.node.usedBytes,r+t)},mmap:function(e,r,t,n,o,a){if(0!==r)throw new we.ErrnoError(28);if(!we.isFile(e.node.mode))throw new we.ErrnoError(43);var i,s,u=e.node.contents;if(2&a||u.buffer!==R){if((n>0||n+t<u.length)&&(u=u.subarray?u.subarray(n,n+t):Array.prototype.slice.call(u,n,n+t)),s=!0,!(i=ge(t)))throw new we.ErrnoError(48);$.set(u,i)}else s=!1,i=u.byteOffset;return{ptr:i,allocated:s}},msync:function(e,r,t,n,o){if(!we.isFile(e.node.mode))throw new we.ErrnoError(43);return 2&o||ye.stream_ops.write(e,r,0,n,t,!1),0}}},we={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,trackingDelegate:{},tracking:{openFlags:{READ:1,WRITE:2}},ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e,r){if(r=r||{},!(e=me.resolve(we.cwd(),e)))return{path:"",node:null};var t={follow_mount:!0,recurse_count:0};for(var n in t)void 0===r[n]&&(r[n]=t[n]);if(r.recurse_count>8)throw new we.ErrnoError(32);for(var o=he.normalizeArray(e.split("/").filter((function(e){return!!e})),!1),a=we.root,i="/",s=0;s<o.length;s++){var u=s===o.length-1;if(u&&r.parent)break;if(a=we.lookupNode(a,o[s]),i=he.join2(i,o[s]),we.isMountpoint(a)&&(!u||u&&r.follow_mount)&&(a=a.mounted.root),!u||r.follow)for(var c=0;we.isLink(a.mode);){var l=we.readlink(i);if(i=me.resolve(he.dirname(i),l),a=we.lookupPath(i,{recurse_count:r.recurse_count}).node,c++>40)throw new we.ErrnoError(32)}}return{path:i,node:a}},getPath:function(e){for(var r;;){if(we.isRoot(e)){var t=e.mount.mountpoint;return r?"/"!==t[t.length-1]?t+"/"+r:t+r:t}r=r?e.name+"/"+r:e.name,e=e.parent}},hashName:function(e,r){for(var t=0,n=0;n<r.length;n++)t=(t<<5)-t+r.charCodeAt(n)|0;return(e+t>>>0)%we.nameTable.length},hashAddNode:function(e){var r=we.hashName(e.parent.id,e.name);e.name_next=we.nameTable[r],we.nameTable[r]=e},hashRemoveNode:function(e){var r=we.hashName(e.parent.id,e.name);if(we.nameTable[r]===e)we.nameTable[r]=e.name_next;else for(var t=we.nameTable[r];t;){if(t.name_next===e){t.name_next=e.name_next;break}t=t.name_next}},lookupNode:function(e,r){var t=we.mayLookup(e);if(t)throw new we.ErrnoError(t,e);for(var n=we.hashName(e.id,r),o=we.nameTable[n];o;o=o.name_next){var a=o.name;if(o.parent.id===e.id&&a===r)return o}return we.lookup(e,r)},createNode:function(e,r,t,n){var o=new we.FSNode(e,r,t,n);return we.hashAddNode(o),o},destroyNode:function(e){we.hashRemoveNode(e)},isRoot:function(e){return e===e.parent},isMountpoint:function(e){return!!e.mounted},isFile:function(e){return 32768==(61440&e)},isDir:function(e){return 16384==(61440&e)},isLink:function(e){return 40960==(61440&e)},isChrdev:function(e){return 8192==(61440&e)},isBlkdev:function(e){return 24576==(61440&e)},isFIFO:function(e){return 4096==(61440&e)},isSocket:function(e){return 49152==(49152&e)},flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:function(e){var r=we.flagModes[e];if(void 0===r)throw new Error("Unknown file open mode: "+e);return r},flagsToPermissionString:function(e){var r=["r","w","rw"][3&e];return 512&e&&(r+="w"),r},nodePermissions:function(e,r){return we.ignorePermissions||(-1===r.indexOf("r")||292&e.mode)&&(-1===r.indexOf("w")||146&e.mode)&&(-1===r.indexOf("x")||73&e.mode)?0:2},mayLookup:function(e){var r=we.nodePermissions(e,"x");return r||(e.node_ops.lookup?0:2)},mayCreate:function(e,r){try{we.lookupNode(e,r);return 20}catch(e){}return we.nodePermissions(e,"wx")},mayDelete:function(e,r,t){var n;try{n=we.lookupNode(e,r)}catch(e){return e.errno}var o=we.nodePermissions(e,"wx");if(o)return o;if(t){if(!we.isDir(n.mode))return 54;if(we.isRoot(n)||we.getPath(n)===we.cwd())return 10}else if(we.isDir(n.mode))return 31;return 0},mayOpen:function(e,r){return e?we.isLink(e.mode)?32:we.isDir(e.mode)&&("r"!==we.flagsToPermissionString(r)||512&r)?31:we.nodePermissions(e,we.flagsToPermissionString(r)):44},MAX_OPEN_FDS:4096,nextfd:function(e,r){e=e||0,r=r||we.MAX_OPEN_FDS;for(var t=e;t<=r;t++)if(!we.streams[t])return t;throw new we.ErrnoError(33)},getStream:function(e){return we.streams[e]},createStream:function(e,r,t){we.FSStream||(we.FSStream=function(){},we.FSStream.prototype={object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}}});var n=new we.FSStream;for(var o in e)n[o]=e[o];e=n;var a=we.nextfd(r,t);return e.fd=a,we.streams[a]=e,e},closeStream:function(e){we.streams[e]=null},chrdev_stream_ops:{open:function(e){var r=we.getDevice(e.node.rdev);e.stream_ops=r.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:function(){throw new we.ErrnoError(70)}},major:function(e){return e>>8},minor:function(e){return 255&e},makedev:function(e,r){return e<<8|r},registerDevice:function(e,r){we.devices[e]={stream_ops:r}},getDevice:function(e){return we.devices[e]},getMounts:function(e){for(var r=[],t=[e];t.length;){var n=t.pop();r.push(n),t.push.apply(t,n.mounts)}return r},syncfs:function(e,r){"function"==typeof e&&(r=e,e=!1),we.syncFSRequests++,we.syncFSRequests>1&&w("warning: "+we.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var t=we.getMounts(we.root.mount),n=0;function o(e){return we.syncFSRequests--,r(e)}function a(e){if(e)return a.errored?void 0:(a.errored=!0,o(e));++n>=t.length&&o(null)}t.forEach((function(r){if(!r.type.syncfs)return a(null);r.type.syncfs(r,e,a)}))},mount:function(e,r,t){var n,o="/"===t,a=!t;if(o&&we.root)throw new we.ErrnoError(10);if(!o&&!a){var i=we.lookupPath(t,{follow_mount:!1});if(t=i.path,n=i.node,we.isMountpoint(n))throw new we.ErrnoError(10);if(!we.isDir(n.mode))throw new we.ErrnoError(54)}var s={type:e,opts:r,mountpoint:t,mounts:[]},u=e.mount(s);return u.mount=s,s.root=u,o?we.root=u:n&&(n.mounted=s,n.mount&&n.mount.mounts.push(s)),u},unmount:function(e){var r=we.lookupPath(e,{follow_mount:!1});if(!we.isMountpoint(r.node))throw new we.ErrnoError(28);var t=r.node,n=t.mounted,o=we.getMounts(n);Object.keys(we.nameTable).forEach((function(e){for(var r=we.nameTable[e];r;){var t=r.name_next;-1!==o.indexOf(r.mount)&&we.destroyNode(r),r=t}})),t.mounted=null;var a=t.mount.mounts.indexOf(n);t.mount.mounts.splice(a,1)},lookup:function(e,r){return e.node_ops.lookup(e,r)},mknod:function(e,r,t){var n=we.lookupPath(e,{parent:!0}).node,o=he.basename(e);if(!o||"."===o||".."===o)throw new we.ErrnoError(28);var a=we.mayCreate(n,o);if(a)throw new we.ErrnoError(a);if(!n.node_ops.mknod)throw new we.ErrnoError(63);return n.node_ops.mknod(n,o,r,t)},create:function(e,r){return r=void 0!==r?r:438,r&=4095,r|=32768,we.mknod(e,r,0)},mkdir:function(e,r){return r=void 0!==r?r:511,r&=1023,r|=16384,we.mknod(e,r,0)},mkdirTree:function(e,r){for(var t=e.split("/"),n="",o=0;o<t.length;++o)if(t[o]){n+="/"+t[o];try{we.mkdir(n,r)}catch(e){if(20!=e.errno)throw e}}},mkdev:function(e,r,t){return void 0===t&&(t=r,r=438),r|=8192,we.mknod(e,r,t)},symlink:function(e,r){if(!me.resolve(e))throw new we.ErrnoError(44);var t=we.lookupPath(r,{parent:!0}).node;if(!t)throw new we.ErrnoError(44);var n=he.basename(r),o=we.mayCreate(t,n);if(o)throw new we.ErrnoError(o);if(!t.node_ops.symlink)throw new we.ErrnoError(63);return t.node_ops.symlink(t,n,e)},rename:function(e,r){var t,n,o=he.dirname(e),a=he.dirname(r),i=he.basename(e),s=he.basename(r);if(t=we.lookupPath(e,{parent:!0}).node,n=we.lookupPath(r,{parent:!0}).node,!t||!n)throw new we.ErrnoError(44);if(t.mount!==n.mount)throw new we.ErrnoError(75);var u,c=we.lookupNode(t,i),l=me.relative(e,a);if("."!==l.charAt(0))throw new we.ErrnoError(28);if("."!==(l=me.relative(r,o)).charAt(0))throw new we.ErrnoError(55);try{u=we.lookupNode(n,s)}catch(e){}if(c!==u){var f=we.isDir(c.mode),d=we.mayDelete(t,i,f);if(d)throw new we.ErrnoError(d);if(d=u?we.mayDelete(n,s,f):we.mayCreate(n,s))throw new we.ErrnoError(d);if(!t.node_ops.rename)throw new we.ErrnoError(63);if(we.isMountpoint(c)||u&&we.isMountpoint(u))throw new we.ErrnoError(10);if(n!==t&&(d=we.nodePermissions(t,"w")))throw new we.ErrnoError(d);try{we.trackingDelegate.willMovePath&&we.trackingDelegate.willMovePath(e,r)}catch(t){w("FS.trackingDelegate['willMovePath']('"+e+"', '"+r+"') threw an exception: "+t.message)}we.hashRemoveNode(c);try{t.node_ops.rename(c,n,s)}catch(e){throw e}finally{we.hashAddNode(c)}try{we.trackingDelegate.onMovePath&&we.trackingDelegate.onMovePath(e,r)}catch(t){w("FS.trackingDelegate['onMovePath']('"+e+"', '"+r+"') threw an exception: "+t.message)}}},rmdir:function(e){var r=we.lookupPath(e,{parent:!0}).node,t=he.basename(e),n=we.lookupNode(r,t),o=we.mayDelete(r,t,!0);if(o)throw new we.ErrnoError(o);if(!r.node_ops.rmdir)throw new we.ErrnoError(63);if(we.isMountpoint(n))throw new we.ErrnoError(10);try{we.trackingDelegate.willDeletePath&&we.trackingDelegate.willDeletePath(e)}catch(r){w("FS.trackingDelegate['willDeletePath']('"+e+"') threw an exception: "+r.message)}r.node_ops.rmdir(r,t),we.destroyNode(n);try{we.trackingDelegate.onDeletePath&&we.trackingDelegate.onDeletePath(e)}catch(r){w("FS.trackingDelegate['onDeletePath']('"+e+"') threw an exception: "+r.message)}},readdir:function(e){var r=we.lookupPath(e,{follow:!0}).node;if(!r.node_ops.readdir)throw new we.ErrnoError(54);return r.node_ops.readdir(r)},unlink:function(e){var r=we.lookupPath(e,{parent:!0}).node,t=he.basename(e),n=we.lookupNode(r,t),o=we.mayDelete(r,t,!1);if(o)throw new we.ErrnoError(o);if(!r.node_ops.unlink)throw new we.ErrnoError(63);if(we.isMountpoint(n))throw new we.ErrnoError(10);try{we.trackingDelegate.willDeletePath&&we.trackingDelegate.willDeletePath(e)}catch(r){w("FS.trackingDelegate['willDeletePath']('"+e+"') threw an exception: "+r.message)}r.node_ops.unlink(r,t),we.destroyNode(n);try{we.trackingDelegate.onDeletePath&&we.trackingDelegate.onDeletePath(e)}catch(r){w("FS.trackingDelegate['onDeletePath']('"+e+"') threw an exception: "+r.message)}},readlink:function(e){var r=we.lookupPath(e).node;if(!r)throw new we.ErrnoError(44);if(!r.node_ops.readlink)throw new we.ErrnoError(28);return me.resolve(we.getPath(r.parent),r.node_ops.readlink(r))},stat:function(e,r){var t=we.lookupPath(e,{follow:!r}).node;if(!t)throw new we.ErrnoError(44);if(!t.node_ops.getattr)throw new we.ErrnoError(63);return t.node_ops.getattr(t)},lstat:function(e){return we.stat(e,!0)},chmod:function(e,r,t){var n;"string"==typeof e?n=we.lookupPath(e,{follow:!t}).node:n=e;if(!n.node_ops.setattr)throw new we.ErrnoError(63);n.node_ops.setattr(n,{mode:4095&r|-4096&n.mode,timestamp:Date.now()})},lchmod:function(e,r){we.chmod(e,r,!0)},fchmod:function(e,r){var t=we.getStream(e);if(!t)throw new we.ErrnoError(8);we.chmod(t.node,r)},chown:function(e,r,t,n){var o;"string"==typeof e?o=we.lookupPath(e,{follow:!n}).node:o=e;if(!o.node_ops.setattr)throw new we.ErrnoError(63);o.node_ops.setattr(o,{timestamp:Date.now()})},lchown:function(e,r,t){we.chown(e,r,t,!0)},fchown:function(e,r,t){var n=we.getStream(e);if(!n)throw new we.ErrnoError(8);we.chown(n.node,r,t)},truncate:function(e,r){if(r<0)throw new we.ErrnoError(28);var t;"string"==typeof e?t=we.lookupPath(e,{follow:!0}).node:t=e;if(!t.node_ops.setattr)throw new we.ErrnoError(63);if(we.isDir(t.mode))throw new we.ErrnoError(31);if(!we.isFile(t.mode))throw new we.ErrnoError(28);var n=we.nodePermissions(t,"w");if(n)throw new we.ErrnoError(n);t.node_ops.setattr(t,{size:r,timestamp:Date.now()})},ftruncate:function(e,r){var t=we.getStream(e);if(!t)throw new we.ErrnoError(8);if(0==(2097155&t.flags))throw new we.ErrnoError(28);we.truncate(t.node,r)},utime:function(e,r,t){var n=we.lookupPath(e,{follow:!0}).node;n.node_ops.setattr(n,{timestamp:Math.max(r,t)})},open:function(e,r,n,o,a){if(""===e)throw new we.ErrnoError(44);var i;if(n=void 0===n?438:n,n=64&(r="string"==typeof r?we.modeStringToFlags(r):r)?4095&n|32768:0,"object"==typeof e)i=e;else{e=he.normalize(e);try{i=we.lookupPath(e,{follow:!(131072&r)}).node}catch(e){}}var s=!1;if(64&r)if(i){if(128&r)throw new we.ErrnoError(20)}else i=we.mknod(e,n,0),s=!0;if(!i)throw new we.ErrnoError(44);if(we.isChrdev(i.mode)&&(r&=-513),65536&r&&!we.isDir(i.mode))throw new we.ErrnoError(54);if(!s){var u=we.mayOpen(i,r);if(u)throw new we.ErrnoError(u)}512&r&&we.truncate(i,0),r&=-131713;var c=we.createStream({node:i,path:we.getPath(i),flags:r,seekable:!0,position:0,stream_ops:i.stream_ops,ungotten:[],error:!1},o,a);c.stream_ops.open&&c.stream_ops.open(c),!t.logReadFiles||1&r||(we.readFiles||(we.readFiles={}),e in we.readFiles||(we.readFiles[e]=1,w("FS.trackingDelegate error on read file: "+e)));try{if(we.trackingDelegate.onOpenFile){var l=0;1!=(2097155&r)&&(l|=we.tracking.openFlags.READ),0!=(2097155&r)&&(l|=we.tracking.openFlags.WRITE),we.trackingDelegate.onOpenFile(e,l)}}catch(r){w("FS.trackingDelegate['onOpenFile']('"+e+"', flags) threw an exception: "+r.message)}return c},close:function(e){if(we.isClosed(e))throw new we.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{we.closeStream(e.fd)}e.fd=null},isClosed:function(e){return null===e.fd},llseek:function(e,r,t){if(we.isClosed(e))throw new we.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new we.ErrnoError(70);if(0!=t&&1!=t&&2!=t)throw new we.ErrnoError(28);return e.position=e.stream_ops.llseek(e,r,t),e.ungotten=[],e.position},read:function(e,r,t,n,o){if(n<0||o<0)throw new we.ErrnoError(28);if(we.isClosed(e))throw new we.ErrnoError(8);if(1==(2097155&e.flags))throw new we.ErrnoError(8);if(we.isDir(e.node.mode))throw new we.ErrnoError(31);if(!e.stream_ops.read)throw new we.ErrnoError(28);var a=void 0!==o;if(a){if(!e.seekable)throw new we.ErrnoError(70)}else o=e.position;var i=e.stream_ops.read(e,r,t,n,o);return a||(e.position+=i),i},write:function(e,r,t,n,o,a){if(n<0||o<0)throw new we.ErrnoError(28);if(we.isClosed(e))throw new we.ErrnoError(8);if(0==(2097155&e.flags))throw new we.ErrnoError(8);if(we.isDir(e.node.mode))throw new we.ErrnoError(31);if(!e.stream_ops.write)throw new we.ErrnoError(28);e.seekable&&1024&e.flags&&we.llseek(e,0,2);var i=void 0!==o;if(i){if(!e.seekable)throw new we.ErrnoError(70)}else o=e.position;var s=e.stream_ops.write(e,r,t,n,o,a);i||(e.position+=s);try{e.path&&we.trackingDelegate.onWriteToFile&&we.trackingDelegate.onWriteToFile(e.path)}catch(r){w("FS.trackingDelegate['onWriteToFile']('"+e.path+"') threw an exception: "+r.message)}return s},allocate:function(e,r,t){if(we.isClosed(e))throw new we.ErrnoError(8);if(r<0||t<=0)throw new we.ErrnoError(28);if(0==(2097155&e.flags))throw new we.ErrnoError(8);if(!we.isFile(e.node.mode)&&!we.isDir(e.node.mode))throw new we.ErrnoError(43);if(!e.stream_ops.allocate)throw new we.ErrnoError(138);e.stream_ops.allocate(e,r,t)},mmap:function(e,r,t,n,o,a){if(0!=(2&o)&&0==(2&a)&&2!=(2097155&e.flags))throw new we.ErrnoError(2);if(1==(2097155&e.flags))throw new we.ErrnoError(2);if(!e.stream_ops.mmap)throw new we.ErrnoError(43);return e.stream_ops.mmap(e,r,t,n,o,a)},msync:function(e,r,t,n,o){return e&&e.stream_ops.msync?e.stream_ops.msync(e,r,t,n,o):0},munmap:function(e){return 0},ioctl:function(e,r,t){if(!e.stream_ops.ioctl)throw new we.ErrnoError(59);return e.stream_ops.ioctl(e,r,t)},readFile:function(e,r){if((r=r||{}).flags=r.flags||0,r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding)throw new Error('Invalid encoding type "'+r.encoding+'"');var t,n=we.open(e,r.flags),o=we.stat(e).size,a=new Uint8Array(o);return we.read(n,a,0,o,0),"utf8"===r.encoding?t=C(a,0):"binary"===r.encoding&&(t=a),we.close(n),t},writeFile:function(e,r,t){(t=t||{}).flags=t.flags||577;var n=we.open(e,t.flags,t.mode);if("string"==typeof r){var o=new Uint8Array(x(r)+1),a=S(r,o,0,o.length);we.write(n,o,0,a,void 0,t.canOwn)}else{if(!ArrayBuffer.isView(r))throw new Error("Unsupported data type");we.write(n,r,0,r.byteLength,void 0,t.canOwn)}we.close(n)},cwd:function(){return we.currentPath},chdir:function(e){var r=we.lookupPath(e,{follow:!0});if(null===r.node)throw new we.ErrnoError(44);if(!we.isDir(r.node.mode))throw new we.ErrnoError(54);var t=we.nodePermissions(r.node,"x");if(t)throw new we.ErrnoError(t);we.currentPath=r.path},createDefaultDirectories:function(){we.mkdir("/tmp"),we.mkdir("/home"),we.mkdir("/home/<USER>")},createDefaultDevices:function(){we.mkdir("/dev"),we.registerDevice(we.makedev(1,3),{read:function(){return 0},write:function(e,r,t,n,o){return n}}),we.mkdev("/dev/null",we.makedev(1,3)),ve.register(we.makedev(5,0),ve.default_tty_ops),ve.register(we.makedev(6,0),ve.default_tty1_ops),we.mkdev("/dev/tty",we.makedev(5,0)),we.mkdev("/dev/tty1",we.makedev(6,0));var e=function(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues){var e=new Uint8Array(1);return function(){return crypto.getRandomValues(e),e[0]}}if(f)try{var r=i.default;return function(){return r.randomBytes(1)[0]}}catch(e){}return function(){ne("randomDevice")}}();we.createDevice("/dev","random",e),we.createDevice("/dev","urandom",e),we.mkdir("/dev/shm"),we.mkdir("/dev/shm/tmp")},createSpecialDirectories:function(){we.mkdir("/proc");var e=we.mkdir("/proc/self");we.mkdir("/proc/self/fd"),we.mount({mount:function(){var r=we.createNode(e,"fd",16895,73);return r.node_ops={lookup:function(e,r){var t=+r,n=we.getStream(t);if(!n)throw new we.ErrnoError(8);var o={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:function(){return n.path}}};return o.parent=o,o}},r}},{},"/proc/self/fd")},createStandardStreams:function(){t.stdin?we.createDevice("/dev","stdin",t.stdin):we.symlink("/dev/tty","/dev/stdin"),t.stdout?we.createDevice("/dev","stdout",null,t.stdout):we.symlink("/dev/tty","/dev/stdout"),t.stderr?we.createDevice("/dev","stderr",null,t.stderr):we.symlink("/dev/tty1","/dev/stderr"),we.open("/dev/stdin",0),we.open("/dev/stdout",1),we.open("/dev/stderr",1)},ensureErrnoError:function(){we.ErrnoError||(we.ErrnoError=function(e,r){this.node=r,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},we.ErrnoError.prototype=new Error,we.ErrnoError.prototype.constructor=we.ErrnoError,[44].forEach((function(e){we.genericErrors[e]=new we.ErrnoError(e),we.genericErrors[e].stack="<generic error, no stack>"})))},staticInit:function(){we.ensureErrnoError(),we.nameTable=new Array(4096),we.mount(ye,{},"/"),we.createDefaultDirectories(),we.createDefaultDevices(),we.createSpecialDirectories(),we.filesystems={MEMFS:ye}},init:function(e,r,n){we.init.initialized=!0,we.ensureErrnoError(),t.stdin=e||t.stdin,t.stdout=r||t.stdout,t.stderr=n||t.stderr,we.createStandardStreams()},quit:function(){we.init.initialized=!1;var e=t._fflush;e&&e(0);for(var r=0;r<we.streams.length;r++){var n=we.streams[r];n&&we.close(n)}},getMode:function(e,r){var t=0;return e&&(t|=365),r&&(t|=146),t},findObject:function(e,r){var t=we.analyzePath(e,r);return t.exists?t.object:null},analyzePath:function(e,r){try{e=(n=we.lookupPath(e,{follow:!r})).path}catch(e){}var t={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=we.lookupPath(e,{parent:!0});t.parentExists=!0,t.parentPath=n.path,t.parentObject=n.node,t.name=he.basename(e),n=we.lookupPath(e,{follow:!r}),t.exists=!0,t.path=n.path,t.object=n.node,t.name=n.node.name,t.isRoot="/"===n.path}catch(e){t.error=e.errno}return t},createPath:function(e,r,t,n){e="string"==typeof e?e:we.getPath(e);for(var o=r.split("/").reverse();o.length;){var a=o.pop();if(a){var i=he.join2(e,a);try{we.mkdir(i)}catch(e){}e=i}}return i},createFile:function(e,r,t,n,o){var a=he.join2("string"==typeof e?e:we.getPath(e),r),i=we.getMode(n,o);return we.create(a,i)},createDataFile:function(e,r,t,n,o,a){var i=r?he.join2("string"==typeof e?e:we.getPath(e),r):e,s=we.getMode(n,o),u=we.create(i,s);if(t){if("string"==typeof t){for(var c=new Array(t.length),l=0,f=t.length;l<f;++l)c[l]=t.charCodeAt(l);t=c}we.chmod(u,146|s);var d=we.open(u,577);we.write(d,t,0,t.length,0,a),we.close(d),we.chmod(u,s)}return u},createDevice:function(e,r,t,n){var o=he.join2("string"==typeof e?e:we.getPath(e),r),a=we.getMode(!!t,!!n);we.createDevice.major||(we.createDevice.major=64);var i=we.makedev(we.createDevice.major++,0);return we.registerDevice(i,{open:function(e){e.seekable=!1},close:function(e){n&&n.buffer&&n.buffer.length&&n(10)},read:function(e,r,n,o,a){for(var i=0,s=0;s<o;s++){var u;try{u=t()}catch(e){throw new we.ErrnoError(29)}if(void 0===u&&0===i)throw new we.ErrnoError(6);if(null==u)break;i++,r[n+s]=u}return i&&(e.node.timestamp=Date.now()),i},write:function(e,r,t,o,a){for(var i=0;i<o;i++)try{n(r[t+i])}catch(e){throw new we.ErrnoError(29)}return o&&(e.node.timestamp=Date.now()),i}}),we.mkdev(o,a,i)},forceLoadFile:function(e){if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!d)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=Yr(d(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new we.ErrnoError(29)}},createLazyFile:function(e,r,t,n,o){function a(){this.lengthKnown=!1,this.chunks=[]}if(a.prototype.get=function(e){if(!(e>this.length-1||e<0)){var r=e%this.chunkSize,t=e/this.chunkSize|0;return this.getter(t)[r]}},a.prototype.setDataGetter=function(e){this.getter=e},a.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",t,!1),e.send(null),!(e.status>=200&&e.status<300||304===e.status))throw new Error("Couldn't load "+t+". Status: "+e.status);var r,n=Number(e.getResponseHeader("Content-length")),o=(r=e.getResponseHeader("Accept-Ranges"))&&"bytes"===r,a=(r=e.getResponseHeader("Content-Encoding"))&&"gzip"===r,i=1048576;o||(i=n);var s=this;s.setDataGetter((function(e){var r=e*i,o=(e+1)*i-1;if(o=Math.min(o,n-1),void 0===s.chunks[e]&&(s.chunks[e]=function(e,r){if(e>r)throw new Error("invalid range ("+e+", "+r+") or no bytes requested!");if(r>n-1)throw new Error("only "+n+" bytes available! programmer error!");var o=new XMLHttpRequest;if(o.open("GET",t,!1),n!==i&&o.setRequestHeader("Range","bytes="+e+"-"+r),"undefined"!=typeof Uint8Array&&(o.responseType="arraybuffer"),o.overrideMimeType&&o.overrideMimeType("text/plain; charset=x-user-defined"),o.send(null),!(o.status>=200&&o.status<300||304===o.status))throw new Error("Couldn't load "+t+". Status: "+o.status);return void 0!==o.response?new Uint8Array(o.response||[]):Yr(o.responseText||"",!0)}(r,o)),void 0===s.chunks[e])throw new Error("doXHR failed!");return s.chunks[e]})),!a&&n||(i=n=1,n=this.getter(0).length,i=n,y("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=i,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!l)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var i=new a;Object.defineProperties(i,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}});var s={isDevice:!1,contents:i}}else s={isDevice:!1,url:t};var u=we.createFile(e,r,s,n,o);s.contents?u.contents=s.contents:s.url&&(u.contents=null,u.url=s.url),Object.defineProperties(u,{usedBytes:{get:function(){return this.contents.length}}});var c={};return Object.keys(u.stream_ops).forEach((function(e){var r=u.stream_ops[e];c[e]=function(){return we.forceLoadFile(u),r.apply(null,arguments)}})),c.read=function(e,r,t,n,o){we.forceLoadFile(u);var a=e.node.contents;if(o>=a.length)return 0;var i=Math.min(a.length-o,n);if(a.slice)for(var s=0;s<i;s++)r[t+s]=a[o+s];else for(s=0;s<i;s++)r[t+s]=a.get(o+s);return i},u.stream_ops=c,u},createPreloadedFile:function(e,r,n,o,a,i,s,u,c,l){Browser.init();var f=r?me.resolve(he.join2(e,r)):e;function d(n){function d(t){l&&l(),u||we.createDataFile(e,r,t,o,a,c),i&&i(),te()}var p=!1;t.preloadPlugins.forEach((function(e){p||e.canHandle(f)&&(e.handle(n,f,d,(function(){s&&s(),te()})),p=!0)})),p||d(n)}re(),"string"==typeof n?Browser.asyncLoad(n,(function(e){d(e)}),s):d(n)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},DB_NAME:function(){return"EM_FS_"+window.location.pathname},DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:function(e,r,t){r=r||function(){},t=t||function(){};var n=we.indexedDB();try{var o=n.open(we.DB_NAME(),we.DB_VERSION)}catch(e){return t(e)}o.onupgradeneeded=function(){y("creating db"),o.result.createObjectStore(we.DB_STORE_NAME)},o.onsuccess=function(){var n=o.result.transaction([we.DB_STORE_NAME],"readwrite"),a=n.objectStore(we.DB_STORE_NAME),i=0,s=0,u=e.length;function c(){0==s?r():t()}e.forEach((function(e){var r=a.put(we.analyzePath(e).object.contents,e);r.onsuccess=function(){++i+s==u&&c()},r.onerror=function(){s++,i+s==u&&c()}})),n.onerror=t},o.onerror=t},loadFilesFromDB:function(e,r,t){r=r||function(){},t=t||function(){};var n=we.indexedDB();try{var o=n.open(we.DB_NAME(),we.DB_VERSION)}catch(e){return t(e)}o.onupgradeneeded=t,o.onsuccess=function(){var n=o.result;try{var a=n.transaction([we.DB_STORE_NAME],"readonly")}catch(e){return void t(e)}var i=a.objectStore(we.DB_STORE_NAME),s=0,u=0,c=e.length;function l(){0==u?r():t()}e.forEach((function(e){var r=i.get(e);r.onsuccess=function(){we.analyzePath(e).exists&&we.unlink(e),we.createDataFile(he.dirname(e),he.basename(e),r.result,!0,!0,!0),++s+u==c&&l()},r.onerror=function(){u++,s+u==c&&l()}})),a.onerror=t},o.onerror=t}},Ee={mappings:{},DEFAULT_POLLMASK:5,umask:511,calculateAt:function(e,r,t){if("/"===r[0])return r;var n;if(-100===e)n=we.cwd();else{var o=we.getStream(e);if(!o)throw new we.ErrnoError(8);n=o.path}if(0==r.length){if(!t)throw new we.ErrnoError(44);return n}return he.join2(n,r)},doStat:function(e,r,t){try{var n=e(r)}catch(e){if(e&&e.node&&he.normalize(r)!==he.normalize(we.getPath(e.node)))return-54;throw e}return j[t>>2]=n.dev,j[t+4>>2]=0,j[t+8>>2]=n.ino,j[t+12>>2]=n.mode,j[t+16>>2]=n.nlink,j[t+20>>2]=n.uid,j[t+24>>2]=n.gid,j[t+28>>2]=n.rdev,j[t+32>>2]=0,le=[n.size>>>0,(ce=n.size,+Math.abs(ce)>=1?ce>0?(0|Math.min(+Math.floor(ce/4294967296),4294967295))>>>0:~~+Math.ceil((ce-+(~~ce>>>0))/4294967296)>>>0:0)],j[t+40>>2]=le[0],j[t+44>>2]=le[1],j[t+48>>2]=4096,j[t+52>>2]=n.blocks,j[t+56>>2]=n.atime.getTime()/1e3|0,j[t+60>>2]=0,j[t+64>>2]=n.mtime.getTime()/1e3|0,j[t+68>>2]=0,j[t+72>>2]=n.ctime.getTime()/1e3|0,j[t+76>>2]=0,le=[n.ino>>>0,(ce=n.ino,+Math.abs(ce)>=1?ce>0?(0|Math.min(+Math.floor(ce/4294967296),4294967295))>>>0:~~+Math.ceil((ce-+(~~ce>>>0))/4294967296)>>>0:0)],j[t+80>>2]=le[0],j[t+84>>2]=le[1],0},doMsync:function(e,r,t,n,o){var a=M.slice(e,e+t);we.msync(r,a,o,t,n)},doMkdir:function(e,r){return"/"===(e=he.normalize(e))[e.length-1]&&(e=e.substr(0,e.length-1)),we.mkdir(e,r,0),0},doMknod:function(e,r,t){switch(61440&r){case 32768:case 8192:case 24576:case 4096:case 49152:break;default:return-28}return we.mknod(e,r,t),0},doReadlink:function(e,r,t){if(t<=0)return-28;var n=we.readlink(e),o=Math.min(t,x(n)),a=$[r+o];return F(n,r,t+1),$[r+o]=a,o},doAccess:function(e,r){if(-8&r)return-28;var t;if(!(t=we.lookupPath(e,{follow:!0}).node))return-44;var n="";return 4&r&&(n+="r"),2&r&&(n+="w"),1&r&&(n+="x"),n&&we.nodePermissions(t,n)?-2:0},doDup:function(e,r,t){var n=we.getStream(t);return n&&we.close(n),we.open(e,r,0,t,t).fd},doReadv:function(e,r,t,n){for(var o=0,a=0;a<t;a++){var i=j[r+8*a>>2],s=j[r+(8*a+4)>>2],u=we.read(e,$,i,s,n);if(u<0)return-1;if(o+=u,u<s)break}return o},doWritev:function(e,r,t,n){for(var o=0,a=0;a<t;a++){var i=j[r+8*a>>2],s=j[r+(8*a+4)>>2],u=we.write(e,$,i,s,n);if(u<0)return-1;o+=u}return o},varargs:void 0,get:function(){return Ee.varargs+=4,j[Ee.varargs-4>>2]},getStr:function(e){return A(e)},getStreamFromFD:function(e){var r=we.getStream(e);if(!r)throw new we.ErrnoError(8);return r},get64:function(e,r){return e}};function be(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var _e=void 0;function ke(e){for(var r="",t=e;M[t];)r+=_e[M[t++]];return r}var Te={},Pe={},De={},Ce=48,Ae=57;function Se(e){if(void 0===e)return"_unknown";var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=Ce&&r<=Ae?"_"+e:e}function Fe(e,r){return e=Se(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function xe(e,r){var t=Fe(r,(function(e){this.name=r,this.message=e;var t=new Error(e).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var Re=void 0;function $e(e){throw new Re(e)}var Me=void 0;function Oe(e){throw new Me(e)}function Ie(e,r,t){function n(r){var n=t(r);n.length!==e.length&&Oe("Mismatched type converter count");for(var o=0;o<e.length;++o)je(e[o],n[o])}e.forEach((function(e){De[e]=r}));var o=new Array(r.length),a=[],i=0;r.forEach((function(e,r){Pe.hasOwnProperty(e)?o[r]=Pe[e]:(a.push(e),Te.hasOwnProperty(e)||(Te[e]=[]),Te[e].push((function(){o[r]=Pe[e],++i===a.length&&n(o)})))})),0===a.length&&n(o)}function je(e,r,t){if(t=t||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=r.name;if(e||$e('type "'+n+'" must have a positive integer typeid pointer'),Pe.hasOwnProperty(e)){if(t.ignoreDuplicateRegistrations)return;$e("Cannot register type '"+n+"' twice")}if(Pe[e]=r,delete De[e],Te.hasOwnProperty(e)){var o=Te[e];delete Te[e],o.forEach((function(e){e()}))}}function Ue(e){if(!(this instanceof Ke))return!1;if(!(e instanceof Ke))return!1;for(var r=this.$$.ptrType.registeredClass,t=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;r.baseClass;)t=r.upcast(t),r=r.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return r===n&&t===o}function Ne(e){$e(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Be=!1;function We(e){}function Le(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function ze(e){return"undefined"==typeof FinalizationGroup?(ze=function(e){return e},e):(Be=new FinalizationGroup((function(e){for(var r=e.next();!r.done;r=e.next()){var t=r.value;t.ptr?Le(t):console.warn("object already deleted: "+t.ptr)}})),ze=function(e){return Be.register(e,e.$$,e.$$),e},We=function(e){Be.unregister(e.$$)},ze(e))}function He(){if(this.$$.ptr||Ne(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,r=ze(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return r.$$.count.value+=1,r.$$.deleteScheduled=!1,r}function Ve(){this.$$.ptr||Ne(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&$e("Object already scheduled for deletion"),We(this),Le(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Xe(){return!this.$$.ptr}var Ge=void 0,qe=[];function Je(){for(;qe.length;){var e=qe.pop();e.$$.deleteScheduled=!1,e.delete()}}function Ye(){return this.$$.ptr||Ne(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&$e("Object already scheduled for deletion"),qe.push(this),1===qe.length&&Ge&&Ge(Je),this.$$.deleteScheduled=!0,this}function Ke(){}var Qe={};function Ze(e,r,t){if(void 0===e[r].overloadTable){var n=e[r];e[r]=function(){return e[r].overloadTable.hasOwnProperty(arguments.length)||$e("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[r].overloadTable+")!"),e[r].overloadTable[arguments.length].apply(this,arguments)},e[r].overloadTable=[],e[r].overloadTable[n.argCount]=n}}function er(e,r,t,n,o,a,i,s){this.name=e,this.constructor=r,this.instancePrototype=t,this.rawDestructor=n,this.baseClass=o,this.getActualType=a,this.upcast=i,this.downcast=s,this.pureVirtualFunctions=[]}function rr(e,r,t){for(;r!==t;)r.upcast||$e("Expected null or instance of "+t.name+", got an instance of "+r.name),e=r.upcast(e),r=r.baseClass;return e}function tr(e,r){if(null===r)return this.isReference&&$e("null is not a valid "+this.name),0;r.$$||$e('Cannot pass "'+$r(r)+'" as a '+this.name),r.$$.ptr||$e("Cannot pass deleted object as a pointer of type "+this.name);var t=r.$$.ptrType.registeredClass;return rr(r.$$.ptr,t,this.registeredClass)}function nr(e,r){var t;if(null===r)return this.isReference&&$e("null is not a valid "+this.name),this.isSmartPointer?(t=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,t),t):0;r.$$||$e('Cannot pass "'+$r(r)+'" as a '+this.name),r.$$.ptr||$e("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&r.$$.ptrType.isConst&&$e("Cannot convert argument of type "+(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name)+" to parameter type "+this.name);var n=r.$$.ptrType.registeredClass;if(t=rr(r.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===r.$$.smartPtr&&$e("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:r.$$.smartPtrType===this?t=r.$$.smartPtr:$e("Cannot convert argument of type "+(r.$$.smartPtrType?r.$$.smartPtrType.name:r.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:t=r.$$.smartPtr;break;case 2:if(r.$$.smartPtrType===this)t=r.$$.smartPtr;else{var o=r.clone();t=this.rawShare(t,Rr((function(){o.delete()}))),null!==e&&e.push(this.rawDestructor,t)}break;default:$e("Unsupporting sharing policy")}return t}function or(e,r){if(null===r)return this.isReference&&$e("null is not a valid "+this.name),0;r.$$||$e('Cannot pass "'+$r(r)+'" as a '+this.name),r.$$.ptr||$e("Cannot pass deleted object as a pointer of type "+this.name),r.$$.ptrType.isConst&&$e("Cannot convert argument of type "+r.$$.ptrType.name+" to parameter type "+this.name);var t=r.$$.ptrType.registeredClass;return rr(r.$$.ptr,t,this.registeredClass)}function ar(e){return this.fromWireType(U[e>>2])}function ir(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function sr(e){this.rawDestructor&&this.rawDestructor(e)}function ur(e){null!==e&&e.delete()}function cr(e,r,t){if(r===t)return e;if(void 0===t.baseClass)return null;var n=cr(e,r,t.baseClass);return null===n?null:t.downcast(n)}function lr(){return Object.keys(pr).length}function fr(){var e=[];for(var r in pr)pr.hasOwnProperty(r)&&e.push(pr[r]);return e}function dr(e){Ge=e,qe.length&&Ge&&Ge(Je)}var pr={};function hr(e,r){return r=function(e,r){for(void 0===r&&$e("ptr should not be undefined");e.baseClass;)r=e.upcast(r),e=e.baseClass;return r}(e,r),pr[r]}function mr(e,r){return r.ptrType&&r.ptr||Oe("makeClassHandle requires ptr and ptrType"),!!r.smartPtrType!==!!r.smartPtr&&Oe("Both smartPtrType and smartPtr must be specified"),r.count={value:1},ze(Object.create(e,{$$:{value:r}}))}function vr(e){var r=this.getPointee(e);if(!r)return this.destructor(e),null;var t=hr(this.registeredClass,r);if(void 0!==t){if(0===t.$$.count.value)return t.$$.ptr=r,t.$$.smartPtr=e,t.clone();var n=t.clone();return this.destructor(e),n}function o(){return this.isSmartPointer?mr(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:r,smartPtrType:this,smartPtr:e}):mr(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var a,i=this.registeredClass.getActualType(r),s=Qe[i];if(!s)return o.call(this);a=this.isConst?s.constPointerType:s.pointerType;var u=cr(r,this.registeredClass,a.registeredClass);return null===u?o.call(this):this.isSmartPointer?mr(a.registeredClass.instancePrototype,{ptrType:a,ptr:u,smartPtrType:this,smartPtr:e}):mr(a.registeredClass.instancePrototype,{ptrType:a,ptr:u})}function gr(e,r,t,n,o,a,i,s,u,c,l){this.name=e,this.registeredClass=r,this.isReference=t,this.isConst=n,this.isSmartPointer=o,this.pointeeType=a,this.sharingPolicy=i,this.rawGetPointee=s,this.rawConstructor=u,this.rawShare=c,this.rawDestructor=l,o||void 0!==r.baseClass?this.toWireType=nr:n?(this.toWireType=tr,this.destructorFunction=null):(this.toWireType=or,this.destructorFunction=null)}function yr(e,r,n){return-1!=e.indexOf("j")?function(e,r,n){var o=t["dynCall_"+e];return n&&n.length?o.apply(null,[r].concat(n)):o.call(null,r)}(e,r,n):W.get(r).apply(null,n)}function wr(e,r){var t,n,o,a=-1!=(e=ke(e)).indexOf("j")?(t=e,n=r,o=[],function(){o.length=arguments.length;for(var e=0;e<arguments.length;e++)o[e]=arguments[e];return yr(t,n,o)}):W.get(r);return"function"!=typeof a&&$e("unknown function pointer with signature "+e+": "+r),a}var Er=void 0;function br(e){var r=nt(e),t=ke(r);return Zr(r),t}function _r(e,r){var t=[],n={};throw r.forEach((function e(r){n[r]||Pe[r]||(De[r]?De[r].forEach(e):(t.push(r),n[r]=!0))})),new Er(e+": "+t.map(br).join([", "]))}function kr(e,r){for(var t=[],n=0;n<e;n++)t.push(j[(r>>2)+n]);return t}function Tr(e){for(;e.length;){var r=e.pop();e.pop()(r)}}function Pr(e,r){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var t=Fe(e.name||"unknownFunctionName",(function(){}));t.prototype=e.prototype;var n=new t,o=e.apply(n,r);return o instanceof Object?o:n}function Dr(e,r,t){return e instanceof Object||$e(t+' with invalid "this": '+e),e instanceof r.registeredClass.constructor||$e(t+' incompatible with "this" of type '+e.constructor.name),e.$$.ptr||$e("cannot call emscripten binding method "+t+" on deleted object"),rr(e.$$.ptr,e.$$.ptrType.registeredClass,r.registeredClass)}var Cr=[],Ar=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Sr(e){e>4&&0==--Ar[e].refcount&&(Ar[e]=void 0,Cr.push(e))}function Fr(){for(var e=0,r=5;r<Ar.length;++r)void 0!==Ar[r]&&++e;return e}function xr(){for(var e=5;e<Ar.length;++e)if(void 0!==Ar[e])return Ar[e];return null}function Rr(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=Cr.length?Cr.pop():Ar.length;return Ar[r]={refcount:1,value:e},r}}function $r(e){if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e}function Mr(e,r){switch(r){case 2:return function(e){return this.fromWireType(N[e>>2])};case 3:return function(e){return this.fromWireType(B[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Or(e,r,t){switch(r){case 0:return t?function(e){return $[e]}:function(e){return M[e]};case 1:return t?function(e){return O[e>>1]}:function(e){return I[e>>1]};case 2:return t?function(e){return j[e>>2]}:function(e){return U[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function Ir(e){return e||$e("Cannot use deleted val. handle = "+e),Ar[e].value}function jr(e,r){var t=Pe[e];return void 0===t&&$e(r+" has unknown type "+br(e)),t}var Ur={};var Nr=[];function Br(e,r){return(e>>>0)+4294967296*r}function Wr(e,r){if(e<=0)return e;var t=r<=32?Math.abs(1<<r-1):Math.pow(2,r-1);return e>=t&&(r<=32||e>t)&&(e=-2*t+e),e}function Lr(e,r){return e>=0?e:r<=32?2*Math.abs(1<<r-1)+e:Math.pow(2,r)+e}function zr(e){if(!e||!e.callee||!e.callee.name)return[null,"",""];e.callee.toString();var r=e.callee.name,t="(",n=!0;for(var o in e){var a=e[o];n||(t+=", "),n=!1,t+="number"==typeof a||"string"==typeof a?a:"("+typeof a+")"}t+=")";var i=e.callee.caller;return n&&(t=""),[e=i?i.arguments:[],r,t]}function Hr(e){var r=function(){var e=new Error;if(!e.stack){try{throw new Error}catch(r){e=r}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()}(),t=r.lastIndexOf("_emscripten_log"),n=r.lastIndexOf("_emscripten_get_callstack"),o=r.indexOf("\n",Math.max(t,n))+1;r=r.slice(o),32&e&&k("EM_LOG_DEMANGLE is deprecated; ignoring"),8&e&&"undefined"==typeof emscripten_source_map&&(k('Source map information is not available, emscripten_log with EM_LOG_C_STACK will be ignored. Build with "--pre-js $EMSCRIPTEN/src/emscripten-source-map.min.js" linker flag to add source map loading to code.'),e^=8,e|=16);var a=null;if(128&e)for(a=zr(arguments);a[1].indexOf("_emscripten_")>=0;)a=zr(a[0]);var i=r.split("\n");r="";var s=new RegExp("\\s*(.*?)@(.*?):([0-9]+):([0-9]+)"),u=new RegExp("\\s*(.*?)@(.*):(.*)(:(.*))?"),c=new RegExp("\\s*at (.*?) \\((.*):(.*):(.*)\\)");for(var l in i){var f=i[l],d="",p="",h=0,m=0,v=c.exec(f);if(v&&5==v.length)d=v[1],p=v[2],h=v[3],m=v[4];else{if((v=s.exec(f))||(v=u.exec(f)),!(v&&v.length>=4)){r+=f+"\n";continue}d=v[1],p=v[2],h=v[3],m=0|v[4]}var g=!1;if(8&e){var y=emscripten_source_map.originalPositionFor({line:h,column:m});(g=y&&y.source)&&(64&e&&(y.source=y.source.substring(y.source.replace(/\\/g,"/").lastIndexOf("/")+1)),r+="    at "+d+" ("+y.source+":"+y.line+":"+y.column+")\n")}(16&e||!g)&&(64&e&&(p=p.substring(p.replace(/\\/g,"/").lastIndexOf("/")+1)),r+=(g?"     = "+d:"    at "+d)+" ("+p+":"+h+":"+m+")\n"),128&e&&a[0]&&(a[1]==d&&a[2].length>0&&(r=r.replace(/\s+$/,""),r+=" with values: "+a[1]+a[2]+"\n"),a=zr(a[0]))}return r=r.replace(/\s+$/,"")}var Vr={};function Xr(){if(!Xr.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:u||"./this.program"};for(var r in Vr)e[r]=Vr[r];var t=[];for(var r in e)t.push(r+"="+e[r]);Xr.strings=t}return Xr.strings}var Gr=function(e,r,t,n){e||(e=this),this.parent=e,this.mount=e.mount,this.mounted=null,this.id=we.nextInode++,this.name=r,this.mode=t,this.node_ops={},this.stream_ops={},this.rdev=n},qr=365,Jr=146;function Yr(e,r,t){var n=t>0?t:x(e)+1,o=new Array(n),a=S(e,o,0,o.length);return r&&(o.length=a),o}Object.defineProperties(Gr.prototype,{read:{get:function(){return(this.mode&qr)===qr},set:function(e){e?this.mode|=qr:this.mode&=-366}},write:{get:function(){return(this.mode&Jr)===Jr},set:function(e){e?this.mode|=Jr:this.mode&=-147}},isFolder:{get:function(){return we.isDir(this.mode)}},isDevice:{get:function(){return we.isChrdev(this.mode)}}}),we.FSNode=Gr,we.staticInit(),function(){for(var e=new Array(256),r=0;r<256;++r)e[r]=String.fromCharCode(r);_e=e}(),Re=t.BindingError=xe(Error,"BindingError"),Me=t.InternalError=xe(Error,"InternalError"),Ke.prototype.isAliasOf=Ue,Ke.prototype.clone=He,Ke.prototype.delete=Ve,Ke.prototype.isDeleted=Xe,Ke.prototype.deleteLater=Ye,gr.prototype.getPointee=ir,gr.prototype.destructor=sr,gr.prototype.argPackAdvance=8,gr.prototype.readValueFromPointer=ar,gr.prototype.deleteObject=ur,gr.prototype.fromWireType=vr,t.getInheritedInstanceCount=lr,t.getLiveInheritedInstances=fr,t.flushPendingDeletes=Je,t.setDelayFunction=dr,Er=t.UnboundTypeError=xe(Error,"UnboundTypeError"),t.count_emval_handles=Fr,t.get_first_emval=xr;var Kr={x:function(e,r,t){Ee.varargs=t;try{var n=Ee.getStreamFromFD(e);switch(r){case 0:return(o=Ee.get())<0?-28:we.open(n.path,n.flags,0,o).fd;case 1:case 2:case 13:case 14:return 0;case 3:return n.flags;case 4:var o=Ee.get();return n.flags|=o,0;case 12:o=Ee.get();return O[o+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return a=28,j[rt()>>2]=a,-1}}catch(e){return void 0!==we&&e instanceof we.ErrnoError||ne(e),-e.errno}var a},w:function(e,r,t){Ee.varargs=t;try{var n=Ee.getStr(e),o=t?Ee.get():0;return we.open(n,r,o).fd}catch(e){return void 0!==we&&e instanceof we.ErrnoError||ne(e),-e.errno}},C:function(e,r,t,n,o){var a=be(t);je(e,{name:r=ke(r),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?n:o},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===t)n=$;else if(2===t)n=O;else{if(4!==t)throw new TypeError("Unknown boolean type size: "+r);n=j}return this.fromWireType(n[e>>a])},destructorFunction:null})},n:function(e,r,n,o,a,i,s,u,c,l,f,d,p){f=ke(f),i=wr(a,i),u&&(u=wr(s,u)),l&&(l=wr(c,l)),p=wr(d,p);var h=Se(f);!function(e,r,n){t.hasOwnProperty(e)?((void 0===n||void 0!==t[e].overloadTable&&void 0!==t[e].overloadTable[n])&&$e("Cannot register public name '"+e+"' twice"),Ze(t,e,e),t.hasOwnProperty(n)&&$e("Cannot register multiple overloads of a function with the same number of arguments ("+n+")!"),t[e].overloadTable[n]=r):(t[e]=r,void 0!==n&&(t[e].numArguments=n))}(h,(function(){_r("Cannot construct "+f+" due to unbound types",[o])})),Ie([e,r,n],o?[o]:[],(function(r){var n,a;r=r[0],a=o?(n=r.registeredClass).instancePrototype:Ke.prototype;var s=Fe(h,(function(){if(Object.getPrototypeOf(this)!==c)throw new Re("Use 'new' to construct "+f);if(void 0===d.constructor_body)throw new Re(f+" has no accessible constructor");var e=d.constructor_body[arguments.length];if(void 0===e)throw new Re("Tried to invoke ctor of "+f+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(d.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),c=Object.create(a,{constructor:{value:s}});s.prototype=c;var d=new er(f,s,c,p,n,i,u,l),m=new gr(f,d,!0,!1,!1),v=new gr(f+"*",d,!1,!1,!1),g=new gr(f+" const*",d,!1,!0,!1);return Qe[e]={pointerType:v,constPointerType:g},function(e,r,n){t.hasOwnProperty(e)||Oe("Replacing nonexistant public symbol"),void 0!==t[e].overloadTable&&void 0!==n?t[e].overloadTable[n]=r:(t[e]=r,t[e].argCount=n)}(h,s),[m,v,g]}))},i:function(e,r,t,n,o,a){P(r>0);var i=kr(r,t);o=wr(n,o);var s=[a],u=[];Ie([],[e],(function(e){var t="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[r-1])throw new Re("Cannot register multiple constructors with identical number of parameters ("+(r-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[r-1]=function(){_r("Cannot construct "+e.name+" due to unbound types",i)},Ie([],i,(function(n){return e.registeredClass.constructor_body[r-1]=function(){arguments.length!==r-1&&$e(t+" called with "+arguments.length+" arguments, expected "+(r-1)),u.length=0,s.length=r;for(var e=1;e<r;++e)s[e]=n[e].toWireType(u,arguments[e-1]);var a=o.apply(null,s);return Tr(u),n[0].fromWireType(a)},[]})),[]}))},f:function(e,r,t,n,o,a,i,s){var u=kr(t,n);r=ke(r),a=wr(o,a),Ie([],[e],(function(e){var n=(e=e[0]).name+"."+r;function o(){_r("Cannot call "+n+" due to unbound types",u)}s&&e.registeredClass.pureVirtualFunctions.push(r);var c=e.registeredClass.instancePrototype,l=c[r];return void 0===l||void 0===l.overloadTable&&l.className!==e.name&&l.argCount===t-2?(o.argCount=t-2,o.className=e.name,c[r]=o):(Ze(c,r,n),c[r].overloadTable[t-2]=o),Ie([],u,(function(o){var s=function(e,r,t,n,o){var a=r.length;a<2&&$e("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==t,s=!1,u=1;u<r.length;++u)if(null!==r[u]&&void 0===r[u].destructorFunction){s=!0;break}var c="void"!==r[0].name,l="",f="";for(u=0;u<a-2;++u)l+=(0!==u?", ":"")+"arg"+u,f+=(0!==u?", ":"")+"arg"+u+"Wired";var d="return function "+Se(e)+"("+l+") {\nif (arguments.length !== "+(a-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(a-2)+" args!');\n}\n";s&&(d+="var destructors = [];\n");var p=s?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[$e,n,o,Tr,r[0],r[1]];for(i&&(d+="var thisWired = classParam.toWireType("+p+", this);\n"),u=0;u<a-2;++u)d+="var arg"+u+"Wired = argType"+u+".toWireType("+p+", arg"+u+"); // "+r[u+2].name+"\n",h.push("argType"+u),m.push(r[u+2]);if(i&&(f="thisWired"+(f.length>0?", ":"")+f),d+=(c?"var rv = ":"")+"invoker(fn"+(f.length>0?", ":"")+f+");\n",s)d+="runDestructors(destructors);\n";else for(u=i?1:2;u<r.length;++u){var v=1===u?"thisWired":"arg"+(u-2)+"Wired";null!==r[u].destructorFunction&&(d+=v+"_dtor("+v+"); // "+r[u].name+"\n",h.push(v+"_dtor"),m.push(r[u].destructorFunction))}return c&&(d+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),d+="}\n",h.push(d),Pr(Function,h).apply(null,m)}(n,o,e,a,i);return void 0===c[r].overloadTable?(s.argCount=t-2,c[r]=s):c[r].overloadTable[t-2]=s,[]})),[]}))},H:function(e,r,t,n,o,a,i,s,u,c){r=ke(r),o=wr(n,o),Ie([],[e],(function(e){var n=(e=e[0]).name+"."+r,l={get:function(){_r("Cannot access "+n+" due to unbound types",[t,i])},enumerable:!0,configurable:!0};return l.set=u?function(){_r("Cannot access "+n+" due to unbound types",[t,i])}:function(e){$e(n+" is a read-only property")},Object.defineProperty(e.registeredClass.instancePrototype,r,l),Ie([],u?[t,i]:[t],(function(t){var i=t[0],l={get:function(){var r=Dr(this,e,n+" getter");return i.fromWireType(o(a,r))},enumerable:!0};if(u){u=wr(s,u);var f=t[1];l.set=function(r){var t=Dr(this,e,n+" setter"),o=[];u(c,t,f.toWireType(o,r)),Tr(o)}}return Object.defineProperty(e.registeredClass.instancePrototype,r,l),[]})),[]}))},B:function(e,r){je(e,{name:r=ke(r),fromWireType:function(e){var r=Ar[e].value;return Sr(e),r},toWireType:function(e,r){return Rr(r)},argPackAdvance:8,readValueFromPointer:ar,destructorFunction:null})},l:function(e,r,t){var n=be(t);je(e,{name:r=ke(r),fromWireType:function(e){return e},toWireType:function(e,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+$r(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:Mr(r,n),destructorFunction:null})},c:function(e,r,t,n,o){r=ke(r),-1===o&&(o=4294967295);var a=be(t),i=function(e){return e};if(0===n){var s=32-8*t;i=function(e){return e<<s>>>s}}var u=-1!=r.indexOf("unsigned");je(e,{name:r,fromWireType:i,toWireType:function(e,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+$r(t)+'" to '+this.name);if(t<n||t>o)throw new TypeError('Passing a number "'+$r(t)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+n+", "+o+"]!");return u?t>>>0:0|t},argPackAdvance:8,readValueFromPointer:Or(r,a,0!==n),destructorFunction:null})},b:function(e,r,t){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(e){var r=U,t=r[e>>=2],o=r[e+1];return new n(R,o,t)}je(e,{name:t=ke(t),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},m:function(e,r){var t="std::string"===(r=ke(r));je(e,{name:r,fromWireType:function(e){var r,n=U[e>>2];if(t)for(var o=e+4,a=0;a<=n;++a){var i=e+4+a;if(a==n||0==M[i]){var s=A(o,i-o);void 0===r?r=s:(r+=String.fromCharCode(0),r+=s),o=i+1}}else{var u=new Array(n);for(a=0;a<n;++a)u[a]=String.fromCharCode(M[e+4+a]);r=u.join("")}return Zr(e),r},toWireType:function(e,r){r instanceof ArrayBuffer&&(r=new Uint8Array(r));var n="string"==typeof r;n||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||$e("Cannot pass non-string to std::string");var o=(t&&n?function(){return x(r)}:function(){return r.length})(),a=et(4+o+1);if(U[a>>2]=o,t&&n)F(r,a+4,o+1);else if(n)for(var i=0;i<o;++i){var s=r.charCodeAt(i);s>255&&(Zr(a),$e("String has UTF-16 code units that do not fit in 8 bits")),M[a+4+i]=s}else for(i=0;i<o;++i)M[a+4+i]=r[i];return null!==e&&e.push(Zr,a),a},argPackAdvance:8,readValueFromPointer:ar,destructorFunction:function(e){Zr(e)}})},h:function(e,r,t){var n,o,a,i,s;t=ke(t),2===r?(n=z,o=H,i=V,a=function(){return I},s=1):4===r&&(n=X,o=G,i=q,a=function(){return U},s=2),je(e,{name:t,fromWireType:function(e){for(var t,o=U[e>>2],i=a(),u=e+4,c=0;c<=o;++c){var l=e+4+c*r;if(c==o||0==i[l>>s]){var f=n(u,l-u);void 0===t?t=f:(t+=String.fromCharCode(0),t+=f),u=l+r}}return Zr(e),t},toWireType:function(e,n){"string"!=typeof n&&$e("Cannot pass non-string to C++ string type "+t);var a=i(n),u=et(4+a+r);return U[u>>2]=a>>s,o(n,u+4,a+r),null!==e&&e.push(Zr,u),u},argPackAdvance:8,readValueFromPointer:ar,destructorFunction:function(e){Zr(e)}})},D:function(e,r){je(e,{isVoid:!0,name:r=ke(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,r){}})},u:function(e,r,t){e=Ir(e),r=jr(r,"emval::as");var n=[],o=Rr(n);return j[t>>2]=o,r.toWireType(n,e)},e:function(e,r,t,n){var o,a;(e=Nr[e])(r=Ir(r),t=void 0===(a=Ur[o=t])?ke(o):a,null,n)},p:Sr,d:function(e,r){for(var t=function(e,r){for(var t=new Array(e),n=0;n<e;++n)t[n]=jr(j[(r>>2)+n],"parameter "+n);return t}(e,r),n=t[0],o=n.name+"_$"+t.slice(1).map((function(e){return e.name})).join("_")+"$",a=["retType"],i=[n],s="",u=0;u<e-1;++u)s+=(0!==u?", ":"")+"arg"+u,a.push("argType"+u),i.push(t[1+u]);var c="return function "+Se("methodCaller_"+o)+"(handle, name, destructors, args) {\n",l=0;for(u=0;u<e-1;++u)c+="    var arg"+u+" = argType"+u+".readValueFromPointer(args"+(l?"+"+l:"")+");\n",l+=t[u+1].argPackAdvance;for(c+="    var rv = handle[name]("+s+");\n",u=0;u<e-1;++u)t[u+1].deleteObject&&(c+="    argType"+u+".deleteObject(arg"+u+");\n");n.isVoid||(c+="    return retType.toWireType(destructors, rv);\n"),c+="};\n",a.push(c);var f,d,p=Pr(Function,a).apply(null,i);return f=p,d=Nr.length,Nr.push(f),d},o:function(e){e>4&&(Ar[e].refcount+=1)},q:function(e){Tr(Ar[e].value),Sr(e)},A:function(e,r){return Rr((e=jr(e,"_emval_take_value")).readValueFromPointer(r))},a:function(){ne()},F:function e(){return void 0===e.start&&(e.start=Date.now()),1e3*(Date.now()-e.start)|0},G:function(e,r,t){var n=function(e,r){var t=e,n=r;function o(e){var r;return n=function(e,r){return"double"!==r&&"i64"!==r||7&e&&(e+=4),e}(n,e),"double"===e?(r=B[n>>3],n+=8):"i64"==e?(r=[j[n>>2],j[n+4>>2]],n+=8):(e="i32",r=j[n>>2],n+=4),r}for(var a,i,s,u,c=[];;){var l=t;if(0===(a=$[t>>0]))break;if(i=$[t+1>>0],37==a){var f=!1,d=!1,p=!1,h=!1,m=!1;e:for(;;){switch(i){case 43:f=!0;break;case 45:d=!0;break;case 35:p=!0;break;case 48:if(h)break e;h=!0;break;case 32:m=!0;break;default:break e}t++,i=$[t+1>>0]}var v=0;if(42==i)v=o("i32"),t++,i=$[t+1>>0];else for(;i>=48&&i<=57;)v=10*v+(i-48),t++,i=$[t+1>>0];var g,y=!1,w=-1;if(46==i){if(w=0,y=!0,t++,42==(i=$[t+1>>0]))w=o("i32"),t++;else for(;;){var E=$[t+1>>0];if(E<48||E>57)break;w=10*w+(E-48),t++}i=$[t+1>>0]}switch(w<0&&(w=6,y=!1),String.fromCharCode(i)){case"h":104==$[t+2>>0]?(t++,g=1):g=2;break;case"l":108==$[t+2>>0]?(t++,g=8):g=4;break;case"L":case"q":case"j":g=8;break;case"z":case"t":case"I":g=4;break;default:g=null}switch(g&&t++,i=$[t+1>>0],String.fromCharCode(i)){case"d":case"i":case"u":case"o":case"x":case"X":case"p":var b=100==i||105==i;s=o("i"+8*(g=g||4)),8==g&&(s=117==i?(s[0]>>>0)+4294967296*(s[1]>>>0):Br(s[0],s[1])),g<=4&&(s=(b?Wr:Lr)(s&Math.pow(256,g)-1,8*g));var _=Math.abs(s),k="";if(100==i||105==i)D=Wr(s,8*g).toString(10);else if(117==i)D=Lr(s,8*g).toString(10),s=Math.abs(s);else if(111==i)D=(p?"0":"")+_.toString(8);else if(120==i||88==i){if(k=p&&0!=s?"0x":"",s<0){s=-s,D=(_-1).toString(16);for(var T=[],P=0;P<D.length;P++)T.push((15-parseInt(D[P],16)).toString(16));for(D=T.join("");D.length<2*g;)D="f"+D}else D=_.toString(16);88==i&&(k=k.toUpperCase(),D=D.toUpperCase())}else 112==i&&(0===_?D="(nil)":(k="0x",D=_.toString(16)));if(y)for(;D.length<w;)D="0"+D;for(s>=0&&(f?k="+"+k:m&&(k=" "+k)),"-"==D.charAt(0)&&(k="-"+k,D=D.substr(1));k.length+D.length<v;)d?D+=" ":h?D="0"+D:k=" "+k;(D=k+D).split("").forEach((function(e){c.push(e.charCodeAt(0))}));break;case"f":case"F":case"e":case"E":case"g":case"G":var D;if(s=o("double"),isNaN(s))D="nan",h=!1;else if(isFinite(s)){var C=!1,A=Math.min(w,20);if(103==i||71==i){C=!0,w=w||1;var S=parseInt(s.toExponential(A).split("e")[1],10);w>S&&S>=-4?(i=(103==i?"f":"F").charCodeAt(0),w-=S+1):(i=(103==i?"e":"E").charCodeAt(0),w--),A=Math.min(w,20)}101==i||69==i?(D=s.toExponential(A),/[eE][-+]\d$/.test(D)&&(D=D.slice(0,-1)+"0"+D.slice(-1))):102!=i&&70!=i||(D=s.toFixed(A),0===s&&((u=s)<0||0===u&&1/u==-1/0)&&(D="-"+D));var F=D.split("e");if(C&&!p)for(;F[0].length>1&&-1!=F[0].indexOf(".")&&("0"==F[0].slice(-1)||"."==F[0].slice(-1));)F[0]=F[0].slice(0,-1);else for(p&&-1==D.indexOf(".")&&(F[0]+=".");w>A++;)F[0]+="0";D=F[0]+(F.length>1?"e"+F[1]:""),69==i&&(D=D.toUpperCase()),s>=0&&(f?D="+"+D:m&&(D=" "+D))}else D=(s<0?"-":"")+"inf",h=!1;for(;D.length<v;)d?D+=" ":D=!h||"-"!=D[0]&&"+"!=D[0]?(h?"0":" ")+D:D[0]+"0"+D.slice(1);i<97&&(D=D.toUpperCase()),D.split("").forEach((function(e){c.push(e.charCodeAt(0))}));break;case"s":var x=o("i8*"),R=x?tt(x):"(null)".length;if(y&&(R=Math.min(R,w)),!d)for(;R<v--;)c.push(32);if(x)for(P=0;P<R;P++)c.push(M[x++>>0]);else c=c.concat(Yr("(null)".substr(0,R),!0));if(d)for(;R<v--;)c.push(32);break;case"c":for(d&&c.push(o("i8"));--v>0;)c.push(32);d||c.push(o("i8"));break;case"n":var O=o("i32*");j[O>>2]=c.length;break;case"%":c.push(a);break;default:for(P=l;P<t+2;P++)c.push($[P>>0])}t+=2}else c.push(a),t+=1}return c}(r,t);!function(e,r){24&e&&(r=r.replace(/\s+$/,""),r+=(r.length>0?"\n":"")+Hr(e)),1&e?4&e?console.error(r):2&e?console.warn(r):512&e?console.info(r):256&e?console.debug(r):console.log(r):6&e?w(r):y(r)}(e,C(n,0))},s:function(e){M.length,ne("OOM")},t:function(e,r){try{var t=0;return Xr().forEach((function(n,o){var a=r+t;j[e+4*o>>2]=a,function(e,r,t){for(var n=0;n<e.length;++n)$[r++>>0]=e.charCodeAt(n);t||($[r>>0]=0)}(n,a),t+=n.length+1})),0}catch(e){return void 0!==we&&e instanceof we.ErrnoError||ne(e),e.errno}},v:function(e,r){try{var t=Xr();j[e>>2]=t.length;var n=0;return t.forEach((function(e){n+=e.length+1})),j[r>>2]=n,0}catch(e){return void 0!==we&&e instanceof we.ErrnoError||ne(e),e.errno}},k:function(e){try{var r=Ee.getStreamFromFD(e);return we.close(r),0}catch(e){return void 0!==we&&e instanceof we.ErrnoError||ne(e),e.errno}},y:function(e,r){try{var t=Ee.getStreamFromFD(e),n=t.tty?2:we.isDir(t.mode)?3:we.isLink(t.mode)?7:4;return $[r>>0]=n,0}catch(e){return void 0!==we&&e instanceof we.ErrnoError||ne(e),e.errno}},z:function(e,r,t,n){try{var o=Ee.getStreamFromFD(e),a=Ee.doReadv(o,r,t);return j[n>>2]=a,0}catch(e){return void 0!==we&&e instanceof we.ErrnoError||ne(e),e.errno}},r:function(e,r,t,n,o){try{var a=Ee.getStreamFromFD(e),i=4294967296*t+(r>>>0),s=9007199254740992;return i<=-s||i>=s?-61:(we.llseek(a,i,n),le=[a.position>>>0,(ce=a.position,+Math.abs(ce)>=1?ce>0?(0|Math.min(+Math.floor(ce/4294967296),4294967295))>>>0:~~+Math.ceil((ce-+(~~ce>>>0))/4294967296)>>>0:0)],j[o>>2]=le[0],j[o+4>>2]=le[1],a.getdents&&0===i&&0===n&&(a.getdents=null),0)}catch(e){return void 0!==we&&e instanceof we.ErrnoError||ne(e),e.errno}},j:function(e,r,t,n){try{var o=Ee.getStreamFromFD(e),a=Ee.doWritev(o,r,t);return j[n>>2]=a,0}catch(e){return void 0!==we&&e instanceof we.ErrnoError||ne(e),e.errno}},E:function(e){var r=Date.now();return j[e>>2]=r/1e3|0,j[e+4>>2]=r%1e3*1e3|0,0},g:function(e){}};!function(){var e={a:Kr};function r(e,r){var n,o,a=e.exports;t.asm=a,b=t.asm.I,n=b.buffer,R=n,t.HEAP8=$=new Int8Array(n),t.HEAP16=O=new Int16Array(n),t.HEAP32=j=new Int32Array(n),t.HEAPU8=M=new Uint8Array(n),t.HEAPU16=I=new Uint16Array(n),t.HEAPU32=U=new Uint32Array(n),t.HEAPF32=N=new Float32Array(n),t.HEAPF64=B=new Float64Array(n),W=t.asm.M,o=t.asm.J,Y.unshift(o),te()}function n(e){r(e.instance)}function o(r){return function(){if(!E&&(c||l)){if("function"==typeof fetch&&!ue(fe))return fetch(fe,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+fe+"'";return e.arrayBuffer()})).catch((function(){return de(fe)}));if(p)return new Promise((function(e,r){p(fe,(function(r){e(new Uint8Array(r))}),r)}))}return Promise.resolve().then((function(){return de(fe)}))}().then((function(r){return WebAssembly.instantiate(r,e)})).then(r,(function(e){w("failed to asynchronously prepare wasm: "+e),ne(e)}))}if(re(),t.instantiateWasm)try{return t.instantiateWasm(e,r)}catch(e){return w("Module.instantiateWasm callback failed with error: "+e),!1}E||"function"!=typeof WebAssembly.instantiateStreaming||ie(fe)||ue(fe)||"function"!=typeof fetch?o(n):fetch(fe,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,e).then(n,(function(e){return w("wasm streaming compile failed: "+e),w("falling back to ArrayBuffer instantiation"),o(n)}))}))}(),t.___wasm_call_ctors=function(){return(t.___wasm_call_ctors=t.asm.J).apply(null,arguments)};var Qr,Zr=t._free=function(){return(Zr=t._free=t.asm.K).apply(null,arguments)},et=t._malloc=function(){return(et=t._malloc=t.asm.L).apply(null,arguments)},rt=t.___errno_location=function(){return(rt=t.___errno_location=t.asm.N).apply(null,arguments)},tt=t._strlen=function(){return(tt=t._strlen=t.asm.O).apply(null,arguments)},nt=t.___getTypeName=function(){return(nt=t.___getTypeName=t.asm.P).apply(null,arguments)};function ot(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function at(e){function r(){Qr||(Qr=!0,t.calledRun=!0,T||(t.noFSInit||we.init.initialized||we.init(),pe(Y),we.ignorePermissions=!1,pe(K),t.onRuntimeInitialized&&t.onRuntimeInitialized(),function(){if(t.postRun)for("function"==typeof t.postRun&&(t.postRun=[t.postRun]);t.postRun.length;)e=t.postRun.shift(),Q.unshift(e);var e;pe(Q)}()))}Z>0||(!function(){if(t.preRun)for("function"==typeof t.preRun&&(t.preRun=[t.preRun]);t.preRun.length;)e=t.preRun.shift(),J.unshift(e);var e;pe(J)}(),Z>0||(t.setStatus?(t.setStatus("Running..."),setTimeout((function(){setTimeout((function(){t.setStatus("")}),1),r()}),1)):r()))}if(t.___embind_register_native_and_builtin_types=function(){return(t.___embind_register_native_and_builtin_types=t.asm.Q).apply(null,arguments)},t.dynCall_ijiii=function(){return(t.dynCall_ijiii=t.asm.R).apply(null,arguments)},t.dynCall_viiijj=function(){return(t.dynCall_viiijj=t.asm.S).apply(null,arguments)},t.dynCall_jij=function(){return(t.dynCall_jij=t.asm.T).apply(null,arguments)},t.dynCall_jii=function(){return(t.dynCall_jii=t.asm.U).apply(null,arguments)},t.dynCall_jiji=function(){return(t.dynCall_jiji=t.asm.V).apply(null,arguments)},t._ff_h264_cabac_tables=82789,ee=function e(){Qr||at(),Qr||(ee=e)},t.run=at,t.preInit)for("function"==typeof t.preInit&&(t.preInit=[t.preInit]);t.preInit.length>0;)t.preInit.pop()();at(),e.exports=t}));const u=1e3,c=!1,l=!0,f=!1,d=!1,p="initVideo",h="render",m="playAudio",v="initAudio",g="audioCode",y="videoCode",w=1,E=2,b="init",_="decode",k="audioDecode",T="videoDecode",P="close",D="updateConfig",C="key",A="delta";(()=>{try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){const e=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(e instanceof WebAssembly.Module)return new WebAssembly.Instance(e)instanceof WebAssembly.Instance}}catch(e){}})(),Date.now||(Date.now=function(){return(new Date).getTime()}),s.postRun=function(){var e=[],r=[],t={};"VideoEncoder"in self&&(t={hasInit:!1,isEmitInfo:!1,offscreenCanvas:null,offscreenCanvasCtx:null,decoder:new VideoDecoder({output:function(e){t.isEmitInfo||(n.opt.debug&&console.log("Jessibuca: [worker] Webcodecs Video Decoder initSize"),postMessage({cmd:p,w:e.codedWidth,h:e.codedHeight}),t.isEmitInfo=!0,t.offscreenCanvas=new OffscreenCanvas(e.codedWidth,e.codedHeight),t.offscreenCanvasCtx=t.offscreenCanvas.getContext("2d")),t.offscreenCanvasCtx.drawImage(e,0,0,e.codedWidth,e.codedHeight);let r=t.offscreenCanvas.transferToImageBitmap();postMessage({cmd:h,buffer:r,delay:n.delay,ts:0},[r]),setTimeout((function(){e.close?e.close():e.destroy()}),100)},error:function(e){console.error(e)}}),decode:function(e,r){const o=e[0]>>4==1;if(t.hasInit){const n=new EncodedVideoChunk({data:e.slice(5),timestamp:r,type:o?C:A});t.decoder.decode(n)}else if(o&&0===e[1]){const r=15&e[0];n.setVideoCodec(r);const o=function(e){let r=e.subarray(1,4),t="avc1.";for(let e=0;e<3;e++){let n=r[e].toString(16);n.length<2&&(n="0"+n),t+=n}return{codec:t,description:e}}(e.slice(5));t.decoder.configure(o),t.hasInit=!0}},reset(){t.hasInit=!1,t.isEmitInfo=!1,t.offscreenCanvas=null,t.offscreenCanvasCtx=null}});var n={opt:{debug:c,forceNoOffscreen:l,useWCS:f,videoBuffer:u,openWebglAlignment:d},useOffscreen:function(){return!n.opt.forceNoOffscreen&&"undefined"!=typeof OffscreenCanvas},initAudioPlanar:function(e,t){postMessage({cmd:v,sampleRate:t,channels:e});var n=[],o=0;this.playAudioPlanar=function(t,a,i){for(var u=a,c=[],l=0,f=0;f<2;f++){var d=s.HEAPU32[(t>>2)+f]>>2;c[f]=s.HEAPF32.subarray(d,d+u)}if(o){if(!(u>=(a=1024-o)))return o+=u,r[0]=Float32Array.of(...r[0],...c[0]),void(2==e&&(r[1]=Float32Array.of(...r[1],...c[1])));n[0]=Float32Array.of(...r[0],...c[0].subarray(0,a)),2==e&&(n[1]=Float32Array.of(...r[1],...c[1].subarray(0,a))),postMessage({cmd:m,buffer:n,ts:i},n.map((e=>e.buffer))),l=a,u-=a}for(o=u;o>=1024;o-=1024)n[0]=c[0].slice(l,l+=1024),2==e&&(n[1]=c[1].slice(l-1024,l)),postMessage({cmd:m,buffer:n,ts:i},n.map((e=>e.buffer)));o&&(r[0]=c[0].slice(l),2==e&&(r[1]=c[1].slice(l)))}},setVideoCodec:function(e){postMessage({cmd:y,code:e})},setAudioCodec:function(e){postMessage({cmd:g,code:e})},setVideoSize:function(e,r){postMessage({cmd:p,w:e,h:r});var t=e*r,o=t>>2;n.useOffscreen()?(this.offscreenCanvas=new OffscreenCanvas(e,r),this.offscreenCanvasGL=this.offscreenCanvas.getContext("webgl"),this.webglObj=((e,r)=>{var t=["attribute vec4 vertexPos;","attribute vec4 texturePos;","varying vec2 textureCoord;","void main()","{","gl_Position = vertexPos;","textureCoord = texturePos.xy;","}"].join("\n"),n=["precision highp float;","varying highp vec2 textureCoord;","uniform sampler2D ySampler;","uniform sampler2D uSampler;","uniform sampler2D vSampler;","const mat4 YUV2RGB = mat4","(","1.1643828125, 0, 1.59602734375, -.87078515625,","1.1643828125, -.39176171875, -.81296875, .52959375,","1.1643828125, 2.017234375, 0, -1.081390625,","0, 0, 0, 1",");","void main(void) {","highp float y = texture2D(ySampler,  textureCoord).r;","highp float u = texture2D(uSampler,  textureCoord).r;","highp float v = texture2D(vSampler,  textureCoord).r;","gl_FragColor = vec4(y, u, v, 1) * YUV2RGB;","}"].join("\n");r&&e.pixelStorei(e.UNPACK_ALIGNMENT,1);var o=e.createShader(e.VERTEX_SHADER);e.shaderSource(o,t),e.compileShader(o),e.getShaderParameter(o,e.COMPILE_STATUS)||console.log("Vertex shader failed to compile: "+e.getShaderInfoLog(o));var a=e.createShader(e.FRAGMENT_SHADER);e.shaderSource(a,n),e.compileShader(a),e.getShaderParameter(a,e.COMPILE_STATUS)||console.log("Fragment shader failed to compile: "+e.getShaderInfoLog(a));var i=e.createProgram();e.attachShader(i,o),e.attachShader(i,a),e.linkProgram(i),e.getProgramParameter(i,e.LINK_STATUS)||console.log("Program failed to compile: "+e.getProgramInfoLog(i)),e.useProgram(i);var s=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,s),e.bufferData(e.ARRAY_BUFFER,new Float32Array([1,1,-1,1,1,-1,-1,-1]),e.STATIC_DRAW);var u=e.getAttribLocation(i,"vertexPos");e.enableVertexAttribArray(u),e.vertexAttribPointer(u,2,e.FLOAT,!1,0,0);var c=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,c),e.bufferData(e.ARRAY_BUFFER,new Float32Array([1,0,0,0,1,1,0,1]),e.STATIC_DRAW);var l=e.getAttribLocation(i,"texturePos");function f(r,t){var n=e.createTexture();return e.bindTexture(e.TEXTURE_2D,n),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.bindTexture(e.TEXTURE_2D,null),e.uniform1i(e.getUniformLocation(i,r),t),n}e.enableVertexAttribArray(l),e.vertexAttribPointer(l,2,e.FLOAT,!1,0,0);var d=f("ySampler",0),p=f("uSampler",1),h=f("vSampler",2);return{render:function(r,t,n,o,a){e.viewport(0,0,r,t),e.activeTexture(e.TEXTURE0),e.bindTexture(e.TEXTURE_2D,d),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,r,t,0,e.LUMINANCE,e.UNSIGNED_BYTE,n),e.activeTexture(e.TEXTURE1),e.bindTexture(e.TEXTURE_2D,p),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,r/2,t/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,o),e.activeTexture(e.TEXTURE2),e.bindTexture(e.TEXTURE_2D,h),e.texImage2D(e.TEXTURE_2D,0,e.LUMINANCE,r/2,t/2,0,e.LUMINANCE,e.UNSIGNED_BYTE,a),e.drawArrays(e.TRIANGLE_STRIP,0,4)},destroy:function(){try{e.deleteProgram(i),e.deleteBuffer(s),e.deleteBuffer(c),e.deleteTexture(d),e.deleteTexture(p),e.deleteBuffer(h)}catch(e){}}}})(this.offscreenCanvasGL,n.opt.openWebglAlignment),this.draw=function(n,a,i,u){this.webglObj.render(e,r,s.HEAPU8.subarray(a,a+t),s.HEAPU8.subarray(i,i+o),s.HEAPU8.subarray(u,u+o));let c=this.offscreenCanvas.transferToImageBitmap();postMessage({cmd:h,buffer:c,delay:this.delay,ts:n},[c])}):this.draw=function(e,r,n,a){var i=[s.HEAPU8.subarray(r,r+t),s.HEAPU8.subarray(n,n+o),s.HEAPU8.subarray(a,a+o)].map((e=>Uint8Array.from(e)));postMessage({cmd:h,output:i,delay:this.delay,ts:e},i.map((e=>e.buffer)))}},getDelay:function(e){return e?(this.firstTimestamp?e&&(this.delay=Date.now()-this.startTimestamp-(e-this.firstTimestamp)):(this.firstTimestamp=e,this.startTimestamp=Date.now(),this.delay=-1),this.delay):-1},resetDelay:function(){this.firstTimestamp=null,this.startTimestamp=null,this.delay=-1},init:function(){n.opt.debug&&console.log("Jessibuca: [worker] init");const r=e=>{n.opt.useWCS&&n.useOffscreen()&&e.type===E&&t.decode?t.decode(e.payload,e.ts):e.decoder.decode(e.payload,e.ts)};this.stopId=setInterval((()=>{if(e.length)if(this.dropping){for((t=e.shift()).type===w&&0===t.payload[1]&&r(t);!t.isIFrame&&e.length;)(t=e.shift()).type===w&&0===t.payload[1]&&r(t);t.isIFrame&&(this.dropping=!1,r(t))}else{var t=e[0];if(-1===this.getDelay(t.ts))n.opt.debug&&console.log("Jessibuca: [worker]: common dumex delay is -1"),e.shift(),r(t);else if(this.delay>n.opt.videoBuffer+1e3)n.opt.debug&&console.log("Jessibuca: [worker]:",`delay is ${this.delay}, set dropping is true`),this.resetDelay(),this.dropping=!0;else for(;e.length&&(t=e[0],this.getDelay(t.ts)>n.opt.videoBuffer);)e.shift(),r(t)}}),10)},close:function(){n.opt.debug&&console.log("Jessibuca: [worker]: close"),clearInterval(this.stopId),this.stopId=null,o.clear(),a.clear(),t.reset&&t.reset(),this.firstTimestamp=null,this.startTimestamp=null,this.delay=-1,this.dropping=!1,this.webglObj&&(this.webglObj.destroy(),this.offscreenCanvas=null,this.offscreenCanvasGL=null,this.offscreenCanvasCtx=null),e=[],r=[],delete this.playAudioPlanar,delete this.draw},pushBuffer:function(r,t){t.type===w?e.push({ts:t.ts,payload:r,decoder:o,type:w}):t.type===E&&e.push({ts:t.ts,payload:r,decoder:a,type:E,isIFrame:t.isIFrame})}},o=new s.AudioDecoder(n),a=new s.VideoDecoder(n);postMessage({cmd:b}),self.onmessage=function(e){var r=e.data;switch(r.cmd){case b:try{n.opt=Object.assign(n.opt,JSON.parse(r.opt))}catch(e){}o.sample_rate=r.sampleRate,n.init();break;case _:n.pushBuffer(r.buffer,r.options);break;case k:o.decode(r.buffer,r.ts);break;case T:a.decode(r.buffer,r.ts);break;case P:n.close();break;case D:n.opt[r.key]=r.value}}}}));
