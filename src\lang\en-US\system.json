{"system.menu.034890-0": "<PERSON>u Name", "system.menu.034890-1": "Please enter the menu name", "system.menu.034890-2": "Menu status", "system.menu.034890-3": "icon", "system.menu.034890-4": "sort", "system.menu.034890-5": "Permission identification", "system.menu.034890-6": "Component path", "system.menu.034890-7": "Superior menu", "system.menu.034890-8": "Select superior menu", "system.menu.034890-9": "Menu Type", "system.menu.034890-10": "catalogue", "system.menu.034890-11": "menu", "system.menu.034890-12": "button", "system.menu.034890-13": "Menu icons", "system.menu.034890-14": "Click on the selection icon", "system.menu.034890-15": "Display sorting", "system.menu.034890-16": "If selecting an external link, the routing address needs to start with 'http (s)://'", "system.menu.034890-17": "Is it an external link", "system.menu.034890-18": "The routing address to be accessed, such as' user '. If an external network address requires internal chain access, it starts with' http (s)://'", "system.menu.034890-19": "Routing address", "system.menu.034890-20": "Please enter the routing address", "system.menu.034890-21": "The path to the accessed component, such as' system/user/index ', defaults to the' views' directory", "system.menu.034890-22": "component path", "system.menu.034890-23": "Please enter the component path", "system.menu.034890-24": "Please enter the permission identifier", "system.menu.034890-25": "The permission characters defined in the controller, such as @ PreAuthorize (` @ ss. hasPermi ('system: user: list ') `)", "system.menu.034890-26": "Permission Characters", "system.menu.034890-27": "Please enter routing parameters", "system.menu.034890-28": "The default pass parameters for accessing the route, such as: {\"id\": 1, \"name\": \"ry\"}", "system.menu.034890-29": "Routing parameters", "system.menu.034890-30": "If you choose yes, it will be cached by 'keep alive', and the matching component's' name 'and address need to be consistent", "system.menu.034890-31": "Is it cached", "system.menu.034890-32": "cache", "system.menu.034890-33": "Do not cache", "system.menu.034890-34": "If you choose to hide, the route will not appear in the sidebar, but it can still be accessed", "system.menu.034890-35": "Display status", "system.menu.034890-36": "If you choose to disable, the route will not appear in the sidebar and cannot be accessed", "system.menu.034890-37": "The menu name cannot be empty", "system.menu.034890-38": "The menu order cannot be empty", "system.menu.034890-39": "The routing address cannot be empty", "system.menu.034890-40": "Main categories", "system.menu.034890-41": "add menu", "system.menu.034890-42": "Modify menu", "system.menu.034890-43": "Are you sure to delete the data item with the name {0}?", "system.dept.780956-0": "Institution name", "system.dept.780956-1": "Please enter the institution name", "system.dept.780956-2": "Institutional status", "system.dept.780956-3": "Institution type", "system.dept.780956-4": "contacts", "system.dept.780956-5": "New institutions added", "system.dept.780956-6": "Editing organization", "system.dept.780956-7": "More management", "system.dept.780956-8": "user management ", "system.dept.780956-9": "Role management", "system.dept.780956-10": "Superior institutions", "system.dept.780956-11": "Select superior organization", "system.dept.780956-12": "Please select the type of institution", "system.dept.780956-13": "Please enter the contact person", "system.dept.780956-14": "system account ", "system.dept.780956-15": "Login account", "system.dept.780956-16": "Please create an account name for this organization", "system.dept.780956-17": "Please enter your login account", "system.dept.780956-18": "password", "system.dept.780956-19": "Please enter password", "system.dept.780956-20": "Confirm password", "system.dept.780956-21": "Contact phone number", "system.dept.780956-22": "Please enter the contact phone number", "system.dept.780956-23": "The passwords entered twice are inconsistent", "system.dept.780956-24": "Superior organization cannot be empty", "system.dept.780956-25": "Institution name cannot be empty", "system.dept.780956-26": "Institution type cannot be empty", "system.dept.780956-27": "Contact person cannot be empty", "system.dept.780956-28": "Login account cannot be empty", "system.dept.780956-29": "Password cannot be empty", "system.dept.780956-30": "Passwords must consist of two or more types of uppercase, lowercase letters, numbers, and special characters", "system.dept.780956-31": "Please enter your password again", "system.dept.780956-32": "Contact number cannot be empty", "system.dept.780956-33": "Add Institution", "system.dept.780956-34": "Modify organization", "system.post.236590-0": "Job code", "system.post.236590-1": "Please enter the job code", "system.post.236590-2": "Job title", "system.post.236590-3": "Please enter the position name", "system.post.236590-4": "Position status", "system.post.236590-5": "Job ID", "system.post.236590-6": "Job ranking", "system.post.236590-7": "The position name cannot be empty", "system.post.236590-8": "The position code cannot be empty", "system.post.236590-9": "The position sequence cannot be empty", "system.post.236590-10": "Add position", "system.post.236590-11": "Modify position", "system.post.236590-12": "Are you sure to delete the data item with position number {0}?", "system.dict.data.879098-0": "Dictionary Name", "system.dict.data.879098-1": "Dictionary labels", "system.dict.data.879098-2": "Please enter the dictionary label", "system.dict.data.879098-3": "Data status", "system.dict.data.879098-4": "Dictionary encoding", "system.dict.data.879098-5": "Dictionary labels", "system.dict.data.879098-6": "Dictionary key values", "system.dict.data.879098-7": "Dictionary sorting", "system.dict.data.879098-8": "Dictionary type", "system.dict.data.879098-9": "Data labels", "system.dict.data.879098-10": "Please enter the data label", "system.dict.data.879098-11": "Data key value", "system.dict.data.879098-12": "Please enter the data key value", "system.dict.data.879098-13": "Style Properties", "system.dict.data.879098-14": "Please enter style attributes", "system.dict.data.879098-15": "Display sorting", "system.dict.data.879098-16": "Echo Style", "system.dict.data.879098-17": "default", "system.dict.data.879098-18": "main", "system.dict.data.879098-19": "success", "system.dict.data.879098-20": "information", "system.dict.data.879098-21": "warning", "system.dict.data.879098-22": "danger", "system.dict.data.879098-23": "The data label cannot be empty", "system.dict.data.879098-24": "The data key value cannot be empty", "system.dict.data.879098-25": "The data order cannot be empty", "system.dict.data.879098-26": "Add Dictionary Data", "system.dict.data.879098-27": "Modifying Dictionary Data", "system.dict.data.879098-28": "Are you sure to delete the data item with dictionary code {0}?", "system.dict.index.880996-0": "Please enter the dictionary name", "system.dict.index.880996-1": "Please enter the dictionary type", "system.dict.index.880996-2": "Dictionary status", "system.dict.index.880996-3": "Start date", "system.dict.index.880996-4": "End date", "system.dict.index.880996-5": "Refresh cache", "system.dict.index.880996-6": "Dictionary number", "system.dict.index.880996-7": "Dictionary name cannot be empty", "system.dict.index.880996-8": "Dictionary type cannot be empty", "system.dict.index.880996-9": "Add Dictionary Type", "system.dict.index.880996-10": "Modify Dictionary Type", "system.dict.index.880996-11": "Are you sure to delete the data item with dictionary number {0}?", "system.dict.index.880996-12": "Refresh successful", "system.config.898564-0": "Parameter Name", "system.config.898564-1": "Please enter the parameter name", "system.config.898564-2": "Parameter key name", "system.config.898564-3": "Please enter the parameter key name", "system.config.898564-4": "Built in system", "system.config.898564-5": "Parameter primary key", "system.config.898564-6": "Parameter key values", "system.config.898564-7": "Please enter parameter key values", "system.config.898564-8": "The parameter name cannot be empty", "system.config.898564-9": "The parameter key name cannot be empty", "system.config.898564-10": "The parameter key value cannot be empty", "system.config.898564-11": "Add parameters", "system.config.898564-12": "modify parameters", "system.config.898564-13": "Are you sure to delete the data item with parameter number {0}?", "system.notice.670989-0": "Announcement Title", "system.notice.670989-1": "Please enter the announcement title", "system.notice.670989-2": "Operators", "system.notice.670989-3": "Please enter the operator", "system.notice.670989-4": "type", "system.notice.670989-5": "Announcement type", "system.notice.670989-6": "Please select the announcement type", "system.notice.670989-7": "content", "system.notice.670989-8": "creator", "system.notice.670989-9": "The announcement title cannot be empty", "system.notice.670989-10": "Announcement type cannot be empty", "system.notice.670989-11": "Add Announcement", "system.notice.670989-12": "Modify announcement", "system.notice.670989-13": "Are you sure to delete the data item with announcement number {0}?", "operlog.874509-0": "System modules", "operlog.874509-1": "Please enter the system module", "operlog.874509-2": "Operators", "operlog.874509-3": "Please enter the operator", "operlog.874509-4": "Operation type", "operlog.874509-5": "Operation status", "operlog.874509-6": "Operation time", "operlog.874509-7": "empty", "operlog.874509-8": "Log number", "operlog.874509-9": "Request method", "operlog.874509-10": "Operation address", "operlog.874509-11": "Operation location", "operlog.874509-12": "Operation date", "operlog.874509-13": "detailed", "operlog.874509-14": "Detailed operation logs", "operlog.874509-15": "Operation module:", "operlog.874509-16": "Login information:", "operlog.874509-17": "Request address:", "operlog.874509-18": "Request method:", "operlog.874509-19": "Operation method:", "operlog.874509-20": "Request parameters:", "operlog.874509-21": "Return parameters:", "operlog.874509-22": "Operation status:", "operlog.874509-23": "normal", "operlog.874509-24": "fail", "operlog.874509-25": "Operation time:", "operlog.874509-26": "Abnormal information:", "operlog.874509-27": "Are you sure to delete the data item with log number {0}?", "operlog.874509-28": "Are you sure to clear all operation log data items?", "operlog.874509-29": "Clear successfully", "online.093480-0": "Login address", "online.093480-1": "Please enter your login address", "online.093480-2": "Please enter the user name", "online.093480-3": "Number", "online.093480-4": "Session Number", "online.093480-5": "logon name", "online.093480-6": "Department Name", "online.093480-7": "main engine", "online.093480-8": "Login location", "online.093480-9": "browser", "online.093480-10": "operating system", "online.093480-11": "login time", "online.093480-12": "Forceful retreat", "online.093480-13": "Are you sure you want to forcibly reject the user with the name {0}?", "online.093480-14": "Forced withdrawal successful", "system.logininfor.890875-0": "Login status", "system.logininfor.890875-1": "Unlock", "system.logininfor.890875-2": "Access number", "system.logininfor.890875-3": "Operation information", "system.logininfor.890875-4": "Are you sure to delete the data item with access number {0}?", "system.logininfor.890875-5": "Are you sure to clear all login log data items?", "system.logininfor.890875-6": "Are you sure to unlock the data item for user {0}?", "system.logininfor.890875-7": "\"Unlocking successful", "system.logininfor.890875-8": "user\"", "system.news.893410-0": "title", "system.news.893410-1": "Please enter a title", "system.news.893410-2": "classification", "system.news.893410-3": "Please enter the category name", "system.news.893410-4": "Topping", "system.news.893410-5": "Whether to place at the top", "system.news.893410-6": "Carousel", "system.news.893410-7": "Whether to rotate", "system.news.893410-8": "release", "system.news.893410-9": "Select Status", "system.news.893410-10": "picture", "system.news.893410-11": "author", "system.news.893410-12": "Please enter the author", "system.news.893410-13": "abstract", "system.news.893410-14": "Please select a category", "system.news.893410-15": "content", "system.news.893410-16": "Title cannot be empty", "system.news.893410-17": "Content cannot be empty", "system.news.893410-18": "Classification cannot be empty", "system.news.893410-19": "Author cannot be empty", "system.news.893410-20": "There is currently no content available", "system.news.893410-21": "Add news and information", "system.news.893410-22": "Modifying News Information", "system.news.893410-23": "Please upload pictures", "system.news.893410-24": "Are you sure to delete the data item with news information number {0}?", "system.news.893410-25": "details", "system.newsCategory.874509-0": "Classification name", "system.newsCategory.874509-1": "Classification number", "system.newsCategory.874509-2": "Display order", "system.newsCategory.874509-3": "Please enter the display order", "system.newsCategory.874509-4": "Category name cannot be empty", "system.newsCategory.874509-5": "Display order cannot be empty", "system.newsCategory.874509-6": "Add news category", "system.newsCategory.874509-7": "Modify news classification", "system.newsCategory.874509-8": "Are you sure to delete the data item with news category number {0}?", "system.platform.675309-0": "Third party platforms", "system.platform.675309-1": "Please select a platform", "system.platform.675309-2": "Please select a status", "system.platform.675309-3": "Platform Name", "system.platform.675309-4": "Platform application ID", "system.platform.675309-5": "Jump address", "system.platform.675309-6": "Bind login uri", "system.platform.675309-7": "Jump login uri", "system.platform.675309-8": "Error prompt uri", "system.platform.675309-9": "Third party platform name", "system.platform.675309-10": "Please select a third-party platform", "system.platform.675309-11": "Third party platform status", "system.platform.675309-12": "Third party platform application ID", "system.platform.675309-13": "Please enter the third-party platform application ID", "system.platform.675309-14": "Third party platform key", "system.platform.675309-15": "Please enter the third-party platform key", "system.platform.675309-16": "User authentication redirect address", "system.platform.675309-17": "Please enter the redirect address after user authentication", "system.platform.675309-18": "Bind registration login URI", "system.platform.675309-19": "Please enter the binding registration login uri, http://localhost/login?bindId=", "system.platform.675309-20": "Jump login URI", "system.platform.675309-21": "Please enter the redirect login uri, http://localhost/login?loginId=", "system.platform.675309-22": "Error prompt URI", "system.platform.675309-23": "Please enter the error prompt uri, http://localhost/login?errorId=", "system.platform.675309-24": "Bind login uri, http://localhost/login?bindId= Replace the domain name with the corresponding domain name, local development does not need to change it", "system.platform.675309-25": "Jump to login uri, http://localhost/login?loginId= Replace the domain name with the corresponding domain name, local development does not need to change it", "system.platform.675309-26": "Error prompt for obtaining uri, http://localhost/login?errorId= Replace the domain name with the corresponding domain name, local development does not need to change it", "system.platform.675309-27": "Third party platforms cannot be empty", "system.platform.675309-28": "0: enable, 1: disable cannot be empty", "system.platform.675309-29": "The third-party platform application ID cannot be empty", "system.platform.675309-30": "The third-party platform key cannot be empty", "system.platform.675309-31": "The redirect address after user authentication cannot be empty", "system.platform.675309-32": "Bind registration login uri, http://localhost/login?bindId= Cannot be empty", "system.platform.675309-33": "Jump to login uri, http://localhost/login?loginId= Cannot be empty", "system.platform.675309-34": "Error prompt uri, http://localhost/login?errorId= Cannot be empty", "system.platform.675309-35": "Add third-party login platform control", "system.platform.675309-36": "Modify third-party login platform controls", "system.platform.675309-37": "Are you sure to delete the data item with third-party login platform control number {0}?", "system.job.356378-0": "Task Name", "system.job.356378-1": "Please enter the task name", "system.job.356378-2": "Task group name", "system.job.356378-3": "Please select a task group name", "system.job.356378-4": "Task status", "system.job.356378-5": "Please select task status", "system.job.356378-6": "journal", "system.job.356378-7": "Task number", "system.job.356378-8": "Call target string", "system.job.356378-9": "Cron execution expression", "system.job.356378-10": "Execute once", "system.job.356378-11": "Task details", "system.job.356378-12": "Scheduling logs", "system.job.356378-13": "Task grouping", "system.job.356378-14": "Please select a task group", "system.job.356378-15": "Calling methods", "system.job.356378-16": "Bean call example: ryTask. ryParams ('ry ')", "system.job.356378-17": "Class class call example: com. ruoyi. quartz. task RyTask. ryParams ('ry ')", "system.job.356378-18": "Parameter Description: Supports strings, Boolean types, long integers, floating-point types, and integers", "system.job.356378-19": "cron expressions ", "system.job.356378-20": "Please enter the cron execution expression", "system.job.356378-21": "Generate expression", "system.job.356378-22": "Execution strategy", "system.job.356378-23": "Execute immediately", "system.job.356378-24": "Abandon execution", "system.job.356378-25": "Is it concurrent", "system.job.356378-26": "allow", "system.job.356378-27": "prohibit", "system.job.356378-28": "Cron expression generator", "system.job.356378-29": "Task number:", "system.job.356378-30": "Task Name:", "system.job.356378-31": "Task grouping:", "system.job.356378-32": "Creation time:", "system.job.356378-33": "Cron expression:", "system.job.356378-34": "Next execution time:", "system.job.356378-35": "Call target method:", "system.job.356378-36": "Task status:", "system.job.356378-37": "normal", "system.job.356378-38": "fail", "system.job.356378-39": "Execution strategy:", "system.job.356378-40": "Default Policy", "system.job.356378-41": "Task name cannot be empty", "system.job.356378-42": "The target string cannot be empty when calling", "system.job.356378-43": "The cron execution expression cannot be empty", "system.job.356378-44": "Confirm to", "system.job.356378-45": "task?", "system.job.356378-46": "Confirm to execute immediately once", "system.job.356378-47": "Execution successful", "system.job.356378-48": "Add task", "system.job.356378-49": "Modify task", "system.job.356378-50": "Are you sure to delete the data item with scheduled task number {0}?", "system.job.log.085689-0": "Please enter the task name", "system.job.log.085689-1": "Task group name", "system.job.log.085689-2": "Please select a task group name", "system.job.log.085689-3": "Execution status", "system.job.log.085689-4": "Please select the execution status", "system.job.log.085689-5": "execution time", "system.job.log.085689-6": "empty", "system.job.log.085689-7": "Log number", "system.job.log.085689-8": "Call target string", "system.job.log.085689-9": "log information", "system.job.log.085689-10": "Execution status", "system.job.log.085689-11": "execution time", "system.job.log.085689-12": "detailed", "system.job.log.085689-13": "Detailed scheduling logs", "system.job.log.085689-14": "Log number:", "system.job.log.085689-15": "Task Name:", "system.job.log.085689-16": "Task grouping:", "system.job.log.085689-17": "Execution time:", "system.job.log.085689-18": "Call method:", "system.job.log.085689-19": "Log information:", "system.job.log.085689-20": "Execution status:", "system.job.log.085689-21": "Abnormal information:", "system.job.log.085689-22": "Are you sure to delete the data item with scheduling log number {0}?", "system.job.log.085689-23": "Are you sure to clear all scheduling log data items?", "system.job.log.085689-24": "Clear successfully", "system.server.890786-0": "attribute", "system.server.890786-1": "value", "system.server.890786-2": "Number of cores", "system.server.890786-3": "User usage rate", "system.server.890786-4": "System utilization rate", "system.server.890786-5": "Current idle rate", "system.server.890786-6": "Memory", "system.server.890786-7": "Total memory", "system.server.890786-8": "Used memory", "system.server.890786-9": "Remaining memory", "system.server.890786-10": "Usage rate", "system.server.890786-11": "server information", "system.server.890786-12": "Server Name", "system.server.890786-13": "operating system", "system.server.890786-14": "Server IP", "system.server.890786-15": "system architecture ", "system.server.890786-16": "Java Virtual Machine Information", "system.server.890786-17": "Java name", "system.server.890786-18": "Java version", "system.server.890786-19": "Start time", "system.server.890786-20": "Run time", "system.server.890786-21": "Installation path", "system.server.890786-22": "Project Path", "system.server.890786-23": "Operating parameters", "system.server.890786-24": "Disk status", "system.server.890786-25": "Drive letter path", "system.server.890786-26": "file system", "system.server.890786-27": "Drive letter type", "system.server.890786-28": "Total size", "system.server.890786-29": "Available sizes", "system.server.890786-30": "Used size", "system.server.890786-31": "Used percentage", "system.server.890786-32": "Loading service monitoring data, please wait!", "system.cache.232015-0": "Basic information", "system.cache.232015-1": "Redis version", "system.cache.232015-2": "Operation mode", "system.cache.232015-3": "port", "system.cache.232015-4": "Number of clients", "system.cache.232015-5": "Running time (days)", "system.cache.232015-6": "Using memory", "system.cache.232015-7": "Using CPU", "system.cache.232015-8": "Memory configuration", "system.cache.232015-9": "Is AOF enabled", "system.cache.232015-10": "Is RDB successful", "system.cache.232015-11": "Number of keys", "system.cache.232015-12": "Network entry/exit", "system.cache.232015-13": "Command statistics", "system.cache.232015-14": "Memory information", "system.cache.232015-15": "command", "system.cache.232015-16": "peak value", "system.cache.232015-17": "Memory consumption", "system.cache.list.093478-0": "<PERSON><PERSON>", "system.cache.list.093478-1": "Number", "system.cache.list.093478-2": "<PERSON><PERSON>", "system.cache.list.093478-3": "Key Name List", "system.cache.list.093478-4": "Cache key names", "system.cache.list.093478-5": "Caching content", "system.cache.list.093478-6": "Clean All", "system.cache.list.093478-7": "Cache Name:", "system.cache.list.093478-8": "Cache key name:", "system.cache.list.093478-9": "Cache content:", "system.cache.list.093478-10": "Successfully refreshed cache list", "system.cache.list.093478-11": "Successfully cleared cache name {0}", "system.cache.list.093478-12": "Successfully refreshed key name list", "system.cache.list.093478-13": "Successfully cleared cache key name {0}", "system.cache.list.093478-14": "Successfully cleared all caches", "system.oss.index.987541-0": "File Extension", "system.oss.index.987541-1": "Please enter file extension", "system.oss.index.987541-2": "File Name", "system.oss.index.987541-3": "Please enter file name", "system.oss.index.987541-4": "Provider", "system.oss.index.987541-5": "Please enter provider", "system.oss.index.987541-6": "Search", "system.oss.index.987541-7": "Reset", "system.oss.index.987541-8": "Upload File", "system.oss.index.987541-9": "Delete", "system.oss.index.987541-10": "Config Management", "system.oss.index.987541-11": "File Name", "system.oss.index.987541-12": "Original Name", "system.oss.index.987541-13": "File Suffix", "system.oss.index.987541-14": "URL Address", "system.oss.index.987541-15": "Provider", "system.oss.index.987541-16": "Action", "system.oss.index.987541-17": "Download", "system.oss.index.987541-18": "Delete", "system.oss.index.987541-19": "Confirm", "system.oss.index.987541-20": "Cancel", "system.oss.index.987541-21": "File cannot be empty", "system.oss.index.987541-22": "Add file record", "system.oss.index.987541-23": "Edit file record", "system.oss.index.987541-24": "Deletion successful", "system.oss.index.987541-25": "Are you sure you want to delete the data item with the file record number {0}?", "system.sysclient.652154-0": "Client Key", "system.sysclient.652154-1": "Please enter client key", "system.sysclient.652154-2": "Search", "system.sysclient.652154-3": "Reset", "system.sysclient.652154-4": "Add New", "system.sysclient.652154-5": "Edit", "system.sysclient.652154-6": "Delete", "system.sysclient.652154-7": "Export", "system.sysclient.652154-8": "Client Key", "system.sysclient.652154-9": "Client Secret", "system.sysclient.652154-10": "Client <PERSON>", "system.sysclient.652154-11": "Authorization Type", "system.sysclient.652154-12": "Device Type", "system.sysclient.652154-13": "Token Fixed Timeout", "system.sysclient.652154-14": "Is it effective", "system.sysclient.652154-15": "Action", "system.sysclient.652154-16": "Client Key", "system.sysclient.652154-17": "Please enter client key", "system.sysclient.652154-18": "Client Secret", "system.sysclient.652154-19": "Please enter client secret", "system.sysclient.652154-20": "Fixed Timeout", "system.sysclient.652154-21": "Seconds", "system.sysclient.652154-22": "Specify a time that will definitely expire (unit: seconds), default is seven days (604800 seconds)", "system.sysclient.652154-23": "Is it effective", "system.sysclient.652154-24": "Confirm", "system.sysclient.652154-25": "Cancel", "system.sysclient.652154-26": "Add system authorization", "system.sysclient.652154-27": "Update system authorization", "system.sysclient.652154-28": "Update successful", "system.sysclient.652154-29": "Addition successful", "system.sysclient.652154-30": "Deletion successful", "system.sysclient.652154-31": "Are you sure you want to delete the data item with the system authorization number {0}?", "system.oss.config.185269-0": "Add", "system.oss.config.185269-1": "Update", "system.oss.config.185269-2": "Delete", "system.oss.config.185269-3": "Configure Key", "system.oss.config.185269-4": "Access Site", "system.oss.config.185269-5": "Custom Domain Name", "system.oss.config.185269-6": "Bucket Name", "system.oss.config.185269-7": "Prefix", "system.oss.config.185269-8": "Domain", "system.oss.config.185269-9": "Bucket Permission Type", "system.oss.config.185269-10": "<PERSON>", "system.oss.config.185269-11": "Action", "system.oss.config.185269-12": "Configure Key", "system.oss.config.185269-13": "Please Enter Configuration Key", "system.oss.config.185269-14": "Access Site", "system.oss.config.185269-15": "Please Enter Access Site", "system.oss.config.185269-16": "Custom Domain Name", "system.oss.config.185269-17": "Please Enter Custom Domain Name", "system.oss.config.185269-18": "Please Enter Access Key", "system.oss.config.185269-19": "Please Enter Secret Key", "system.oss.config.185269-20": "Bucket Name", "system.oss.config.185269-21": "Please Enter Bucket Name", "system.oss.config.185269-22": "Is HTTPS", "system.oss.config.185269-23": "Bucket Permission Type", "system.oss.config.185269-24": "Prefix", "system.oss.config.185269-25": "Please Enter Prefix", "system.oss.config.185269-26": "Domain", "system.oss.config.185269-27": "Please Enter Domain", "system.oss.config.185269-28": "Confirm", "system.oss.config.185269-29": "Cancel", "system.oss.config.185269-30": "Configuration Key Cannot Be Empty", "system.oss.config.185269-31": "Access Key Cannot Be Empty", "system.oss.config.185269-32": "Secret Key Cannot Be Empty", "system.oss.config.185269-33": "Bucket Name Cannot Be Empty", "system.oss.config.185269-34": "Access Site Cannot Be Empty", "system.oss.config.185269-35": "Is HTTPS Cannot Be Empty", "system.oss.config.185269-36": "Bucket Permission Type (0=private 1=public 2=custom) Cannot Be Empty", "system.oss.config.185269-37": "Add File Storage Configuration", "system.oss.config.185269-38": "Modify File Storage Configuration", "system.oss.config.185269-39": "Modification Successful", "system.oss.config.185269-40": "Addition Successful", "system.oss.config.185269-41": "Deletion Successful", "system.oss.config.185269-42": "Please Ensure There Is At Least One Default Configuration!", "system.oss.config.185269-43": "Default Configuration Switched Successfully", "system.oss.config.185269-44": "Are you sure you want to delete the data item with the file storage configuration number {0}?", "system.oss.config.185269-45": "Are you sure you want to set the configuration number {0} as the default data?"}