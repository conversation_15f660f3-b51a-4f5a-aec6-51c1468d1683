# 页面标题
VUE_APP_TITLE = BYC物联网系统

# 开发环境配置
ENV = 'development'

# 开发环境
VUE_APP_BASE_API = '/dev-api'

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true

# 后端接口地址 - 请替换为正式环境地址
VUE_APP_SERVER_API_URL = 'http://localhost:8080/'
# 正式环境示例：
# VUE_APP_SERVER_API_URL = 'http://your-production-server:8080/'

# Mqtt消息服务器连接地址
VUE_APP_MQTT_SERVER_URL = 'ws://127.0.0.1:9903/mqtt'

# 百度地图AK
VUE_APP_BAI_DU_AK = 'nAtaBg9FYzav6c8P9rF9qzsWZfT8O0PD'
