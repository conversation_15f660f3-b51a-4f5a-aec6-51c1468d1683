# 简单的import修复脚本
$BackendPath = "H:\fastbee2.5.1\SSAC-smart-v2.5.1-A\springboot"

Write-Host "开始修复import错误..."

# 获取所有包含错误import的Java文件
$files = Get-ChildItem -Path $BackendPath -Filter "*.java" -Recurse | Where-Object {
    (Get-Content $_.FullName -Raw) -match "import com\.ssac"
}

Write-Host "找到 $($files.Count) 个需要修复的文件"

foreach ($file in $files) {
    Write-Host "修复: $($file.Name)"
    
    # 读取文件内容
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    # 替换错误的import
    $newContent = $content -replace "import com\.ssac", "import com.fastbee"
    
    # 写回文件
    Set-Content -Path $file.FullName -Value $newContent -Encoding UTF8 -NoNewline
}

Write-Host "修复完成!"

# 验证结果
$remaining = Get-ChildItem -Path $BackendPath -Filter "*.java" -Recurse | Select-String "import com\.ssac"
Write-Host "剩余错误: $($remaining.Count) 个"
