!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("path"),require("fs"),require("crypto")):"function"==typeof define&&define.amd?define(["path","fs","crypto"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).path,e.fs,e.crypto$1)}(this,function(e,m,g){"use strict";function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var Tt=t(e),Bt=t(m),At=t(g);function y(e,t){return e(t={exports:{}},t.exports),t.exports}var _=y(function(I){var t,m,M,$,N,G,a,g=void 0!==g?g:{},O=Object.assign({},g),V="./this.program",z="object"==typeof window,d="function"==typeof importScripts,j="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,e="",W=(j?(e=d?Tt.default.dirname(e)+"/":__dirname+"/",G=()=>{N||($=Bt.default,N=Tt.default)},t=function(e,t){return G(),e=N.normalize(e),$.readFileSync(e,t?void 0:"utf8")},M=e=>{e=t(e,!0);return e=e.buffer?e:new Uint8Array(e)},m=(e,r,n)=>{G(),e=N.normalize(e),$.readFile(e,function(e,t){e?n(e):r(t.buffer)})},1<process.argv.length&&(V=process.argv[1].replace(/\\/g,"/")),process.argv.slice(2),I.exports=g,process.on("uncaughtException",function(e){throw e}),process.on("unhandledRejection",function(e){throw e}),g.inspect=function(){return"[Emscripten Module object]"}):(z||d)&&(d?e=self.location.href:"undefined"!=typeof document&&document.currentScript&&(e=document.currentScript.src),e=0!==e.indexOf("blob:")?e.substr(0,e.replace(/[?#].*/,"").lastIndexOf("/")+1):"",t=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},d&&(M=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),m=(e,t,r)=>{var n=new XMLHttpRequest;n.open("GET",e,!0),n.responseType="arraybuffer",n.onload=()=>{200==n.status||0==n.status&&n.response?t(n.response):r()},n.onerror=r,n.send(null)}),g.print||console.log.bind(console)),s=g.printErr||console.warn.bind(console),H=(Object.assign(g,O),g.arguments&&g.arguments,g.thisProgram&&(V=g.thisProgram),g.quit&&g.quit,g.wasmBinary&&(a=g.wasmBinary),g.noExitRuntime,"object"!=typeof WebAssembly&&w("no native wasm support detected"),!1);function q(e,t){e||w(t)}var X,c,f,l,K,u,p,Y,J,Z,Q="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function i(e,t,r){for(var n=t+r,o=t;e[o]&&!(n<=o);)++o;if(16<o-t&&e.buffer&&Q)return Q.decode(e.subarray(t,o));for(var a="";t<o;){var i,s,l=e[t++];128&l?(i=63&e[t++],192!=(224&l)?(s=63&e[t++],(l=224==(240&l)?(15&l)<<12|i<<6|s:(7&l)<<18|i<<12|s<<6|63&e[t++])<65536?a+=String.fromCharCode(l):(s=l-65536,a+=String.fromCharCode(55296|s>>10,56320|1023&s))):a+=String.fromCharCode((31&l)<<6|i)):a+=String.fromCharCode(l)}return a}function ee(e,t){return e?i(f,e,t):""}function te(e,t,r,n){if(!(0<n))return 0;for(var o=r,a=r+n-1,i=0;i<e.length;++i){var s=e.charCodeAt(i);if((s=55296<=s&&s<=57343?65536+((1023&s)<<10)|1023&e.charCodeAt(++i):s)<=127){if(a<=r)break;t[r++]=s}else{if(s<=2047){if(a<=r+1)break;t[r++]=192|s>>6}else{if(s<=65535){if(a<=r+2)break;t[r++]=224|s>>12}else{if(a<=r+3)break;t[r++]=240|s>>18,t[r++]=128|s>>12&63}t[r++]=128|s>>6&63}t[r++]=128|63&s}}return t[r]=0,r-o}function re(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n<=127?t++:n<=2047?t+=2:55296<=n&&n<=57343?(t+=4,++r):t+=3}return t}g.INITIAL_MEMORY;var h,y,_,ne=[],oe=[],ae=[],r=0,n=null;function ie(){r++,g.monitorRunDependencies&&g.monitorRunDependencies(r)}function se(){var e;r--,g.monitorRunDependencies&&g.monitorRunDependencies(r),0==r&&n&&(e=n,n=null,e())}function w(e){throw g.onAbort&&g.onAbort(e),s(e="Aborted("+e+")"),H=!0,e+=". Build with -sASSERTIONS for more info.",new WebAssembly.RuntimeError(e)}function le(e){return e.startsWith("data:application/octet-stream;base64,")}function ue(e){return e.startsWith("file://")}function de(e){try{if(e==h&&a)return new Uint8Array(a);if(M)return M(e);throw"both async and sync fetching of the wasm failed"}catch(e){w(e)}}function ce(e){for(;0<e.length;)e.shift()(g)}function fe(e){this.excPtr=e,this.ptr=e-24,this.set_type=function(e){p[this.ptr+4>>2]=e},this.get_type=function(){return p[this.ptr+4>>2]},this.set_destructor=function(e){p[this.ptr+8>>2]=e},this.get_destructor=function(){return p[this.ptr+8>>2]},this.set_refcount=function(e){u[this.ptr>>2]=e},this.set_caught=function(e){c[this.ptr+12>>0]=e=e?1:0},this.get_caught=function(){return 0!=c[this.ptr+12>>0]},this.set_rethrown=function(e){c[this.ptr+13>>0]=e=e?1:0},this.get_rethrown=function(){return 0!=c[this.ptr+13>>0]},this.init=function(e,t){this.set_adjusted_ptr(0),this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=u[this.ptr>>2];u[this.ptr>>2]=e+1},this.release_ref=function(){var e=u[this.ptr>>2];return u[this.ptr>>2]=e-1,1===e},this.set_adjusted_ptr=function(e){p[this.ptr+16>>2]=e},this.get_adjusted_ptr=function(){return p[this.ptr+16>>2]},this.get_exception_ptr=function(){var e;return Et(this.get_type())?p[this.excPtr>>2]:0!==(e=this.get_adjusted_ptr())?e:this.excPtr}}le(h="decoder-pro-simd.wasm")||(O=h,h=g.locateFile?g.locateFile(O,e):e+O);var v={isAbs:e=>"/"===e.charAt(0),splitPath:e=>/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1),normalizeArray:(e,t)=>{for(var r=0,n=e.length-1;0<=n;n--){var o=e[n];"."===o?e.splice(n,1):".."===o?(e.splice(n,1),r++):r&&(e.splice(n,1),r--)}if(t)for(;r;r--)e.unshift("..");return e},normalize:e=>{var t=v.isAbs(e),r="/"===e.substr(-1);return(e=(e=v.normalizeArray(e.split("/").filter(e=>!!e),!t).join("/"))||t?e:".")&&r&&(e+="/"),(t?"/":"")+e},dirname:e=>{var e=v.splitPath(e),t=e[0],e=e[1];return t||e?t+(e=e&&e.substr(0,e.length-1)):"."},basename:e=>{var t;return"/"===e?"/":-1===(t=(e=(e=v.normalize(e)).replace(/\/$/,"")).lastIndexOf("/"))?e:e.substr(t+1)},join:function(){var e=Array.prototype.slice.call(arguments,0);return v.normalize(e.join("/"))},join2:(e,t)=>v.normalize(e+"/"+t)},b={resolve:function(){for(var e="",t=!1,r=arguments.length-1;-1<=r&&!t;r--){var n=0<=r?arguments[r]:k.cwd();if("string"!=typeof n)throw new TypeError("Arguments to path.resolve must be strings");if(!n)return"";e=n+"/"+e,t=v.isAbs(n)}return(t?"/":"")+v.normalizeArray(e.split("/").filter(e=>!!e),!t).join("/")||"."},relative:(e,t)=>{function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var r=e.length-1;0<=r&&""===e[r];r--);return r<t?[]:e.slice(t,r-t+1)}e=b.resolve(e).substr(1),t=b.resolve(t).substr(1);for(var n=r(e.split("/")),o=r(t.split("/")),a=Math.min(n.length,o.length),i=a,s=0;s<a;s++)if(n[s]!==o[s]){i=s;break}for(var l=[],s=i;s<n.length;s++)l.push("..");return(l=l.concat(o.slice(i))).join("/")}};function pe(e,t,r){r=0<r?r:re(e)+1,r=new Array(r),e=te(e,r,0,r.length);return t&&(r.length=e),r}var o={ttys:[],init:function(){},shutdown:function(){},register:function(e,t){o.ttys[e]={input:[],output:[],ops:t},k.registerDevice(e,o.stream_ops)},stream_ops:{open:function(e){var t=o.ttys[e.node.rdev];if(!t)throw new k.ErrnoError(43);e.tty=t,e.seekable=!1},close:function(e){e.tty.ops.flush(e.tty)},flush:function(e){e.tty.ops.flush(e.tty)},read:function(e,t,r,n,o){if(!e.tty||!e.tty.ops.get_char)throw new k.ErrnoError(60);for(var a,i=0,s=0;s<n;s++){try{a=e.tty.ops.get_char(e.tty)}catch(e){throw new k.ErrnoError(29)}if(void 0===a&&0===i)throw new k.ErrnoError(6);if(null==a)break;i++,t[r+s]=a}return i&&(e.node.timestamp=Date.now()),i},write:function(e,t,r,n,o){if(!e.tty||!e.tty.ops.put_char)throw new k.ErrnoError(60);try{for(var a=0;a<n;a++)e.tty.ops.put_char(e.tty,t[r+a])}catch(e){throw new k.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),a}},default_tty_ops:{get_char:function(e){if(!e.input.length){var t=null;if(j){var r=Buffer.alloc(256),n=0;try{n=$.readSync(process.stdin.fd,r,0,256,-1)}catch(e){if(!e.toString().includes("EOF"))throw e;n=0}t=0<n?r.slice(0,n).toString("utf-8"):null}else"undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n");if(!t)return null;e.input=pe(t,!0)}return e.input.shift()},put_char:function(e,t){null===t||10===t?(W(i(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&0<e.output.length&&(W(i(e.output,0)),e.output=[])}},default_tty1_ops:{put_char:function(e,t){null===t||10===t?(s(i(e.output,0)),e.output=[]):0!=t&&e.output.push(t)},flush:function(e){e.output&&0<e.output.length&&(s(i(e.output,0)),e.output=[])}}};function he(e){t=e,e=65536*Math.ceil(t/65536);var t=bt(65536,e);return t?(e=e,f.fill(0,t,t+e),t):0}var E={ops_table:null,mount:function(e){return E.createNode(null,"/",16895,0)},createNode:function(e,t,r,n){if(k.isBlkdev(r)||k.isFIFO(r))throw new k.ErrnoError(63);E.ops_table||(E.ops_table={dir:{node:{getattr:E.node_ops.getattr,setattr:E.node_ops.setattr,lookup:E.node_ops.lookup,mknod:E.node_ops.mknod,rename:E.node_ops.rename,unlink:E.node_ops.unlink,rmdir:E.node_ops.rmdir,readdir:E.node_ops.readdir,symlink:E.node_ops.symlink},stream:{llseek:E.stream_ops.llseek}},file:{node:{getattr:E.node_ops.getattr,setattr:E.node_ops.setattr},stream:{llseek:E.stream_ops.llseek,read:E.stream_ops.read,write:E.stream_ops.write,allocate:E.stream_ops.allocate,mmap:E.stream_ops.mmap,msync:E.stream_ops.msync}},link:{node:{getattr:E.node_ops.getattr,setattr:E.node_ops.setattr,readlink:E.node_ops.readlink},stream:{}},chrdev:{node:{getattr:E.node_ops.getattr,setattr:E.node_ops.setattr},stream:k.chrdev_stream_ops}});r=k.createNode(e,t,r,n);return k.isDir(r.mode)?(r.node_ops=E.ops_table.dir.node,r.stream_ops=E.ops_table.dir.stream,r.contents={}):k.isFile(r.mode)?(r.node_ops=E.ops_table.file.node,r.stream_ops=E.ops_table.file.stream,r.usedBytes=0,r.contents=null):k.isLink(r.mode)?(r.node_ops=E.ops_table.link.node,r.stream_ops=E.ops_table.link.stream):k.isChrdev(r.mode)&&(r.node_ops=E.ops_table.chrdev.node,r.stream_ops=E.ops_table.chrdev.stream),r.timestamp=Date.now(),e&&(e.contents[t]=r,e.timestamp=r.timestamp),r},getFileDataAsTypedArray:function(e){return e.contents?e.contents.subarray?e.contents.subarray(0,e.usedBytes):new Uint8Array(e.contents):new Uint8Array(0)},expandFileStorage:function(e,t){var r=e.contents?e.contents.length:0;t<=r||(t=Math.max(t,r*(r<1048576?2:1.125)>>>0),0!=r&&(t=Math.max(t,256)),r=e.contents,e.contents=new Uint8Array(t),0<e.usedBytes&&e.contents.set(r.subarray(0,e.usedBytes),0))},resizeFileStorage:function(e,t){var r;e.usedBytes!=t&&(0==t?(e.contents=null,e.usedBytes=0):(r=e.contents,e.contents=new Uint8Array(t),r&&e.contents.set(r.subarray(0,Math.min(t,e.usedBytes))),e.usedBytes=t))},node_ops:{getattr:function(e){var t={};return t.dev=k.isChrdev(e.mode)?e.id:1,t.ino=e.id,t.mode=e.mode,t.nlink=1,t.uid=0,t.gid=0,t.rdev=e.rdev,k.isDir(e.mode)?t.size=4096:k.isFile(e.mode)?t.size=e.usedBytes:k.isLink(e.mode)?t.size=e.link.length:t.size=0,t.atime=new Date(e.timestamp),t.mtime=new Date(e.timestamp),t.ctime=new Date(e.timestamp),t.blksize=4096,t.blocks=Math.ceil(t.size/t.blksize),t},setattr:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&E.resizeFileStorage(e,t.size)},lookup:function(e,t){throw k.genericErrors[44]},mknod:function(e,t,r,n){return E.createNode(e,t,r,n)},rename:function(e,t,r){if(k.isDir(e.mode)){var n;try{n=k.lookupNode(t,r)}catch(e){}if(n)for(var o in n.contents)throw new k.ErrnoError(55)}delete e.parent.contents[e.name],e.parent.timestamp=Date.now(),e.name=r,t.contents[r]=e,t.timestamp=e.parent.timestamp,e.parent=t},unlink:function(e,t){delete e.contents[t],e.timestamp=Date.now()},rmdir:function(e,t){for(var r in k.lookupNode(e,t).contents)throw new k.ErrnoError(55);delete e.contents[t],e.timestamp=Date.now()},readdir:function(e){var t,r=[".",".."];for(t in e.contents)e.contents.hasOwnProperty(t)&&r.push(t);return r},symlink:function(e,t,r){e=E.createNode(e,t,41471,0);return e.link=r,e},readlink:function(e){if(k.isLink(e.mode))return e.link;throw new k.ErrnoError(28)}},stream_ops:{read:function(e,t,r,n,o){var a=e.node.contents;if(o>=e.node.usedBytes)return 0;var i=Math.min(e.node.usedBytes-o,n);if(8<i&&a.subarray)t.set(a.subarray(o,o+i),r);else for(var s=0;s<i;s++)t[r+s]=a[o+s];return i},write:function(e,t,r,n,o,a){if(!n)return 0;var i=e.node;if(i.timestamp=Date.now(),t.subarray&&(!i.contents||i.contents.subarray)){if(a)return i.contents=t.subarray(r,r+n),i.usedBytes=n;if(0===i.usedBytes&&0===o)return i.contents=t.slice(r,r+n),i.usedBytes=n;if(o+n<=i.usedBytes)return i.contents.set(t.subarray(r,r+n),o),n}if(E.expandFileStorage(i,o+n),i.contents.subarray&&t.subarray)i.contents.set(t.subarray(r,r+n),o);else for(var s=0;s<n;s++)i.contents[o+s]=t[r+s];return i.usedBytes=Math.max(i.usedBytes,o+n),n},llseek:function(e,t,r){if(1===r?t+=e.position:2===r&&k.isFile(e.node.mode)&&(t+=e.node.usedBytes),t<0)throw new k.ErrnoError(28);return t},allocate:function(e,t,r){E.expandFileStorage(e.node,t+r),e.node.usedBytes=Math.max(e.node.usedBytes,t+r)},mmap:function(e,t,r,n,o){if(!k.isFile(e.node.mode))throw new k.ErrnoError(43);var a,i,e=e.node.contents;if(2&o||e.buffer!==X){if((0<r||r+t<e.length)&&(e=e.subarray?e.subarray(r,r+t):Array.prototype.slice.call(e,r,r+t)),i=!0,!(a=he(t)))throw new k.ErrnoError(48);c.set(e,a)}else i=!1,a=e.byteOffset;return{ptr:a,allocated:i}},msync:function(e,t,r,n,o){if(k.isFile(e.node.mode))return 2&o||E.stream_ops.write(e,t,0,n,r,!1),0;throw new k.ErrnoError(43)}}},k={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:!1,ignorePermissions:!0,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(!(e=b.resolve(k.cwd(),e)))return{path:"",node:null};if(8<(t=Object.assign({follow_mount:!0,recurse_count:0},t)).recurse_count)throw new k.ErrnoError(32);for(var r=v.normalizeArray(e.split("/").filter(e=>!!e),!1),n=k.root,o="/",a=0;a<r.length;a++){var i=a===r.length-1;if(i&&t.parent)break;if(n=k.lookupNode(n,r[a]),o=v.join2(o,r[a]),!k.isMountpoint(n)||i&&!t.follow_mount||(n=n.mounted.root),!i||t.follow)for(var s=0;k.isLink(n.mode);){var l=k.readlink(o),o=b.resolve(v.dirname(o),l),n=k.lookupPath(o,{recurse_count:t.recurse_count+1}).node;if(40<s++)throw new k.ErrnoError(32)}}return{path:o,node:n}},getPath:e=>{for(var t,r;;){if(k.isRoot(e))return r=e.mount.mountpoint,t?"/"!==r[r.length-1]?r+"/"+t:r+t:r;t=t?e.name+"/"+t:e.name,e=e.parent}},hashName:(e,t)=>{for(var r=0,n=0;n<t.length;n++)r=(r<<5)-r+t.charCodeAt(n)|0;return(e+r>>>0)%k.nameTable.length},hashAddNode:e=>{var t=k.hashName(e.parent.id,e.name);e.name_next=k.nameTable[t],k.nameTable[t]=e},hashRemoveNode:e=>{var t=k.hashName(e.parent.id,e.name);if(k.nameTable[t]===e)k.nameTable[t]=e.name_next;else for(var r=k.nameTable[t];r;){if(r.name_next===e){r.name_next=e.name_next;break}r=r.name_next}},lookupNode:(e,t)=>{var r=k.mayLookup(e);if(r)throw new k.ErrnoError(r,e);for(var r=k.hashName(e.id,t),n=k.nameTable[r];n;n=n.name_next){var o=n.name;if(n.parent.id===e.id&&o===t)return n}return k.lookup(e,t)},createNode:(e,t,r,n)=>{e=new k.FSNode(e,t,r,n);return k.hashAddNode(e),e},destroyNode:e=>{k.hashRemoveNode(e)},isRoot:e=>e===e.parent,isMountpoint:e=>!!e.mounted,isFile:e=>32768==(61440&e),isDir:e=>16384==(61440&e),isLink:e=>40960==(61440&e),isChrdev:e=>8192==(61440&e),isBlkdev:e=>24576==(61440&e),isFIFO:e=>4096==(61440&e),isSocket:e=>49152==(49152&e),flagModes:{r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090},modeStringToFlags:e=>{var t=k.flagModes[e];if(void 0===t)throw new Error("Unknown file open mode: "+e);return t},flagsToPermissionString:e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},nodePermissions:(e,t)=>k.ignorePermissions||(!t.includes("r")||292&e.mode)&&(!t.includes("w")||146&e.mode)&&(!t.includes("x")||73&e.mode)?0:2,mayLookup:e=>{return k.nodePermissions(e,"x")||(e.node_ops.lookup?0:2)},mayCreate:(e,t)=>{try{return k.lookupNode(e,t),20}catch(e){}return k.nodePermissions(e,"wx")},mayDelete:(e,t,r)=>{var n;try{n=k.lookupNode(e,t)}catch(e){return e.errno}t=k.nodePermissions(e,"wx");if(t)return t;if(r){if(!k.isDir(n.mode))return 54;if(k.isRoot(n)||k.getPath(n)===k.cwd())return 10}else if(k.isDir(n.mode))return 31;return 0},mayOpen:(e,t)=>e?k.isLink(e.mode)?32:k.isDir(e.mode)&&("r"!==k.flagsToPermissionString(t)||512&t)?31:k.nodePermissions(e,k.flagsToPermissionString(t)):44,MAX_OPEN_FDS:4096,nextfd:function(){for(var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:k.MAX_OPEN_FDS,r=e;r<=t;r++)if(!k.streams[r])return r;throw new k.ErrnoError(33)},getStream:e=>k.streams[e],createStream:(e,t,r)=>{k.FSStream||(k.FSStream=function(){this.shared={}},k.FSStream.prototype={},Object.defineProperties(k.FSStream.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},isRead:{get:function(){return 1!=(2097155&this.flags)}},isWrite:{get:function(){return 0!=(2097155&this.flags)}},isAppend:{get:function(){return 1024&this.flags}},flags:{get:function(){return this.shared.flags},set:function(e){this.shared.flags=e}},position:{get:function(){return this.shared.position},set:function(e){this.shared.position=e}}})),e=Object.assign(new k.FSStream,e);t=k.nextfd(t,r);return e.fd=t,k.streams[t]=e},closeStream:e=>{k.streams[e]=null},chrdev_stream_ops:{open:e=>{var t=k.getDevice(e.node.rdev);e.stream_ops=t.stream_ops,e.stream_ops.open&&e.stream_ops.open(e)},llseek:()=>{throw new k.ErrnoError(70)}},major:e=>e>>8,minor:e=>255&e,makedev:(e,t)=>e<<8|t,registerDevice:(e,t)=>{k.devices[e]={stream_ops:t}},getDevice:e=>k.devices[e],getMounts:e=>{for(var t=[],r=[e];r.length;){var n=r.pop();t.push(n),r.push.apply(r,n.mounts)}return t},syncfs:(t,r)=>{"function"==typeof t&&(r=t,t=!1),k.syncFSRequests++,1<k.syncFSRequests&&s("warning: "+k.syncFSRequests+" FS.syncfs operations in flight at once, probably just doing extra work");var n=k.getMounts(k.root.mount),o=0;function a(e){return k.syncFSRequests--,r(e)}function i(e){if(e)return i.errored?void 0:(i.errored=!0,a(e));++o>=n.length&&a(null)}n.forEach(e=>{if(!e.type.syncfs)return i(null);e.type.syncfs(e,t,i)})},mount:(e,t,r)=>{var n,o="/"===r,a=!r;if(o&&k.root)throw new k.ErrnoError(10);if(!o&&!a){a=k.lookupPath(r,{follow_mount:!1});if(r=a.path,n=a.node,k.isMountpoint(n))throw new k.ErrnoError(10);if(!k.isDir(n.mode))throw new k.ErrnoError(54)}a={type:e,opts:t,mountpoint:r,mounts:[]},t=e.mount(a);return(t.mount=a).root=t,o?k.root=t:n&&(n.mounted=a,n.mount)&&n.mount.mounts.push(a),t},unmount:e=>{e=k.lookupPath(e,{follow_mount:!1});if(!k.isMountpoint(e.node))throw new k.ErrnoError(28);var e=e.node,t=e.mounted,n=k.getMounts(t),t=(Object.keys(k.nameTable).forEach(e=>{for(var t=k.nameTable[e];t;){var r=t.name_next;n.includes(t.mount)&&k.destroyNode(t),t=r}}),e.mounted=null,e.mount.mounts.indexOf(t));e.mount.mounts.splice(t,1)},lookup:(e,t)=>e.node_ops.lookup(e,t),mknod:(e,t,r)=>{var n=k.lookupPath(e,{parent:!0}).node,e=v.basename(e);if(!e||"."===e||".."===e)throw new k.ErrnoError(28);var o=k.mayCreate(n,e);if(o)throw new k.ErrnoError(o);if(n.node_ops.mknod)return n.node_ops.mknod(n,e,t,r);throw new k.ErrnoError(63)},create:(e,t)=>k.mknod(e,t=(t=void 0!==t?t:438)&4095|32768,0),mkdir:(e,t)=>k.mknod(e,t=(t=void 0!==t?t:511)&1023|16384,0),mkdirTree:(e,t)=>{for(var r=e.split("/"),n="",o=0;o<r.length;++o)if(r[o]){n+="/"+r[o];try{k.mkdir(n,t)}catch(e){if(20!=e.errno)throw e}}},mkdev:(e,t,r)=>(void 0===r&&(r=t,t=438),k.mknod(e,t|=8192,r)),symlink:(e,t)=>{if(!b.resolve(e))throw new k.ErrnoError(44);var r=k.lookupPath(t,{parent:!0}).node;if(!r)throw new k.ErrnoError(44);var t=v.basename(t),n=k.mayCreate(r,t);if(n)throw new k.ErrnoError(n);if(r.node_ops.symlink)return r.node_ops.symlink(r,t,e);throw new k.ErrnoError(63)},rename:(e,t)=>{var r=v.dirname(e),n=v.dirname(t),o=v.basename(e),a=v.basename(t),i=k.lookupPath(e,{parent:!0}).node,s=k.lookupPath(t,{parent:!0}).node;if(!i||!s)throw new k.ErrnoError(44);if(i.mount!==s.mount)throw new k.ErrnoError(75);var l,u=k.lookupNode(i,o);if("."!==b.relative(e,n).charAt(0))throw new k.ErrnoError(28);if("."!==b.relative(t,r).charAt(0))throw new k.ErrnoError(55);try{l=k.lookupNode(s,a)}catch(e){}if(u!==l){n=k.isDir(u.mode),t=k.mayDelete(i,o,n);if(t)throw new k.ErrnoError(t);if(t=l?k.mayDelete(s,a,n):k.mayCreate(s,a))throw new k.ErrnoError(t);if(!i.node_ops.rename)throw new k.ErrnoError(63);if(k.isMountpoint(u)||l&&k.isMountpoint(l))throw new k.ErrnoError(10);if(s!==i&&(t=k.nodePermissions(i,"w")))throw new k.ErrnoError(t);k.hashRemoveNode(u);try{i.node_ops.rename(u,s,a)}catch(e){throw e}finally{k.hashAddNode(u)}}},rmdir:e=>{var t=k.lookupPath(e,{parent:!0}).node,e=v.basename(e),r=k.lookupNode(t,e),n=k.mayDelete(t,e,!0);if(n)throw new k.ErrnoError(n);if(!t.node_ops.rmdir)throw new k.ErrnoError(63);if(k.isMountpoint(r))throw new k.ErrnoError(10);t.node_ops.rmdir(t,e),k.destroyNode(r)},readdir:e=>{e=k.lookupPath(e,{follow:!0}).node;if(e.node_ops.readdir)return e.node_ops.readdir(e);throw new k.ErrnoError(54)},unlink:e=>{var t=k.lookupPath(e,{parent:!0}).node;if(!t)throw new k.ErrnoError(44);var e=v.basename(e),r=k.lookupNode(t,e),n=k.mayDelete(t,e,!1);if(n)throw new k.ErrnoError(n);if(!t.node_ops.unlink)throw new k.ErrnoError(63);if(k.isMountpoint(r))throw new k.ErrnoError(10);t.node_ops.unlink(t,e),k.destroyNode(r)},readlink:e=>{e=k.lookupPath(e).node;if(!e)throw new k.ErrnoError(44);if(e.node_ops.readlink)return b.resolve(k.getPath(e.parent),e.node_ops.readlink(e));throw new k.ErrnoError(28)},stat:(e,t)=>{e=k.lookupPath(e,{follow:!t}).node;if(!e)throw new k.ErrnoError(44);if(e.node_ops.getattr)return e.node_ops.getattr(e);throw new k.ErrnoError(63)},lstat:e=>k.stat(e,!0),chmod:(e,t,r)=>{r="string"==typeof e?k.lookupPath(e,{follow:!r}).node:e;if(!r.node_ops.setattr)throw new k.ErrnoError(63);r.node_ops.setattr(r,{mode:4095&t|-4096&r.mode,timestamp:Date.now()})},lchmod:(e,t)=>{k.chmod(e,t,!0)},fchmod:(e,t)=>{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);k.chmod(e.node,t)},chown:(e,t,r,n)=>{n="string"==typeof e?k.lookupPath(e,{follow:!n}).node:e;if(!n.node_ops.setattr)throw new k.ErrnoError(63);n.node_ops.setattr(n,{timestamp:Date.now()})},lchown:(e,t,r)=>{k.chown(e,t,r,!0)},fchown:(e,t,r)=>{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);k.chown(e.node,t,r)},truncate:(e,t)=>{if(t<0)throw new k.ErrnoError(28);e="string"==typeof e?k.lookupPath(e,{follow:!0}).node:e;if(!e.node_ops.setattr)throw new k.ErrnoError(63);if(k.isDir(e.mode))throw new k.ErrnoError(31);if(!k.isFile(e.mode))throw new k.ErrnoError(28);var r=k.nodePermissions(e,"w");if(r)throw new k.ErrnoError(r);e.node_ops.setattr(e,{size:t,timestamp:Date.now()})},ftruncate:(e,t)=>{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);if(0==(2097155&e.flags))throw new k.ErrnoError(28);k.truncate(e.node,t)},utime:(e,t,r)=>{e=k.lookupPath(e,{follow:!0}).node;e.node_ops.setattr(e,{timestamp:Math.max(t,r)})},open:(e,t,r)=>{if(""===e)throw new k.ErrnoError(44);var n;if(r=void 0===r?438:r,r=64&(t="string"==typeof t?k.modeStringToFlags(t):t)?4095&r|32768:0,"object"==typeof e)n=e;else{e=v.normalize(e);try{n=k.lookupPath(e,{follow:!(131072&t)}).node}catch(e){}}var o=!1;if(64&t)if(n){if(128&t)throw new k.ErrnoError(20)}else n=k.mknod(e,r,0),o=!0;if(!n)throw new k.ErrnoError(44);if(k.isChrdev(n.mode)&&(t&=-513),65536&t&&!k.isDir(n.mode))throw new k.ErrnoError(54);if(!o){r=k.mayOpen(n,t);if(r)throw new k.ErrnoError(r)}512&t&&!o&&k.truncate(n,0),t&=-131713;r=k.createStream({node:n,path:k.getPath(n),flags:t,seekable:!0,position:0,stream_ops:n.stream_ops,ungotten:[],error:!1});return r.stream_ops.open&&r.stream_ops.open(r),!g.logReadFiles||1&t||(k.readFiles||(k.readFiles={}),e in k.readFiles)||(k.readFiles[e]=1),r},close:e=>{if(k.isClosed(e))throw new k.ErrnoError(8);e.getdents&&(e.getdents=null);try{e.stream_ops.close&&e.stream_ops.close(e)}catch(e){throw e}finally{k.closeStream(e.fd)}e.fd=null},isClosed:e=>null===e.fd,llseek:(e,t,r)=>{if(k.isClosed(e))throw new k.ErrnoError(8);if(!e.seekable||!e.stream_ops.llseek)throw new k.ErrnoError(70);if(0!=r&&1!=r&&2!=r)throw new k.ErrnoError(28);return e.position=e.stream_ops.llseek(e,t,r),e.ungotten=[],e.position},read:(e,t,r,n,o)=>{if(n<0||o<0)throw new k.ErrnoError(28);if(k.isClosed(e))throw new k.ErrnoError(8);if(1==(2097155&e.flags))throw new k.ErrnoError(8);if(k.isDir(e.node.mode))throw new k.ErrnoError(31);if(!e.stream_ops.read)throw new k.ErrnoError(28);var a=void 0!==o;if(a){if(!e.seekable)throw new k.ErrnoError(70)}else o=e.position;t=e.stream_ops.read(e,t,r,n,o);return a||(e.position+=t),t},write:(e,t,r,n,o,a)=>{if(n<0||o<0)throw new k.ErrnoError(28);if(k.isClosed(e))throw new k.ErrnoError(8);if(0==(2097155&e.flags))throw new k.ErrnoError(8);if(k.isDir(e.node.mode))throw new k.ErrnoError(31);if(!e.stream_ops.write)throw new k.ErrnoError(28);e.seekable&&1024&e.flags&&k.llseek(e,0,2);var i=void 0!==o;if(i){if(!e.seekable)throw new k.ErrnoError(70)}else o=e.position;t=e.stream_ops.write(e,t,r,n,o,a);return i||(e.position+=t),t},allocate:(e,t,r)=>{if(k.isClosed(e))throw new k.ErrnoError(8);if(t<0||r<=0)throw new k.ErrnoError(28);if(0==(2097155&e.flags))throw new k.ErrnoError(8);if(!k.isFile(e.node.mode)&&!k.isDir(e.node.mode))throw new k.ErrnoError(43);if(!e.stream_ops.allocate)throw new k.ErrnoError(138);e.stream_ops.allocate(e,t,r)},mmap:(e,t,r,n,o)=>{if(0!=(2&n)&&0==(2&o)&&2!=(2097155&e.flags))throw new k.ErrnoError(2);if(1==(2097155&e.flags))throw new k.ErrnoError(2);if(e.stream_ops.mmap)return e.stream_ops.mmap(e,t,r,n,o);throw new k.ErrnoError(43)},msync:(e,t,r,n,o)=>e&&e.stream_ops.msync?e.stream_ops.msync(e,t,r,n,o):0,munmap:e=>0,ioctl:(e,t,r)=>{if(e.stream_ops.ioctl)return e.stream_ops.ioctl(e,t,r);throw new k.ErrnoError(59)},readFile:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(t.flags=t.flags||0,t.encoding=t.encoding||"binary","utf8"!==t.encoding&&"binary"!==t.encoding)throw new Error('Invalid encoding type "'+t.encoding+'"');var r,n=k.open(e,t.flags),e=k.stat(e).size,o=new Uint8Array(e);return k.read(n,o,0,e,0),"utf8"===t.encoding?r=i(o,0):"binary"===t.encoding&&(r=o),k.close(n),r},writeFile:function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},e=(r.flags=r.flags||577,k.open(e,r.flags,r.mode));if("string"==typeof t){var n=new Uint8Array(re(t)+1),o=te(t,n,0,n.length);k.write(e,n,0,o,void 0,r.canOwn)}else{if(!ArrayBuffer.isView(t))throw new Error("Unsupported data type");k.write(e,t,0,t.byteLength,void 0,r.canOwn)}k.close(e)},cwd:()=>k.currentPath,chdir:e=>{e=k.lookupPath(e,{follow:!0});if(null===e.node)throw new k.ErrnoError(44);if(!k.isDir(e.node.mode))throw new k.ErrnoError(54);var t=k.nodePermissions(e.node,"x");if(t)throw new k.ErrnoError(t);k.currentPath=e.path},createDefaultDirectories:()=>{k.mkdir("/tmp"),k.mkdir("/home"),k.mkdir("/home/<USER>")},createDefaultDevices:()=>{k.mkdir("/dev"),k.registerDevice(k.makedev(1,3),{read:()=>0,write:(e,t,r,n,o)=>n}),k.mkdev("/dev/null",k.makedev(1,3)),o.register(k.makedev(5,0),o.default_tty_ops),o.register(k.makedev(6,0),o.default_tty1_ops),k.mkdev("/dev/tty",k.makedev(5,0)),k.mkdev("/dev/tty1",k.makedev(6,0));var e=function(){var e;if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return e=new Uint8Array(1),()=>(crypto.getRandomValues(e),e[0]);if(j)try{var t=At.default;return()=>t.randomBytes(1)[0]}catch(e){}return()=>w("randomDevice")}();k.createDevice("/dev","random",e),k.createDevice("/dev","urandom",e),k.mkdir("/dev/shm"),k.mkdir("/dev/shm/tmp")},createSpecialDirectories:()=>{k.mkdir("/proc");var t=k.mkdir("/proc/self");k.mkdir("/proc/self/fd"),k.mount({mount:()=>{var e=k.createNode(t,"fd",16895,73);return e.node_ops={lookup:(e,t)=>{var r=k.getStream(+t);if(r)return(t={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>r.path}}).parent=t;throw new k.ErrnoError(8)}},e}},{},"/proc/self/fd")},createStandardStreams:()=>{g.stdin?k.createDevice("/dev","stdin",g.stdin):k.symlink("/dev/tty","/dev/stdin"),g.stdout?k.createDevice("/dev","stdout",null,g.stdout):k.symlink("/dev/tty","/dev/stdout"),g.stderr?k.createDevice("/dev","stderr",null,g.stderr):k.symlink("/dev/tty1","/dev/stderr"),k.open("/dev/stdin",0),k.open("/dev/stdout",1),k.open("/dev/stderr",1)},ensureErrnoError:()=>{k.ErrnoError||(k.ErrnoError=function(e,t){this.node=t,this.setErrno=function(e){this.errno=e},this.setErrno(e),this.message="FS error"},k.ErrnoError.prototype=new Error,k.ErrnoError.prototype.constructor=k.ErrnoError,[44].forEach(e=>{k.genericErrors[e]=new k.ErrnoError(e),k.genericErrors[e].stack="<generic error, no stack>"}))},staticInit:()=>{k.ensureErrnoError(),k.nameTable=new Array(4096),k.mount(E,{},"/"),k.createDefaultDirectories(),k.createDefaultDevices(),k.createSpecialDirectories(),k.filesystems={MEMFS:E}},init:(e,t,r)=>{k.init.initialized=!0,k.ensureErrnoError(),g.stdin=e||g.stdin,g.stdout=t||g.stdout,g.stderr=r||g.stderr,k.createStandardStreams()},quit:()=>{k.init.initialized=!1;for(var e=0;e<k.streams.length;e++){var t=k.streams[e];t&&k.close(t)}},getMode:(e,t)=>{var r=0;return e&&(r|=365),t&&(r|=146),r},findObject:(e,t)=>{e=k.analyzePath(e,t);return e.exists?e.object:null},analyzePath:(e,t)=>{try{e=(n=k.lookupPath(e,{follow:!t})).path}catch(e){}var r={isRoot:!1,exists:!1,error:0,name:null,path:null,object:null,parentExists:!1,parentPath:null,parentObject:null};try{var n=k.lookupPath(e,{parent:!0});r.parentExists=!0,r.parentPath=n.path,r.parentObject=n.node,r.name=v.basename(e),n=k.lookupPath(e,{follow:!t}),r.exists=!0,r.path=n.path,r.object=n.node,r.name=n.node.name,r.isRoot="/"===n.path}catch(e){r.error=e.errno}return r},createPath:(e,t,r,n)=>{e="string"==typeof e?e:k.getPath(e);for(var o=t.split("/").reverse();o.length;){var a=o.pop();if(a){var i=v.join2(e,a);try{k.mkdir(i)}catch(e){}e=i}}return i},createFile:(e,t,r,n,o)=>{e=v.join2("string"==typeof e?e:k.getPath(e),t),t=k.getMode(n,o);return k.create(e,t)},createDataFile:(e,t,r,n,o,a)=>{var i=t,t=(e&&(e="string"==typeof e?e:k.getPath(e),i=t?v.join2(e,t):e),k.getMode(n,o)),e=k.create(i,t);if(r){if("string"==typeof r){for(var s=new Array(r.length),l=0,u=r.length;l<u;++l)s[l]=r.charCodeAt(l);r=s}k.chmod(e,146|t);n=k.open(e,577);k.write(n,r,0,r.length,0,a),k.close(n),k.chmod(e,t)}return e},createDevice:(e,t,l,i)=>{var e=v.join2("string"==typeof e?e:k.getPath(e),t),t=k.getMode(!!l,!!i),r=(k.createDevice.major||(k.createDevice.major=64),k.makedev(k.createDevice.major++,0));return k.registerDevice(r,{open:e=>{e.seekable=!1},close:e=>{i&&i.buffer&&i.buffer.length&&i(10)},read:(e,t,r,n,o)=>{for(var a,i=0,s=0;s<n;s++){try{a=l()}catch(e){throw new k.ErrnoError(29)}if(void 0===a&&0===i)throw new k.ErrnoError(6);if(null==a)break;i++,t[r+s]=a}return i&&(e.node.timestamp=Date.now()),i},write:(e,t,r,n,o)=>{for(var a=0;a<n;a++)try{i(t[r+a])}catch(e){throw new k.ErrnoError(29)}return n&&(e.node.timestamp=Date.now()),a}}),k.mkdev(e,t,r)},forceLoadFile:e=>{if(e.isDevice||e.isFolder||e.link||e.contents)return!0;if("undefined"!=typeof XMLHttpRequest)throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!t)throw new Error("Cannot load without read() or XMLHttpRequest.");try{e.contents=pe(t(e.url),!0),e.usedBytes=e.contents.length}catch(e){throw new k.ErrnoError(29)}},createLazyFile:(e,t,i,r,n)=>{function o(){this.lengthKnown=!1,this.chunks=[]}if(o.prototype.get=function(e){var t;if(!(e>this.length-1||e<0))return t=e%this.chunkSize,e=e/this.chunkSize|0,this.getter(e)[t]},o.prototype.setDataGetter=function(e){this.getter=e},o.prototype.cacheLength=function(){var e=new XMLHttpRequest;if(e.open("HEAD",i,!1),e.send(null),!(200<=e.status&&e.status<300||304===e.status))throw new Error("Couldn't load "+i+". Status: "+e.status);var t,n=Number(e.getResponseHeader("Content-length")),r=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t,e=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t,o=1048576,a=(r||(o=n),this);a.setDataGetter(e=>{var t=e*o,r=(e+1)*o-1,r=Math.min(r,n-1);if(void 0===a.chunks[e]&&(a.chunks[e]=((e,t)=>{if(t<e)throw new Error("invalid range ("+e+", "+t+") or no bytes requested!");if(n-1<t)throw new Error("only "+n+" bytes available! programmer error!");var r=new XMLHttpRequest;if(r.open("GET",i,!1),n!==o&&r.setRequestHeader("Range","bytes="+e+"-"+t),r.responseType="arraybuffer",r.overrideMimeType&&r.overrideMimeType("text/plain; charset=x-user-defined"),r.send(null),200<=r.status&&r.status<300||304===r.status)return void 0!==r.response?new Uint8Array(r.response||[]):pe(r.responseText||"",!0);throw new Error("Couldn't load "+i+". Status: "+r.status)})(t,r)),void 0===a.chunks[e])throw new Error("doXHR failed!");return a.chunks[e]}),!e&&n||(o=n=1,n=this.getter(0).length,o=n,W("LazyFiles on gzip forces download of the whole file when length is accessed")),this._length=n,this._chunkSize=o,this.lengthKnown=!0},"undefined"!=typeof XMLHttpRequest){if(!d)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var a=new o,a=(Object.defineProperties(a,{length:{get:function(){return this.lengthKnown||this.cacheLength(),this._length}},chunkSize:{get:function(){return this.lengthKnown||this.cacheLength(),this._chunkSize}}}),{isDevice:!1,contents:a})}else a={isDevice:!1,url:i};var s=k.createFile(e,t,a,r,n),l=(a.contents?s.contents=a.contents:a.url&&(s.contents=null,s.url=a.url),Object.defineProperties(s,{usedBytes:{get:function(){return this.contents.length}}}),{});function u(e,t,r,n,o){var a=e.node.contents;if(o>=a.length)return 0;var i=Math.min(a.length-o,n);if(a.slice)for(var s=0;s<i;s++)t[r+s]=a[o+s];else for(s=0;s<i;s++)t[r+s]=a.get(o+s);return i}return Object.keys(s.stream_ops).forEach(e=>{var t=s.stream_ops[e];l[e]=function(){return k.forceLoadFile(s),t.apply(null,arguments)}}),l.read=(e,t,r,n,o)=>(k.forceLoadFile(s),u(e,t,r,n,o)),l.mmap=(e,t,r,n,o)=>{k.forceLoadFile(s);var a=he(t);if(a)return u(e,c,a,t,r),{ptr:a,allocated:!0};throw new k.ErrnoError(48)},s.stream_ops=l,s},createPreloadedFile:(r,n,e,o,a,i,s,l,u,d)=>{var t,c,f,p=n?b.resolve(v.join2(r,n)):r;function h(e){function t(e){d&&d(),l||k.createDataFile(r,n,e,o,a,u),i&&i(),se()}Browser.handledByPreloadPlugin(e,p,t,()=>{s&&s(),se()})||t(e)}ie(),"string"==typeof e?(c=s,f="al "+(t=e),m(t,e=>{q(e,'Loading data file "'+t+'" failed (no arrayBuffer).'),h(new Uint8Array(e)),f&&se()},e=>{if(!c)throw'Loading data file "'+t+'" failed.';c()}),f&&ie()):h(e)},indexedDB:()=>window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB,DB_NAME:()=>"EM_FS_"+window.location.pathname,DB_VERSION:20,DB_STORE_NAME:"FILE_DATA",saveFilesToDB:(i,s,l)=>{s=s||(()=>{}),l=l||(()=>{});var e=k.indexedDB();try{var u=e.open(k.DB_NAME(),k.DB_VERSION)}catch(i){return l(i)}u.onupgradeneeded=()=>{W("creating db"),u.result.createObjectStore(k.DB_STORE_NAME)},u.onsuccess=()=>{var e=u.result.transaction([k.DB_STORE_NAME],"readwrite"),t=e.objectStore(k.DB_STORE_NAME),r=0,n=0,o=i.length;function a(){(0==n?s:l)()}i.forEach(e=>{e=t.put(k.analyzePath(e).object.contents,e);e.onsuccess=()=>{++r+n==o&&a()},e.onerror=()=>{r+ ++n==o&&a()}}),e.onerror=l},u.onerror=l},loadFilesFromDB:(s,l,u)=>{l=l||(()=>{}),u=u||(()=>{});var e=k.indexedDB();try{var d=e.open(k.DB_NAME(),k.DB_VERSION)}catch(s){return u(s)}d.onupgradeneeded=u,d.onsuccess=()=>{var e=d.result;try{var t=e.transaction([k.DB_STORE_NAME],"readonly")}catch(e){return void u(e)}var r=t.objectStore(k.DB_STORE_NAME),n=0,o=0,a=s.length;function i(){(0==o?l:u)()}s.forEach(e=>{var t=r.get(e);t.onsuccess=()=>{k.analyzePath(e).exists&&k.unlink(e),k.createDataFile(v.dirname(e),v.basename(e),t.result,!0,!0,!0),++n+o==a&&i()},t.onerror=()=>{n+ ++o==a&&i()}}),t.onerror=u},d.onerror=u}},T={DEFAULT_POLLMASK:5,calculateAt:function(e,t,r){if(v.isAbs(t))return t;var n;if(-100===e)n=k.cwd();else{e=k.getStream(e);if(!e)throw new k.ErrnoError(8);n=e.path}if(0!=t.length)return v.join2(n,t);if(r)return n;throw new k.ErrnoError(44)},doStat:function(e,t,r){try{var n=e(t)}catch(e){if(e&&e.node&&v.normalize(t)!==v.normalize(k.getPath(e.node)))return-54;throw e}return u[r>>2]=n.dev,u[r+4>>2]=0,u[r+8>>2]=n.ino,u[r+12>>2]=n.mode,u[r+16>>2]=n.nlink,u[r+20>>2]=n.uid,u[r+24>>2]=n.gid,u[r+28>>2]=n.rdev,u[r+32>>2]=0,_=[n.size>>>0,(y=n.size,1<=+Math.abs(y)?0<y?(0|Math.min(+Math.floor(y/4294967296),4294967295))>>>0:~~+Math.ceil((y-(~~y>>>0))/4294967296)>>>0:0)],u[r+40>>2]=_[0],u[r+44>>2]=_[1],u[r+48>>2]=4096,u[r+52>>2]=n.blocks,_=[Math.floor(n.atime.getTime()/1e3)>>>0,(y=Math.floor(n.atime.getTime()/1e3),1<=+Math.abs(y)?0<y?(0|Math.min(+Math.floor(y/4294967296),4294967295))>>>0:~~+Math.ceil((y-(~~y>>>0))/4294967296)>>>0:0)],u[r+56>>2]=_[0],u[r+60>>2]=_[1],u[r+64>>2]=0,_=[Math.floor(n.mtime.getTime()/1e3)>>>0,(y=Math.floor(n.mtime.getTime()/1e3),1<=+Math.abs(y)?0<y?(0|Math.min(+Math.floor(y/4294967296),4294967295))>>>0:~~+Math.ceil((y-(~~y>>>0))/4294967296)>>>0:0)],u[r+72>>2]=_[0],u[r+76>>2]=_[1],u[r+80>>2]=0,_=[Math.floor(n.ctime.getTime()/1e3)>>>0,(y=Math.floor(n.ctime.getTime()/1e3),1<=+Math.abs(y)?0<y?(0|Math.min(+Math.floor(y/4294967296),4294967295))>>>0:~~+Math.ceil((y-(~~y>>>0))/4294967296)>>>0:0)],u[r+88>>2]=_[0],u[r+92>>2]=_[1],u[r+96>>2]=0,_=[n.ino>>>0,(y=n.ino,1<=+Math.abs(y)?0<y?(0|Math.min(+Math.floor(y/4294967296),4294967295))>>>0:~~+Math.ceil((y-(~~y>>>0))/4294967296)>>>0:0)],u[r+104>>2]=_[0],u[r+108>>2]=_[1],0},doMsync:function(e,t,r,n,o){e=f.slice(e,e+r);k.msync(t,e,o,r,n)},varargs:void 0,get:function(){return T.varargs+=4,u[T.varargs-4>>2]},getStr:function(e){return ee(e)},getStreamFromFD:function(e){e=k.getStream(e);if(e)return e;throw new k.ErrnoError(8)}};function me(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var ge=void 0;function B(e){for(var t="",r=e;f[r];)t+=ge[f[r++]];return t}var A={},D={},ye={};function _e(e){var t;return void 0===e?"_unknown":48<=(t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0))&&t<=57?"_"+e:e}function we(e,t){return e=_e(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function ve(e,t){var r=we(t,function(e){this.name=t,this.message=e;e=new Error(e).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))});return r.prototype=Object.create(e.prototype),(r.prototype.constructor=r).prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var S=void 0;function C(e){throw new S(e)}var be=void 0;function Ee(e){throw new be(e)}function ke(n,t,o){function r(e){var t=o(e);t.length!==n.length&&Ee("Mismatched type converter count");for(var r=0;r<n.length;++r)F(n[r],t[r])}n.forEach(function(e){ye[e]=t});var a=new Array(t.length),i=[],s=0;t.forEach((e,t)=>{D.hasOwnProperty(e)?a[t]=D[e]:(i.push(e),A.hasOwnProperty(e)||(A[e]=[]),A[e].push(()=>{a[t]=D[e],++s===i.length&&r(a)}))}),0===i.length&&r(a)}function F(e,t,r){r=2<arguments.length&&void 0!==r?r:{};if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=t.name;if(e||C('type "'+n+'" must have a positive integer typeid pointer'),D.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;C("Cannot register type '"+n+"' twice")}D[e]=t,delete ye[e],A.hasOwnProperty(e)&&(r=A[e],delete A[e],r.forEach(e=>e()))}function Te(e){C(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Be=!1;function Ae(e){}function De(e){--e.count.value,0===e.count.value&&((e=e).smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr))}var Se={};var Ce=[];function Fe(){for(;Ce.length;){var e=Ce.pop();e.$$.deleteScheduled=!1,e.delete()}}var Ue=void 0;var Pe={};function xe(e,t){return t.ptrType&&t.ptr||Ee("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&Ee("Both smartPtrType and smartPtr must be specified"),t.count={value:1},Le(Object.create(e,{$$:{value:t}}))}function Le(e){return"undefined"==typeof FinalizationRegistry?(Le=e=>e,e):(Be=new FinalizationRegistry(e=>{De(e.$$)}),Ae=e=>Be.unregister(e),(Le=e=>{var t=e.$$;return t.smartPtr&&Be.register(e,{$$:t},e),e})(e))}function U(){}function Re(e,t,r){var n;void 0===e[t].overloadTable&&(n=e[t],e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||C("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n)}function Ie(e,t,r,n,o,a,i,s){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=o,this.getActualType=a,this.upcast=i,this.downcast=s,this.pureVirtualFunctions=[]}function Me(e,t,r){for(;t!==r;)t.upcast||C("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function $e(e,t){if(null===t)return this.isReference&&C("null is not a valid "+this.name),0;t.$$||C('Cannot pass "'+et(t)+'" as a '+this.name),t.$$.ptr||C("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return Me(t.$$.ptr,r,this.registeredClass)}function Ne(e,t){if(null===t)return this.isReference&&C("null is not a valid "+this.name),this.isSmartPointer?(n=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,n),n):0;t.$$||C('Cannot pass "'+et(t)+'" as a '+this.name),t.$$.ptr||C("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&C("Cannot convert argument of type "+(t.$$.smartPtrType||t.$$.ptrType).name+" to parameter type "+this.name);var r,n,o=t.$$.ptrType.registeredClass;if(n=Me(t.$$.ptr,o,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&C("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?n=t.$$.smartPtr:C("Cannot convert argument of type "+(t.$$.smartPtrType||t.$$.ptrType).name+" to parameter type "+this.name);break;case 1:n=t.$$.smartPtr;break;case 2:t.$$.smartPtrType===this?n=t.$$.smartPtr:(r=t.clone(),n=this.rawShare(n,Qe.toHandle(function(){r.delete()})),null!==e&&e.push(this.rawDestructor,n));break;default:C("Unsupporting sharing policy")}return n}function Ge(e,t){if(null===t)return this.isReference&&C("null is not a valid "+this.name),0;t.$$||C('Cannot pass "'+et(t)+'" as a '+this.name),t.$$.ptr||C("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&C("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return Me(t.$$.ptr,r,this.registeredClass)}function Oe(e){return this.fromWireType(u[e>>2])}function P(e,t,r,n,o,a,i,s,l,u,d){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=n,this.isSmartPointer=o,this.pointeeType=a,this.sharingPolicy=i,this.rawGetPointee=s,this.rawConstructor=l,this.rawShare=u,this.rawDestructor=d,o||void 0!==t.baseClass?this.toWireType=Ne:(this.toWireType=n?$e:Ge,this.destructorFunction=null)}var Ve=[];function ze(e){var t=Ve[e];return t||(e>=Ve.length&&(Ve.length=e+1),Ve[e]=t=Z.get(e)),t}function x(e,t){var o,a,i,r=(e=B(e)).includes("j")?(o=e,a=t,i=[],function(){return i.length=0,Object.assign(i,arguments),t=a,r=i,(e=o).includes("j")?(n=t,e=g["dynCall_"+e],r&&r.length?e.apply(null,[n].concat(r)):e.call(null,n)):ze(t).apply(null,r);var e,t,r,n}):ze(t);return"function"!=typeof r&&C("unknown function pointer with signature "+e+": "+t),r}var je=void 0;function We(e){var e=wt(e),t=B(e);return R(e),t}function He(e,t){var r=[],n={};throw t.forEach(function e(t){n[t]||D[t]||(ye[t]?ye[t].forEach(e):(r.push(t),n[t]=!0))}),new je(e+": "+r.map(We).join([", "]))}function qe(e,t){for(var r=[],n=0;n<e;n++)r.push(p[t+4*n>>2]);return r}function Xe(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function Ke(e,t){var r;if(e instanceof Function)return(r=we(e.name||"unknownFunctionName",function(){})).prototype=e.prototype,r=new r,(t=e.apply(r,t))instanceof Object?t:r;throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function")}function Ye(e,t,r,n,o){var a=t.length;a<2&&C("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var r=null!==t[1]&&null!==r,i=!1,s=1;s<t.length;++s)if(null!==t[s]&&void 0===t[s].destructorFunction){i=!0;break}for(var l="void"!==t[0].name,u="",d="",s=0;s<a-2;++s)u+=(0!==s?", ":"")+"arg"+s,d+=(0!==s?", ":"")+"arg"+s+"Wired";var c="return function "+_e(e)+"("+u+") {\nif (arguments.length !== "+(a-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(a-2)+" args!');\n}\n",f=(i&&(c+="var destructors = [];\n"),i?"destructors":"null"),p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],h=[C,n,o,Xe,t[0],t[1]];for(r&&(c+="var thisWired = classParam.toWireType("+f+", this);\n"),s=0;s<a-2;++s)c+="var arg"+s+"Wired = argType"+s+".toWireType("+f+", arg"+s+"); // "+t[s+2].name+"\n",p.push("argType"+s),h.push(t[s+2]);if(c+=(l?"var rv = ":"")+"invoker(fn"+(0<(d=r?"thisWired"+(0<d.length?", ":"")+d:d).length?", ":"")+d+");\n",i)c+="runDestructors(destructors);\n";else for(s=r?1:2;s<t.length;++s){var m=1===s?"thisWired":"arg"+(s-2)+"Wired";null!==t[s].destructorFunction&&(c+=m+"_dtor("+m+"); // "+t[s].name+"\n",p.push(m+"_dtor"),h.push(t[s].destructorFunction))}return l&&(c+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p.push(c+="}\n"),Ke(Function,p).apply(null,h)}var Je=[],L=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Ze(e){4<e&&0==--L[e].refcount&&(L[e]=void 0,Je.push(e))}var Qe={toValue:e=>(e||C("Cannot use deleted val. handle = "+e),L[e].value),toHandle:e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=Je.length?Je.pop():L.length;return L[t]={refcount:1,value:e},t}}};function et(e){var t;return null===e?"null":"object"==(t=typeof e)||"array"==t||"function"==t?e.toString():""+e}var tt="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function rt(e,t){for(var r,n=e>>1,o=n+t/2;!(o<=n)&&K[n];)++n;if(32<(r=n<<1)-e&&tt)return tt.decode(f.subarray(e,r));for(var a="",i=0;!(t/2<=i);++i){var s=l[e+2*i>>1];if(0==s)break;a+=String.fromCharCode(s)}return a}function nt(e,t,r){if((r=void 0===r?2147483647:r)<2)return 0;for(var n=t,o=(r-=2)<2*e.length?r/2:e.length,a=0;a<o;++a){var i=e.charCodeAt(a);l[t>>1]=i,t+=2}return l[t>>1]=0,t-n}function ot(e){return 2*e.length}function at(e,t){for(var r=0,n="";!(t/4<=r);){var o,a=u[e+4*r>>2];if(0==a)break;++r,65536<=a?(o=a-65536,n+=String.fromCharCode(55296|o>>10,56320|1023&o)):n+=String.fromCharCode(a)}return n}function it(e,t,r){if((r=void 0===r?2147483647:r)<4)return 0;for(var n=t,o=n+r-4,a=0;a<e.length;++a){var i=e.charCodeAt(a);if(55296<=i&&i<=57343&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++a)),u[t>>2]=i,(t+=4)+4>o)break}return u[t>>2]=0,t-n}function st(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);55296<=n&&n<=57343&&++r,t+=4}return t}var lt={},ut=[],dt=[],ct={};function ft(){if(!ft.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:V||"./this.program"};for(t in ct)void 0===ct[t]?delete e[t]:e[t]=ct[t];var t,r=[];for(t in e)r.push(t+"="+e[t]);ft.strings=r}return ft.strings}function pt(e,t,r,n){this.parent=e=e||this,this.mount=e.mount,this.mounted=null,this.id=k.nextInode++,this.name=t,this.mode=r,this.node_ops={},this.stream_ops={},this.rdev=n}Object.defineProperties(pt.prototype,{read:{get:function(){return 365==(365&this.mode)},set:function(e){e?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146==(146&this.mode)},set:function(e){e?this.mode|=146:this.mode&=-147}},isFolder:{get:function(){return k.isDir(this.mode)}},isDevice:{get:function(){return k.isChrdev(this.mode)}}}),k.FSNode=pt,k.staticInit();for(var ht=new Array(256),mt=0;mt<256;++mt)ht[mt]=String.fromCharCode(mt);ge=ht,S=g.BindingError=ve(Error,"BindingError"),be=g.InternalError=ve(Error,"InternalError"),U.prototype.isAliasOf=function(e){if(!(this instanceof U))return!1;if(!(e instanceof U))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return t===n&&r===o},U.prototype.clone=function(){var e;return this.$$.ptr||Te(this),this.$$.preservePointerOnDelete?(this.$$.count.value+=1,this):((e=Le(Object.create(Object.getPrototypeOf(this),{$$:{value:{count:(e=this.$$).count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}}}))).$$.count.value+=1,e.$$.deleteScheduled=!1,e)},U.prototype.delete=function(){this.$$.ptr||Te(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&C("Object already scheduled for deletion"),Ae(this),De(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},U.prototype.isDeleted=function(){return!this.$$.ptr},U.prototype.deleteLater=function(){return this.$$.ptr||Te(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&C("Object already scheduled for deletion"),Ce.push(this),1===Ce.length&&Ue&&Ue(Fe),this.$$.deleteScheduled=!0,this},g.getInheritedInstanceCount=function(){return Object.keys(Pe).length},g.getLiveInheritedInstances=function(){var e,t=[];for(e in Pe)Pe.hasOwnProperty(e)&&t.push(Pe[e]);return t},g.flushPendingDeletes=Fe,g.setDelayFunction=function(e){Ue=e,Ce.length&&Ue&&Ue(Fe)},P.prototype.getPointee=function(e){return e=this.rawGetPointee?this.rawGetPointee(e):e},P.prototype.destructor=function(e){this.rawDestructor&&this.rawDestructor(e)},P.prototype.argPackAdvance=8,P.prototype.readValueFromPointer=Oe,P.prototype.deleteObject=function(e){null!==e&&e.delete()},P.prototype.fromWireType=function(e){var t,r,n=this.getPointee(e);return n?(t=function(e,t){for(void 0===t&&C("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(t=this.registeredClass,t=n),void 0!==(t=Pe[t])?0===t.$$.count.value?(t.$$.ptr=n,t.$$.smartPtr=e,t.clone()):(t=t.clone(),this.destructor(e),t):(t=this.registeredClass.getActualType(n),!(t=Se[t])||(t=this.isConst?t.constPointerType:t.pointerType,null===(r=function e(t,r,n){return r===n?t:void 0===n.baseClass||null===(t=e(t,r,n.baseClass))?null:n.downcast(t)}(n,this.registeredClass,t.registeredClass)))?o.call(this):this.isSmartPointer?xe(t.registeredClass.instancePrototype,{ptrType:t,ptr:r,smartPtrType:this,smartPtr:e}):xe(t.registeredClass.instancePrototype,{ptrType:t,ptr:r}))):(this.destructor(e),null);function o(){return this.isSmartPointer?xe(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:n,smartPtrType:this,smartPtr:e}):xe(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}},je=g.UnboundTypeError=ve(Error,"UnboundTypeError"),g.count_emval_handles=function(){for(var e=0,t=5;t<L.length;++t)void 0!==L[t]&&++e;return e},g.get_first_emval=function(){for(var e=5;e<L.length;++e)if(void 0!==L[e])return L[e];return null};var gt={s:function(e){return yt(e+24)+24},r:function(e,t,r){throw new fe(e).init(t,r),e},C:function(e,t,r){T.varargs=r;try{var n=T.getStreamFromFD(e);switch(t){case 0:return(o=T.get())<0?-28:k.createStream(n,o).fd;case 1:case 2:case 6:case 7:return 0;case 3:return n.flags;case 4:var o=T.get();return n.flags|=o,0;case 5:return o=T.get(),l[o+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return u[_t()>>2]=28,-1}}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return-e.errno;throw e}},v:function(e,t,r,n){T.varargs=n;try{t=T.getStr(t),t=T.calculateAt(e,t);var o=n?T.get():0;return k.open(t,r,o).fd}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return-e.errno;throw e}},u:function(e,t,r,n,o){},E:function(e,r,n,o,a){var i=me(n);F(e,{name:r=B(r),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?o:a},argPackAdvance:8,readValueFromPointer:function(e){var t;if(1===n)t=c;else if(2===n)t=l;else{if(4!==n)throw new TypeError("Unknown boolean type size: "+r);t=u}return this.fromWireType(t[e>>i])},destructorFunction:null})},m:function(l,e,t,u,r,d,n,c,o,f,p,a,h){p=B(p),d=x(r,d),c=c&&x(n,c),f=f&&x(o,f),h=x(a,h);var i,m=_e(p);r=m,n=function(){He("Cannot construct "+p+" due to unbound types",[u])},g.hasOwnProperty(r)?(C("Cannot register public name '"+r+"' twice"),Re(g,r,r),g.hasOwnProperty(i)&&C("Cannot register multiple overloads of a function with the same number of arguments ("+i+")!"),g[r].overloadTable[i]=n):g[r]=n,ke([l,e,t],u?[u]:[],function(e){e=e[0],e=u?(i=e.registeredClass).instancePrototype:U.prototype;var t,r,n=we(m,function(){if(Object.getPrototypeOf(this)!==o)throw new S("Use 'new' to construct "+p);if(void 0===a.constructor_body)throw new S(p+" has no accessible constructor");var e=a.constructor_body[arguments.length];if(void 0===e)throw new S("Tried to invoke ctor of "+p+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(a.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)}),o=Object.create(e,{constructor:{value:n}}),a=(n.prototype=o,new Ie(p,n,o,h,i,d,c,f)),e=new P(p,a,!0,!1,!1),i=new P(p+"*",a,!1,!1,!1),s=new P(p+" const*",a,!1,!0,!1);return Se[l]={pointerType:i,constPointerType:s},t=m,n=n,g.hasOwnProperty(t)||Ee("Replacing nonexistant public symbol"),g[t].overloadTable,g[t]=n,g[t].argCount=r,[e,i,s]})},l:function(e,n,t,r,o,a){q(0<n);var i=qe(n,t);o=x(r,o),ke([],[e],function(t){var r="constructor "+(t=t[0]).name;if(void 0===t.registeredClass.constructor_body&&(t.registeredClass.constructor_body=[]),void 0!==t.registeredClass.constructor_body[n-1])throw new S("Cannot register multiple constructors with identical number of parameters ("+(n-1)+") for class '"+t.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return t.registeredClass.constructor_body[n-1]=()=>{He("Cannot construct "+t.name+" due to unbound types",i)},ke([],i,function(e){return e.splice(1,0,null),t.registeredClass.constructor_body[n-1]=Ye(r,e,null,o,a),[]}),[]})},d:function(e,a,i,t,r,s,l,u){var d=qe(i,t);a=B(a),s=x(r,s),ke([],[e],function(t){var r=(t=t[0]).name+"."+a;function e(){He("Cannot call "+r+" due to unbound types",d)}a.startsWith("@@")&&(a=Symbol[a.substring(2)]),u&&t.registeredClass.pureVirtualFunctions.push(a);var n=t.registeredClass.instancePrototype,o=n[a];return void 0===o||void 0===o.overloadTable&&o.className!==t.name&&o.argCount===i-2?(e.argCount=i-2,e.className=t.name,n[a]=e):(Re(n,a,r),n[a].overloadTable[i-2]=e),ke([],d,function(e){e=Ye(r,e,t,s,l);return void 0===n[a].overloadTable?(e.argCount=i-2,n[a]=e):n[a].overloadTable[i-2]=e,[]}),[]})},D:function(e,t){F(e,{name:t=B(t),fromWireType:function(e){var t=Qe.toValue(e);return Ze(e),t},toWireType:function(e,t){return Qe.toHandle(t)},argPackAdvance:8,readValueFromPointer:Oe,destructorFunction:null})},o:function(e,t,r){r=me(r);F(e,{name:t=B(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:function(e,t){switch(t){case 2:return function(e){return this.fromWireType(Y[e>>2])};case 3:return function(e){return this.fromWireType(J[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}(t,r),destructorFunction:null})},c:function(e,t,r,n,o){t=B(t);var a,i=me(r),s=e=>e,r=(0===n&&(a=32-8*r,s=e=>e<<a>>>a),t.includes("unsigned"));F(e,{name:t,fromWireType:s,toWireType:r?function(e,t){return this.name,t>>>0}:function(e,t){return this.name,t},argPackAdvance:8,readValueFromPointer:function(e,t,r){switch(t){case 0:return r?function(e){return c[e]}:function(e){return f[e]};case 1:return r?function(e){return l[e>>1]}:function(e){return K[e>>1]};case 2:return r?function(e){return u[e>>2]}:function(e){return p[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}(t,i,0!==n),destructorFunction:null})},b:function(e,t,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function o(e){var t=p,r=t[e>>=2],t=t[e+1];return new n(X,t,r)}F(e,{name:r=B(r),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},p:function(e,t){var u="std::string"===(t=B(t));F(e,{name:t,fromWireType:function(e){var t,r=p[e>>2],n=e+4;if(u)for(var o=n,a=0;a<=r;++a){var i,s=n+a;a!=r&&0!=f[s]||(i=ee(o,s-o),void 0===t?t=i:t=t+String.fromCharCode(0)+i,o=s+1)}else{for(var l=new Array(r),a=0;a<r;++a)l[a]=String.fromCharCode(f[n+a]);t=l.join("")}return R(e),t},toWireType:function(e,t){var r,n="string"==typeof(t=t instanceof ArrayBuffer?new Uint8Array(t):t),o=(n||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||C("Cannot pass non-string to std::string"),r=u&&n?re(t):t.length,yt(4+r+1)),a=o+4;if(p[o>>2]=r,u&&n)te(t,f,a,r+1);else if(n)for(var i=0;i<r;++i){var s=t.charCodeAt(i);255<s&&(R(a),C("String has UTF-16 code units that do not fit in 8 bits")),f[a+i]=s}else for(i=0;i<r;++i)f[a+i]=t[i];return null!==e&&e.push(R,o),o},argPackAdvance:8,readValueFromPointer:Oe,destructorFunction:function(e){R(e)}})},k:function(e,l,o){var u,a,d,i,c;o=B(o),2===l?(u=rt,a=nt,i=ot,d=()=>K,c=1):4===l&&(u=at,a=it,i=st,d=()=>p,c=2),F(e,{name:o,fromWireType:function(e){for(var t,r=p[e>>2],n=d(),o=e+4,a=0;a<=r;++a){var i,s=e+4+a*l;a!=r&&0!=n[s>>c]||(i=u(o,s-o),void 0===t?t=i:t=t+String.fromCharCode(0)+i,o=s+l)}return R(e),t},toWireType:function(e,t){"string"!=typeof t&&C("Cannot pass non-string to C++ string type "+o);var r=i(t),n=yt(4+r+l);return p[n>>2]=r>>c,a(t,n+4,r+l),null!==e&&e.push(R,n),n},argPackAdvance:8,readValueFromPointer:Oe,destructorFunction:function(e){R(e)}})},q:function(e,t){F(e,{isVoid:!0,name:t=B(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},j:function(){return Date.now()},g:function(e,t,r,n){(e=ut[e])(t=Qe.toValue(t),r=void 0===(t=lt[e=r])?B(e):t,null,n)},e:Ze,f:function(e,t){var r=function(e,t){for(var r,n,o=new Array(e),a=0;a<e;++a)o[a]=(r=p[t+4*a>>2],void 0===(n=D[r])&&C("parameter "+a+" has unknown type "+We(r)),n);return o}(e,t),t=r[0],n=t.name+"_$"+r.slice(1).map(function(e){return e.name}).join("_")+"$",o=dt[n];if(void 0===o){for(var a=["retType"],i=[t],s="",l=0;l<e-1;++l)s+=(0!==l?", ":"")+"arg"+l,a.push("argType"+l),i.push(r[1+l]);for(var u="return function "+_e("methodCaller_"+n)+"(handle, name, destructors, args) {\n",d=0,l=0;l<e-1;++l)u+="    var arg"+l+" = argType"+l+".readValueFromPointer(args"+(d?"+"+d:"")+");\n",d+=r[l+1].argPackAdvance;for(u+="    var rv = handle[name]("+s+");\n",l=0;l<e-1;++l)r[l+1].deleteObject&&(u+="    argType"+l+".deleteObject(arg"+l+");\n");t.isVoid||(u+="    return retType.toWireType(destructors, rv);\n"),a.push(u+="};\n");var t=Ke(Function,a).apply(null,i),c=ut.length;ut.push(t),dt[n]=o=c}return o},a:function(){w("")},z:function(e,t,r){f.copyWithin(e,t,t+r)},i:function(e){f.length,w("OOM")},x:function(i,s){var l=0;return ft().forEach(function(e,t){for(var r=s+l,n=(p[i+4*t>>2]=r,e),o=r,a=0;a<n.length;++a)c[o++>>0]=n.charCodeAt(a);c[o>>0]=0,l+=e.length+1}),0},y:function(e,t){var r=ft(),n=(p[e>>2]=r.length,0);return r.forEach(function(e){n+=e.length+1}),p[t>>2]=n,0},n:function(e){try{var t=T.getStreamFromFD(e);return k.close(t),0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},w:function(e,t){try{var r=T.getStreamFromFD(e),n=r.tty?2:k.isDir(r.mode)?3:k.isLink(r.mode)?7:4;return c[t>>0]=n,0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},B:function(e,t,r,n){try{var o=function(e,t,r){for(var n=0,o=0;o<r;o++){var a=p[t>>2],i=p[t+4>>2],a=(t+=8,k.read(e,c,a,i,void 0));if(a<0)return-1;if(n+=a,a<i)break}return n}(T.getStreamFromFD(e),t,r);return u[n>>2]=o,0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},t:function(e,t,r,n,o){try{var a,i=r+2097152>>>0<4194305-!!t?(t>>>0)+4294967296*r:NaN;return isNaN(i)?61:(a=T.getStreamFromFD(e),k.llseek(a,i,n),_=[a.position>>>0,(y=a.position,1<=+Math.abs(y)?0<y?(0|Math.min(+Math.floor(y/4294967296),4294967295))>>>0:~~+Math.ceil((y-(~~y>>>0))/4294967296)>>>0:0)],u[o>>2]=_[0],u[o+4>>2]=_[1],a.getdents&&0===i&&0===n&&(a.getdents=null),0)}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},A:function(e,t,r,n){try{var o=function(e,t,r){for(var n=0,o=0;o<r;o++){var a=p[t>>2],i=p[t+4>>2],a=(t+=8,k.write(e,c,a,i,void 0));if(a<0)return-1;n+=a}return n}(T.getStreamFromFD(e),t,r);return p[n>>2]=o,0}catch(e){if(void 0!==k&&e instanceof k.ErrnoError)return e.errno;throw e}},h:function(e){}},R=(!function(){var t={a:gt};function r(e,t){var e=e.exports;g.asm=e,e=g.asm.F.buffer,X=e,g.HEAP8=c=new Int8Array(e),g.HEAP16=l=new Int16Array(e),g.HEAP32=u=new Int32Array(e),g.HEAPU8=f=new Uint8Array(e),g.HEAPU16=K=new Uint16Array(e),g.HEAPU32=p=new Uint32Array(e),g.HEAPF32=Y=new Float32Array(e),g.HEAPF64=J=new Float64Array(e),Z=g.asm.J,e=g.asm.G,oe.unshift(e),se()}function n(e){r(e.instance)}function o(e){return function(){if(!a&&(z||d)){if("function"==typeof fetch&&!ue(h))return fetch(h,{credentials:"same-origin"}).then(function(e){if(e.ok)return e.arrayBuffer();throw"failed to load wasm binary file at '"+h+"'"}).catch(function(){return de(h)});if(m)return new Promise(function(t,e){m(h,function(e){t(new Uint8Array(e))},e)})}return Promise.resolve().then(function(){return de(h)})}().then(function(e){return WebAssembly.instantiate(e,t)}).then(function(e){return e}).then(e,function(e){s("failed to asynchronously prepare wasm: "+e),w(e)})}if(ie(),g.instantiateWasm)try{return g.instantiateWasm(t,r)}catch(t){return s("Module.instantiateWasm callback failed with error: "+t)}a||"function"!=typeof WebAssembly.instantiateStreaming||le(h)||ue(h)||j||"function"!=typeof fetch?o(n):fetch(h,{credentials:"same-origin"}).then(function(e){return WebAssembly.instantiateStreaming(e,t).then(n,function(e){return s("wasm streaming compile failed: "+e),s("falling back to ArrayBuffer instantiation"),o(n)})})}(),g.___wasm_call_ctors=function(){return(g.___wasm_call_ctors=g.asm.G).apply(null,arguments)},g._free=function(){return(R=g._free=g.asm.H).apply(null,arguments)}),yt=g._malloc=function(){return(yt=g._malloc=g.asm.I).apply(null,arguments)},_t=g.___errno_location=function(){return(_t=g.___errno_location=g.asm.K).apply(null,arguments)},wt=g.___getTypeName=function(){return(wt=g.___getTypeName=g.asm.L).apply(null,arguments)};g.___embind_register_native_and_builtin_types=function(){return(g.___embind_register_native_and_builtin_types=g.asm.M).apply(null,arguments)};var vt,bt=g._emscripten_builtin_memalign=function(){return(bt=g._emscripten_builtin_memalign=g.asm.N).apply(null,arguments)},Et=g.___cxa_is_pointer_type=function(){return(Et=g.___cxa_is_pointer_type=g.asm.O).apply(null,arguments)};function kt(){function e(){if(!vt&&(vt=!0,g.calledRun=!0,!H)){if(g.noFSInit||k.init.initialized||k.init(),k.ignorePermissions=!1,ce(oe),g.onRuntimeInitialized&&g.onRuntimeInitialized(),g.postRun)for("function"==typeof g.postRun&&(g.postRun=[g.postRun]);g.postRun.length;)e=g.postRun.shift(),ae.unshift(e);var e;ce(ae)}}0<r||(function(){if(g.preRun)for("function"==typeof g.preRun&&(g.preRun=[g.preRun]);g.preRun.length;)e=g.preRun.shift(),ne.unshift(e);var e;ce(ne)}(),0<r)||(g.setStatus?(g.setStatus("Running..."),setTimeout(function(){setTimeout(function(){g.setStatus("")},1),e()},1)):e())}if(g.dynCall_viiijj=function(){return(g.dynCall_viiijj=g.asm.P).apply(null,arguments)},g.dynCall_jij=function(){return(g.dynCall_jij=g.asm.Q).apply(null,arguments)},g.dynCall_jii=function(){return(g.dynCall_jii=g.asm.R).apply(null,arguments)},g.dynCall_jiji=function(){return(g.dynCall_jiji=g.asm.S).apply(null,arguments)},g._ff_h264_cabac_tables=215420,n=function e(){vt||kt(),vt||(n=e)},g.preInit)for("function"==typeof g.preInit&&(g.preInit=[g.preInit]);0<g.preInit.length;)g.preInit.pop()();kt(),I.exports=g}),k=1e-6,o="undefined"!=typeof Float32Array?Float32Array:Array;function T(){var e=new o(16);return o!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0),e[0]=1,e[5]=1,e[10]=1,e[15]=1,e}function A(e){e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=1,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=1,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1}Math.hypot||(Math.hypot=function(){for(var e=0,t=arguments.length;t--;)e+=arguments[t]*arguments[t];return Math.sqrt(e)});var D=function(e,t,r,n,o,a,i){var s=1/(t-r),l=1/(n-o),u=1/(a-i);return e[0]=-2*s,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=-2*l,e[6]=0,e[7]=0,e[8]=0,e[9]=0,e[10]=2*u,e[11]=0,e[12]=(t+r)*s,e[13]=(o+n)*l,e[14]=(i+a)*u,e[15]=1,e};function B(e,t,r){var n=new o(3);return n[0]=e,n[1]=t,n[2]=r,n}e=new o(3),o!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0);var J=(y,e)=>{e&&y.pixelStorei(y.UNPACK_ALIGNMENT,1);e=a(y.VERTEX_SHADER,"\n            attribute vec4 aVertexPosition;\n            attribute vec2 aTexturePosition;\n            uniform mat4 uModelMatrix;\n            uniform mat4 uViewMatrix;\n            uniform mat4 uProjectionMatrix;\n            varying lowp vec2 vTexturePosition;\n            void main(void) {\n              gl_Position = uProjectionMatrix * uViewMatrix * uModelMatrix * aVertexPosition;\n              vTexturePosition = aTexturePosition;\n            }\n        "),t=a(y.FRAGMENT_SHADER,"\n            precision highp float;\n            varying highp vec2 vTexturePosition;\n            uniform int isyuv;\n            uniform sampler2D rgbaTexture;\n            uniform sampler2D yTexture;\n            uniform sampler2D uTexture;\n            uniform sampler2D vTexture;\n\n            const mat4 YUV2RGB = mat4( 1.1643828125, 0, 1.59602734375, -.87078515625,\n                                       1.1643828125, -.39176171875, -.81296875, .52959375,\n                                       1.1643828125, 2.017234375, 0, -1.081390625,\n                                       0, 0, 0, 1);\n\n\n            void main(void) {\n\n                if (isyuv>0) {\n\n                    highp float y = texture2D(yTexture,  vTexturePosition).r;\n                    highp float u = texture2D(uTexture,  vTexturePosition).r;\n                    highp float v = texture2D(vTexture,  vTexturePosition).r;\n                    gl_FragColor = vec4(y, u, v, 1) * YUV2RGB;\n\n                } else {\n                    gl_FragColor =  texture2D(rgbaTexture, vTexturePosition);\n                }\n            }\n        "),r=y.createProgram(),y.attachShader(r,e),y.attachShader(r,t),y.linkProgram(r);var t,r,e=y.getProgramParameter(r,y.LINK_STATUS)?r:(console.log("Unable to initialize the shader program: "+y.getProgramInfoLog(r)),null);let _={program:e,attribLocations:{vertexPosition:y.getAttribLocation(e,"aVertexPosition"),texturePosition:y.getAttribLocation(e,"aTexturePosition")},uniformLocations:{projectionMatrix:y.getUniformLocation(e,"uProjectionMatrix"),modelMatrix:y.getUniformLocation(e,"uModelMatrix"),viewMatrix:y.getUniformLocation(e,"uViewMatrix"),rgbatexture:y.getUniformLocation(e,"rgbaTexture"),ytexture:y.getUniformLocation(e,"yTexture"),utexture:y.getUniformLocation(e,"uTexture"),vtexture:y.getUniformLocation(e,"vTexture"),isyuv:y.getUniformLocation(e,"isyuv")}},w=(t=y.createBuffer(),y.bindBuffer(y.ARRAY_BUFFER,t),y.bufferData(y.ARRAY_BUFFER,new Float32Array([-1,-1,-1,1,-1,-1,1,1,-1,-1,1,-1]),y.STATIC_DRAW),r=(r=[]).concat([0,1],[1,1],[1,0],[0,0]),e=y.createBuffer(),y.bindBuffer(y.ARRAY_BUFFER,e),y.bufferData(y.ARRAY_BUFFER,new Float32Array(r),y.STATIC_DRAW),r=y.createBuffer(),y.bindBuffer(y.ELEMENT_ARRAY_BUFFER,r),y.bufferData(y.ELEMENT_ARRAY_BUFFER,new Uint16Array([0,1,2,0,2,3]),y.STATIC_DRAW),{position:t,texPosition:e,indices:r}),n=o(),v=o(),b=o(),E=o();function o(){var e=y.createTexture();return y.bindTexture(y.TEXTURE_2D,e),y.texParameteri(y.TEXTURE_2D,y.TEXTURE_MAG_FILTER,y.LINEAR),y.texParameteri(y.TEXTURE_2D,y.TEXTURE_MIN_FILTER,y.LINEAR),y.texParameteri(y.TEXTURE_2D,y.TEXTURE_WRAP_S,y.CLAMP_TO_EDGE),y.texParameteri(y.TEXTURE_2D,y.TEXTURE_WRAP_T,y.CLAMP_TO_EDGE),e}function a(e,t){e=y.createShader(e);return y.shaderSource(e,t),y.compileShader(e),y.getShaderParameter(e,y.COMPILE_STATUS)?e:(console.log("An error occurred compiling the shaders: "+y.getShaderInfoLog(e)),y.deleteShader(e),null)}function i(e,t){y.viewport(0,0,e,t),y.clearColor(0,0,0,0),y.clearDepth(1),y.enable(y.DEPTH_TEST),y.depthFunc(y.LEQUAL),y.clear(y.COLOR_BUFFER_BIT|y.DEPTH_BUFFER_BIT);const r=T();D(r,-1,1,-1,1,.1,100);var n,o,a,i,s,l,u,d,c,f,p,h,m,g,e=T(),t=(A(e),T());n=t,o=B(0,0,0),a=B(0,0,-1),i=B(0,1,0),c=o[0],f=o[1],o=o[2],p=i[0],h=i[1],i=i[2],m=a[0],g=a[1],a=a[2],Math.abs(c-m)<k&&Math.abs(f-g)<k&&Math.abs(o-a)<k?A(n):(m=c-m,g=f-g,a=o-a,s=h*(a*=d=1/Math.hypot(m,g,a))-i*(g*=d),i=i*(m*=d)-p*a,p=p*g-h*m,(d=Math.hypot(s,i,p))?(s*=d=1/d,i*=d,p*=d):p=i=s=0,h=g*p-a*i,l=a*s-m*p,u=m*i-g*s,(d=Math.hypot(h,l,u))?(h*=d=1/d,l*=d,u*=d):u=l=h=0,n[0]=s,n[1]=h,n[2]=m,n[3]=0,n[4]=i,n[5]=l,n[6]=g,n[7]=0,n[8]=p,n[9]=u,n[10]=a,n[11]=0,n[12]=-(s*c+i*f+p*o),n[13]=-(h*c+l*f+u*o),n[14]=-(m*c+g*f+a*o),n[15]=1);{const e=3,t=y.FLOAT,r=!1,v=0,b=0;y.bindBuffer(y.ARRAY_BUFFER,w.position),y.vertexAttribPointer(_.attribLocations.vertexPosition,3,t,!1,0,0),y.enableVertexAttribArray(_.attribLocations.vertexPosition)}{const e=2,t=y.FLOAT,r=!1,v=0,b=0;y.bindBuffer(y.ARRAY_BUFFER,w.texPosition),y.vertexAttribPointer(_.attribLocations.texturePosition,2,t,!1,0,0),y.enableVertexAttribArray(_.attribLocations.texturePosition)}y.activeTexture(y.TEXTURE0+3),y.bindTexture(y.TEXTURE_2D,v),y.activeTexture(y.TEXTURE0+4),y.bindTexture(y.TEXTURE_2D,b),y.activeTexture(y.TEXTURE0+5),y.bindTexture(y.TEXTURE_2D,E),y.bindBuffer(y.ELEMENT_ARRAY_BUFFER,w.indices),y.useProgram(_.program),y.uniformMatrix4fv(_.uniformLocations.projectionMatrix,!1,r),y.uniformMatrix4fv(_.uniformLocations.modelMatrix,!1,e),y.uniformMatrix4fv(_.uniformLocations.viewMatrix,!1,t),y.uniform1i(_.uniformLocations.rgbatexture,2),y.uniform1i(_.uniformLocations.ytexture,3),y.uniform1i(_.uniformLocations.utexture,4),y.uniform1i(_.uniformLocations.vtexture,5),y.uniform1i(_.uniformLocations.isyuv,1);{const e=6,t=y.UNSIGNED_SHORT,_=0;y.drawElements(y.TRIANGLES,6,t,0)}}return{render:function(e,t,r,n,o){y.activeTexture(y.TEXTURE0),y.bindTexture(y.TEXTURE_2D,v),y.texImage2D(y.TEXTURE_2D,0,y.LUMINANCE,e,t,0,y.LUMINANCE,y.UNSIGNED_BYTE,r),y.activeTexture(y.TEXTURE1),y.bindTexture(y.TEXTURE_2D,b),y.texImage2D(y.TEXTURE_2D,0,y.LUMINANCE,e/2,t/2,0,y.LUMINANCE,y.UNSIGNED_BYTE,n),y.activeTexture(y.TEXTURE2),y.bindTexture(y.TEXTURE_2D,E),y.texImage2D(y.TEXTURE_2D,0,y.LUMINANCE,e/2,t/2,0,y.LUMINANCE,y.UNSIGNED_BYTE,o),i(e,t)},renderYUV:function(e,t,r){var n=r.slice(0,e*t),o=r.slice(e*t,e*t*5/4),r=r.slice(e*t*5/4,e*t*3/2);y.activeTexture(y.TEXTURE0),y.bindTexture(y.TEXTURE_2D,v),y.texImage2D(y.TEXTURE_2D,0,y.LUMINANCE,e,t,0,y.LUMINANCE,y.UNSIGNED_BYTE,n),y.activeTexture(y.TEXTURE1),y.bindTexture(y.TEXTURE_2D,b),y.texImage2D(y.TEXTURE_2D,0,y.LUMINANCE,e/2,t/2,0,y.LUMINANCE,y.UNSIGNED_BYTE,o),y.activeTexture(y.TEXTURE2),y.bindTexture(y.TEXTURE_2D,E),y.texImage2D(y.TEXTURE_2D,0,y.LUMINANCE,e/2,t/2,0,y.LUMINANCE,y.UNSIGNED_BYTE,r),i(e,t)},destroy:function(){y.deleteProgram(_.program),y.deleteBuffer(w.position),y.deleteBuffer(w.texPosition),y.deleteBuffer(w.indices),y.deleteTexture(n),y.deleteTexture(v),y.deleteTexture(b),y.deleteTexture(E),_=null,w=null,n=null,v=null,b=null,E=null}}};const Z="fetch",x="player",L="mp4",w="debug",v="warn",Q={playType:x,container:"",videoBuffer:1e3,videoBufferDelay:1e3,networkDelay:1e4,isResize:!0,isFullResize:!1,isFlv:!1,isHls:!1,isWebrtc:!1,isWebrtcForZLM:!1,isNakedFlow:!1,debug:!1,debugLevel:v,debugUuid:"",isMulti:!1,hotKey:!1,loadingTimeout:10,heartTimeout:10,timeout:10,pageVisibilityHiddenTimeout:300,loadingTimeoutReplay:!0,heartTimeoutReplay:!0,loadingTimeoutReplayTimes:3,heartTimeoutReplayTimes:3,heartTimeoutReplayUseLastFrameShow:!1,replayUseLastFrameShow:!1,supportDblclickFullscreen:!1,showBandwidth:!1,showPerformance:!1,mseCorrectTimeDuration:20,keepScreenOn:!0,isNotMute:!1,hasAudio:!0,hasVideo:!0,operateBtns:{fullscreen:!1,screenshot:!1,play:!1,audio:!1,record:!1,ptz:!1,quality:!1,zoom:!1,close:!1,scale:!1,performance:!1,aiFace:!1,aiObject:!1,fullscreenFn:null,fullscreenExitFn:null,screenshotFn:null,playFn:null,pauseFn:null,recordFn:null,recordStopFn:null},extendOperateBtns:[],contextmenuBtns:[],watermarkConfig:{},controlAutoHide:!1,hasControl:!1,loadingIcon:!0,loadingText:"",background:"",backgroundLoadingShow:!1,loadingBackground:"",decoder:"decoder-pro.js",decoderWASM:"",url:"",rotate:0,mirrorRotate:"none",playbackConfig:{playList:[],fps:"",showControl:!0,showRateBtn:!1,rateConfig:[],isCacheBeforeDecodeForFpsRender:!1,uiUsePlaybackPause:!1,isPlaybackPauseClearCache:!0,isUseFpsRender:!1,isUseLocalCalculateTime:!1,localOneFrameTimestamp:40,supportWheel:!1},qualityConfig:[],defaultStreamQuality:"",scaleConfig:["拉伸","缩放","正常"],forceNoOffscreen:!0,hiddenAutoPause:!1,protocol:2,demuxType:"flv",useWasm:!1,useWCS:!1,useSIMD:!0,wcsUseVideoRender:!0,wasmUseVideoRender:!0,mseUseCanvasRender:!1,hlsUseCanvasRender:!1,useMSE:!1,useOffscreen:!1,useWebGPU:!1,mseDecodeErrorReplay:!0,wcsDecodeErrorReplay:!0,wasmDecodeErrorReplay:!0,autoWasm:!0,webglAlignmentErrorReplay:!0,webglContextLostErrorReplay:!0,openWebglAlignment:!1,syncAudioAndVideo:!1,playbackDelayTime:1e3,playbackFps:25,playbackForwardMaxRateDecodeIFrame:4,playbackCurrentTimeMove:!0,useVideoRender:!0,useCanvasRender:!1,networkDelayTimeoutReplay:!1,recordType:L,checkFirstIFrame:!0,nakedFlowFps:25,audioEngine:null,isShowRecordingUI:!0,isShowZoomingUI:!0,useFaceDetector:!1,useObjectDetector:!1,ptzClickType:"click",ptzStopEmitDelay:.3,ptzZoomShow:!1,ptzApertureShow:!1,ptzFocusShow:!1,ptzMoreArrow:!1,weiXinInAndroidAudioBufferSize:4800,isCrypto:!1,cryptoKey:"",cryptoIV:"",cryptoKeyUrl:"",autoResize:!1,useWebFullScreen:!1,ptsMaxDiff:3600,aiFaceDetectWidth:192,aiObjectDetectWidth:192,videoRenderSupportScale:!0,mediaSourceTsIsMaxDiffReplay:!0,controlHtml:""},ee="initVideo",R="render",te="playAudio",re="videoCode",ne="videoCodec",I="workerFetch",M="streamEnd",oe="streamSuccess",$="fetchError",ae="H264(AVC)",ie="H265(HEVC)",N=10,G=7,O=8,b=5,E=6,V=32,z=33,j=34,W="AbortError",se=0;function le(e){return e[0]>>4===N&&e[1]===se}function ue(){return(new Date).getTime()}function de(){return(performance&&"function"==typeof performance.now?performance:Date).now()}function H(e){return null==e}function ce(r,n){let o=(r=n?r.filter(e=>e.type===n):r)[0],a=null,e=1;var t;if(0<r.length&&(t=r[1])&&1e5<t.ts-o.ts&&(o=t,e=2),o)for(let t=e;t<r.length;t++){let e=r[t];!(e=n&&e.type!==n?null:e)||1e3<=e.ts-o.ts&&r[t-1].ts-o.ts<1e3&&(a=t+1)}return a}function fe(e){return!0===e||"true"===e}y(function(e){var i,t,s,r,n;i="undefined"!=typeof window&&void 0!==window.document?window.document:{},t=e.exports,s=function(){for(var e,t=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],r=0,n=t.length,o={};r<n;r++)if((e=t[r])&&e[1]in i){for(r=0;r<e.length;r++)o[t[0][r]]=e[r];return o}return!1}(),r={change:s.fullscreenchange,error:s.fullscreenerror},n={request:function(o,a){return new Promise(function(e,t){var r=function(){this.off("change",r),e()}.bind(this),n=(this.on("change",r),(o=o||i.documentElement)[s.requestFullscreen](a));n instanceof Promise&&n.then(r).catch(t)}.bind(this))},exit:function(){return new Promise(function(e,t){var r,n;this.isFullscreen?(r=function(){this.off("change",r),e()}.bind(this),this.on("change",r),(n=i[s.exitFullscreen]())instanceof Promise&&n.then(r).catch(t)):e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,t){e=r[e];e&&i.addEventListener(e,t,!1)},off:function(e,t){e=r[e];e&&i.removeEventListener(e,t,!1)},raw:s},s?(Object.defineProperties(n,{isFullscreen:{get:function(){return Boolean(i[s.fullscreenElement])}},element:{enumerable:!0,get:function(){return i[s.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(i[s.fullscreenEnabled])}}}),t?e.exports=n:window.screenfull=n):t?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}).isEnabled;try{if("object"==typeof WebAssembly&&"function"==typeof WebAssembly.instantiate){var S=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(S instanceof WebAssembly.Module)new WebAssembly.Instance(S)instanceof WebAssembly.Instance}}catch(e){}const C=[[Uint8Array,Int8Array],[Uint16Array,Int16Array],[Uint32Array,Int32Array,Float32Array],[Float64Array]],F=Symbol(32),U=Symbol(16),P=Symbol(8),pe=new Map;C.forEach((e,t)=>e.forEach(e=>pe.set(e,t)));class q{constructor(e){this.g=e,this.consumed=0,e&&(this.need=e.next().value)}fillFromReader(r){return e=this,l=function*(){var{done:e,value:t}=yield r.read();return e?void this.close():(this.write(t),this.fillFromReader(r))},new(s=(s=i=void 0)||Promise)(function(r,t){function n(e){try{a(l.next(e))}catch(e){t(e)}}function o(e){try{a(l.throw(e))}catch(e){t(e)}}function a(e){var t;e.done?r(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(n,o)}a((l=l.apply(e,i||[])).next())});var e,i,s,l}consume(){this.buffer&&this.consumed&&(this.buffer.copyWithin(0,this.consumed),this.buffer=this.buffer.subarray(0,this.buffer.length-this.consumed),this.consumed=0)}demand(e,t){return t&&this.consume(),this.need=e,this.flush()}read(r){return new Promise((t,e)=>{if(this.resolve)return e("last read not complete yet");this.resolve=e=>{delete this.resolve,delete this.need,t(e)},this.demand(r,!0)})}readU32(){return this.read(F)}readU16(){return this.read(U)}readU8(){return this.read(P)}close(){this.g&&this.g.return()}flush(){if(this.buffer&&this.need){let e=null;const n=this.buffer.subarray(this.consumed);let t=0;var r=e=>n.length<(t=e);if("number"==typeof this.need){if(r(this.need))return;e=n.subarray(0,t)}else if(this.need instanceof ArrayBuffer){if(r(this.need.byteLength))return;new Uint8Array(this.need).set(n.subarray(0,t)),e=this.need}else if(this.need===F){if(r(4))return;e=n[0]<<24|n[1]<<16|n[2]<<8|n[3]}else if(this.need===U){if(r(2))return;e=n[0]<<8|n[1]}else if(this.need===P){if(r(1))return;e=n[0]}else if(pe.has(this.need.constructor)){if(r(this.need.length<<pe.get(this.need.constructor)))return;new Uint8Array(this.need.buffer,this.need.byteOffset).set(n.subarray(0,t)),e=this.need}else if(this.g)return void this.g.throw(new Error("Unsupported type"));return this.consumed+=t,this.g?this.demand(this.g.next(e).value,!0):this.resolve&&this.resolve(e),e}}write(e){e instanceof ArrayBuffer?this.malloc(e.byteLength).set(new Uint8Array(e)):this.malloc(e.byteLength).set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),(this.g||this.resolve)&&this.flush()}writeU32(e){this.malloc(4).set([e>>24&255,e>>16&255,e>>8&255,255&e]),this.flush()}writeU16(e){this.malloc(2).set([e>>8&255,255&e]),this.flush()}writeU8(e){this.malloc(1)[0]=e,this.flush()}malloc(e){if(this.buffer){var t=this.buffer.length,r=t+e;if(r<=this.buffer.buffer.byteLength-this.buffer.byteOffset)this.buffer=new Uint8Array(this.buffer.buffer,this.buffer.byteOffset,r);else{const e=new Uint8Array(r);e.set(this.buffer),this.buffer=e}return this.buffer.subarray(t,r)}return this.buffer=new Uint8Array(e),this.buffer}}q.U32=F,q.U16=U,q.U8=P;class he{constructor(a){this.log=function(e){if(a._opt.debug&&a._opt.debugLevel==w){for(var t=a._opt.debugUuid?`[${a._opt.debugUuid}]`:"",r=arguments.length,n=new Array(1<r?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];console.log(`JessibucaPro${t}:[✅✅✅][${e}]`,...n)}},this.warn=function(e){if(a._opt.debug&&(a._opt.debugLevel==w||a._opt.debugLevel==v)){for(var t=a._opt.debugUuid?`[${a._opt.debugUuid}]`:"",r=arguments.length,n=new Array(1<r?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];console.log(`JessibucaPro${t}:[❗❗❗][${e}]`,...n)}},this.error=function(e){for(var t=a._opt.debugUuid?`[${a._opt.debugUuid}]`:"",r=arguments.length,n=new Array(1<r?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];console.error(`JessibucaPro${t}:[❌❌❌][${e}]`,...n)}}}class X{constructor(e){this._buffer=e,this._buffer_index=0,this._total_bytes=e.byteLength,this._total_bits=8*e.byteLength,this._current_word=0,this._current_word_bits_left=0}destroy(){this._buffer=null}_fillCurrentWord(){var e,t=this._total_bytes-this._buffer_index;t<=0?console.error("ExpGolomb: _fillCurrentWord() but no bytes available",this._total_bytes,this._buffer_index):(t=Math.min(4,t),(e=new Uint8Array(4)).set(this._buffer.subarray(this._buffer_index,this._buffer_index+t)),this._current_word=new DataView(e.buffer).getUint32(0,!1),this._buffer_index+=t,this._current_word_bits_left=8*t)}readBits(e){var t,r;return 32<e&&console.error("ExpGolomb: readBits() bits exceeded max 32bits!"),e<=this._current_word_bits_left?(t=this._current_word>>>32-e,this._current_word<<=e,this._current_word_bits_left-=e,t):(t=this._current_word_bits_left?this._current_word:0,t>>>=32-this._current_word_bits_left,e=e-this._current_word_bits_left,this._fillCurrentWord(),e=Math.min(e,this._current_word_bits_left),r=this._current_word>>>32-e,this._current_word<<=e,this._current_word_bits_left-=e,t<<e|r)}readBool(){return 1===this.readBits(1)}readByte(){return this.readBits(8)}_skipLeadingZero(){let e;for(e=0;e<this._current_word_bits_left;e++)if(0!=(this._current_word&2147483648>>>e))return this._current_word<<=e,this._current_word_bits_left-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()}readUEG(){var e=this._skipLeadingZero();return this.readBits(e+1)-1}readSEG(){var e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)}}class K{static _ebsp2rbsp(e){let t=e,r=t.byteLength,n=new Uint8Array(r),o=0;for(let e=0;e<r;e++)2<=e&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(n[o]=t[e],o++);return new Uint8Array(n.buffer,0,o)}static parseSPS(e){let t=K._ebsp2rbsp(e),r=new X(t);r.readByte();var e=r.readByte(),n=(r.readByte(),r.readByte());r.readUEG();let o=K.getProfileString(e),a=K.getLevelString(n),i=1,s=420,l=8;if((100===e||110===e||122===e||244===e||44===e||83===e||86===e||118===e||128===e||138===e||144===e)&&(3===(i=r.readUEG())&&r.readBits(1),i<=3&&(s=[0,420,422,444][i]),l=r.readUEG()+8,r.readUEG(),r.readBits(1),r.readBool())){var u=3!==i?8:12;for(let e=0;e<u;e++)r.readBool()&&(e<6?K._skipScalingList(r,16):K._skipScalingList(r,64))}r.readUEG();n=r.readUEG();if(0===n)r.readUEG();else if(1===n){r.readBits(1),r.readSEG(),r.readSEG();var d=r.readUEG();for(let e=0;e<d;e++)r.readSEG()}var e=r.readUEG(),n=(r.readBits(1),r.readUEG()),c=r.readUEG(),f=r.readBits(1);0===f&&r.readBits(1),r.readBits(1);let p=0,h=0,m=0,g=0,y=(r.readBool()&&(p=r.readUEG(),h=r.readUEG(),m=r.readUEG(),g=r.readUEG()),1),_=1,w=0,v=!0,b=0,E=0,k=(r.readBool()&&(r.readBool()&&(0<(D=r.readByte())&&D<16?(y=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][D-1],_=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][D-1]):255===D&&(y=r.readByte()<<8|r.readByte(),_=r.readByte()<<8|r.readByte())),r.readBool()&&r.readBool(),r.readBool()&&(r.readBits(4),r.readBool())&&r.readBits(24),r.readBool()&&(r.readUEG(),r.readUEG()),r.readBool())&&(D=r.readBits(32),A=r.readBits(32),v=r.readBool(),b=A,E=2*D,w=b/E),1),T=(1===y&&1===_||(k=y/_),0),B=0;B=0===i?(T=1,2-f):(T=3===i?1:2,(1===i?2:1)*(2-f));var A=16*(n+1),D=16*(c+1)*(2-f),n=(A-=(p+h)*T,D-=(m+g)*B,Math.ceil(A*k));return r.destroy(),r=null,{profile_string:o,level_string:a,bit_depth:l,ref_frames:e,chroma_format:s,chroma_format_string:K.getChromaFormatString(s),frame_rate:{fixed:v,fps:w,fps_den:E,fps_num:b},sar_ratio:{width:y,height:_},codec_size:{width:A,height:D},present_size:{width:n,height:D}}}static parseSPS$2(e){let r=e.subarray(1,4),n="avc1.";for(let t=0;t<3;t++){let e=r[t].toString(16);e.length<2&&(e="0"+e),n+=e}let t=K._ebsp2rbsp(e),o=new X(t);o.readByte();var e=o.readByte(),a=(o.readByte(),o.readByte());o.readUEG();let i=K.getProfileString(e),s=K.getLevelString(a),l=1,u=420,d=8,c=8;if((100===e||110===e||122===e||244===e||44===e||83===e||86===e||118===e||128===e||138===e||144===e)&&(3===(l=o.readUEG())&&o.readBits(1),l<=3&&(u=[0,420,422,444][l]),d=o.readUEG()+8,c=o.readUEG()+8,o.readBits(1),o.readBool())){var f=3!==l?8:12;for(let e=0;e<f;e++)o.readBool()&&(e<6?K._skipScalingList(o,16):K._skipScalingList(o,64))}o.readUEG();var p=o.readUEG();if(0===p)o.readUEG();else if(1===p){o.readBits(1),o.readSEG(),o.readSEG();var h=o.readUEG();for(let e=0;e<h;e++)o.readSEG()}var p=o.readUEG(),m=(o.readBits(1),o.readUEG()),g=o.readUEG(),y=o.readBits(1);0===y&&o.readBits(1),o.readBits(1);let _=0,w=0,v=0,b=0,E=(o.readBool()&&(_=o.readUEG(),w=o.readUEG(),v=o.readUEG(),b=o.readUEG()),1),k=1,T=0,B=!0,A=0,D=0,S=(o.readBool()&&(o.readBool()&&(0<(P=o.readByte())&&P<16?(E=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][P-1],k=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][P-1]):255===P&&(E=o.readByte()<<8|o.readByte(),k=o.readByte()<<8|o.readByte())),o.readBool()&&o.readBool(),o.readBool()&&(o.readBits(4),o.readBool())&&o.readBits(24),o.readBool()&&(o.readUEG(),o.readUEG()),o.readBool())&&(P=o.readBits(32),U=o.readBits(32),B=o.readBool(),A=U,D=2*P,T=A/D),1),C=(1===E&&1===k||(S=E/k),0),F=0;F=0===l?(C=1,2-y):(C=3===l?1:2,(1===l?2:1)*(2-y));var U=16*(m+1),P=16*(g+1)*(2-y),m=(U-=(_+w)*C,P-=(v+b)*F,Math.ceil(U*S));return o.destroy(),o=null,{codec_mimetype:n,profile_idc:e,level_idc:a,profile_string:i,level_string:s,chroma_format_idc:l,bit_depth:d,bit_depth_luma:d,bit_depth_chroma:c,ref_frames:p,chroma_format:u,chroma_format_string:K.getChromaFormatString(u),frame_rate:{fixed:B,fps:T,fps_den:D,fps_num:A},sar_ratio:{width:E,height:k},codec_size:{width:U,height:P},present_size:{width:m,height:P}}}static _skipScalingList(t,r){let n=8,o=8,a;for(let e=0;e<r;e++)0!==o&&(a=t.readSEG(),o=(n+a+256)%256),n=0===o?n:o}static getProfileString(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}}static getLevelString(e){return(e/10).toFixed(1)}static getChromaFormatString(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}}}class me{constructor(e){this.buffer=e,this.buflen=e.length,this.bufpos=0,this.bufoff=0,this.iserro=!1}read(e){let t=0,r;for(;e;){if(e<0||this.bufpos>=this.buflen)return this.iserro=!0,0;this.iserro=!1,r=8<this.bufoff+e?8-this.bufoff:e,t=(t<<=r)+(this.buffer[this.bufpos]>>8-this.bufoff-r&255>>8-r),this.bufoff+=r,e-=r,8==this.bufoff&&(this.bufpos++,this.bufoff=0)}return t}look(e){var t=this.bufpos,r=this.bufoff,e=this.read(e);return this.bufpos=t,this.bufoff=r,e}read_golomb(){let e;for(e=0;0===this.read(1)&&!this.iserro;e++);return(1<<e)+this.read(e)-1}}function ge(t){const o={},r=new DataView(t.buffer);let e=r.getUint8(0),n=r.getUint8(1);if(r.getUint8(2),r.getUint8(3),1!==e||0===n)return{};var a=1+(3&r.getUint8(4));if(3!=a&&4!=a)return{};var i=31&r.getUint8(5);if(0==i)return{};let s=6;for(let e=0;e<i;e++){var l=r.getUint16(s,!1);if(s+=2,0!==l){var u=new Uint8Array(t.buffer,s,l),l=(s+=l,K.parseSPS(u));if(0===e){o.sps=u,o.timescale=1e3,o.codecWidth=l.codec_size.width,o.codecHeight=l.codec_size.height,o.presentWidth=l.present_size.width,o.presentHeight=l.present_size.height,o.profile=l.profile_string,o.level=l.level_string,o.bitDepth=l.bit_depth,o.chromaFormat=l.chroma_format,o.sarRatio=l.sar_ratio,o.frameRate=l.frame_rate,!1!==l.frame_rate.fixed&&0!==l.frame_rate.fps_num&&0!==l.frame_rate.fps_den||(o.frameRate={fixed:!0,fps:23.976,fps_num:23976,fps_den:1e3});var l=o.frameRate.fps_den,d=o.frameRate.fps_num;o.refSampleDuration=o.timescale*(l/d);let r=u.subarray(1,4),n="avc1.";for(let t=0;t<3;t++){let e=r[t].toString(16);e.length<2&&(e="0"+e),n+=e}o.codec=n}}}var c=r.getUint8(s);if(0===c)return{};s++;for(let e=0;e<c;e++){var f,p=r.getUint16(s,!1);s+=2,0!==p&&(f=new Uint8Array(t.buffer,s,p),s+=p,o.pps=f)}if(o.videoType="avc",o.sps){const t=o.sps.byteLength,r=new Uint8Array([t>>>24&255,t>>>16&255,t>>>8&255,255&t]),e=new Uint8Array(t+4);e.set(r,0),e.set(o.sps,4),o.sps=e}if(o.pps){const t=o.pps.byteLength,r=new Uint8Array([t>>>24&255,t>>>16&255,t>>>8&255,255&t]),e=new Uint8Array(t+4);e.set(r,0),e.set(o.pps,4),o.pps=e}return o}function ye(e){var t=e.byteLength,r=new Uint8Array(4),t=(r[0]=t>>>24&255,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,new Uint8Array(t+4));return t.set(r,0),t.set(e,4),t}function Y(e){return 31&e[0]}function _e(e){return e===E}function we(e){return(t=e)!==G&&t!==O&&!_e(e);var t}function ve(e){return e===b}const be=e=>{let t=e,r=t.byteLength,n=new Uint8Array(r),o=0;for(let e=0;e<r;e++)2<=e&&3===t[e]&&0===t[e-1]&&0===t[e-2]||(n[o]=t[e],o++);return new Uint8Array(n.buffer,0,o)},Ee=e=>{switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}};function ke(e){return(126&e[0])>>1}function Te(e){return parseInt(e)===e}function Be(e){if(Te(e.length)){for(var t=0;t<e.length;t++)if(!Te(e[t])||e[t]<0||255<e[t])return;return 1}}function a(e,t){if(e.buffer&&"Uint8Array"===e.name)return e=t?e.slice?e.slice():Array.prototype.slice.call(e):e;if(Array.isArray(e)){if(Be(e))return new Uint8Array(e);throw new Error("Array contains invalid value: "+e)}if(Te(e.length)&&Be(e))return new Uint8Array(e);throw new Error("unsupported array-like object")}function l(e){return new Uint8Array(e)}function i(e,t,r,n,o){null==n&&null==o||(e=e.slice?e.slice(n,o):Array.prototype.slice.call(e,n,o)),t.set(e,r)}var Ae,m={toBytes:function(e){var t=[],r=0;for(e=encodeURI(e);r<e.length;){var n=e.charCodeAt(r++);37===n?(t.push(parseInt(e.substr(r,2),16)),r+=2):t.push(n)}return a(t)},fromBytes:function(e){for(var t=[],r=0;r<e.length;){var n=e[r];n<128?(t.push(String.fromCharCode(n)),r++):191<n&&n<224?(t.push(String.fromCharCode((31&n)<<6|63&e[r+1])),r+=2):(t.push(String.fromCharCode((15&n)<<12|(63&e[r+1])<<6|63&e[r+2])),r+=3)}return t.join("")}},g=(Ae="0123456789abcdef",{toBytes:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substr(r,2),16));return t},fromBytes:function(e){for(var t=[],r=0;r<e.length;r++){var n=e[r];t.push(Ae[(240&n)>>4]+Ae[15&n])}return t.join("")}}),De={16:10,24:12,32:14},Se=[1,2,4,8,16,32,64,128,27,54,108,216,171,77,154,47,94,188,99,198,151,53,106,212,179,125,250,239,197,145],c=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],u=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],Ce=[3328402341,4168907908,4000806809,4135287693,4294111757,3597364157,3731845041,2445657428,1613770832,33620227,3462883241,1445669757,3892248089,3050821474,1303096294,3967186586,2412431941,528646813,2311702848,4202528135,4026202645,2992200171,2387036105,4226871307,1101901292,3017069671,1604494077,1169141738,597466303,1403299063,3832705686,2613100635,1974974402,3791519004,1033081774,1277568618,1815492186,2118074177,4126668546,2211236943,1748251740,1369810420,3521504564,4193382664,3799085459,2883115123,1647391059,706024767,134480908,2512897874,1176707941,2646852446,806885416,932615841,168101135,798661301,235341577,605164086,461406363,3756188221,3454790438,1311188841,2142417613,3933566367,302582043,495158174,1479289972,874125870,907746093,3698224818,3025820398,1537253627,2756858614,1983593293,3084310113,2108928974,1378429307,3722699582,1580150641,327451799,2790478837,3117535592,0,3253595436,1075847264,3825007647,2041688520,3059440621,3563743934,2378943302,1740553945,1916352843,2487896798,2555137236,2958579944,2244988746,3151024235,3320835882,1336584933,3992714006,2252555205,2588757463,1714631509,293963156,2319795663,3925473552,67240454,4269768577,2689618160,2017213508,631218106,1269344483,2723238387,1571005438,2151694528,93294474,1066570413,563977660,1882732616,4059428100,1673313503,2008463041,2950355573,1109467491,537923632,3858759450,4260623118,3218264685,2177748300,403442708,638784309,3287084079,3193921505,899127202,2286175436,773265209,2479146071,1437050866,4236148354,2050833735,3362022572,3126681063,840505643,3866325909,3227541664,427917720,2655997905,2749160575,1143087718,1412049534,999329963,193497219,2353415882,3354324521,1807268051,672404540,2816401017,3160301282,369822493,2916866934,3688947771,1681011286,1949973070,336202270,2454276571,201721354,1210328172,3093060836,2680341085,3184776046,1135389935,3294782118,965841320,831886756,3554993207,4068047243,3588745010,2345191491,1849112409,3664604599,26054028,2983581028,2622377682,1235855840,3630984372,2891339514,4092916743,3488279077,3395642799,4101667470,1202630377,268961816,1874508501,4034427016,1243948399,1546530418,941366308,1470539505,1941222599,2546386513,3421038627,2715671932,3899946140,1042226977,2521517021,1639824860,227249030,260737669,3765465232,2084453954,1907733956,3429263018,2420656344,100860677,4160157185,470683154,3261161891,1781871967,2924959737,1773779408,394692241,2579611992,974986535,664706745,3655459128,3958962195,731420851,571543859,3530123707,2849626480,126783113,865375399,765172662,1008606754,361203602,3387549984,2278477385,2857719295,1344809080,2782912378,59542671,1503764984,160008576,437062935,1707065306,3622233649,2218934982,3496503480,2185314755,697932208,1512910199,504303377,2075177163,2824099068,1841019862,739644986],Fe=[2781242211,2230877308,2582542199,2381740923,234877682,3184946027,2984144751,1418839493,1348481072,50462977,2848876391,2102799147,434634494,1656084439,3863849899,2599188086,1167051466,2636087938,1082771913,2281340285,368048890,3954334041,3381544775,201060592,3963727277,1739838676,4250903202,3930435503,3206782108,4149453988,2531553906,1536934080,3262494647,484572669,2923271059,1783375398,1517041206,1098792767,49674231,1334037708,1550332980,4098991525,886171109,150598129,2481090929,1940642008,1398944049,1059722517,201851908,1385547719,1699095331,1587397571,674240536,2704774806,252314885,3039795866,151914247,908333586,2602270848,1038082786,651029483,1766729511,3447698098,2682942837,454166793,2652734339,1951935532,775166490,758520603,3000790638,4004797018,4217086112,4137964114,1299594043,1639438038,3464344499,2068982057,1054729187,1901997871,2534638724,4121318227,1757008337,0,750906861,1614815264,535035132,3363418545,3988151131,3201591914,1183697867,3647454910,1265776953,3734260298,3566750796,3903871064,1250283471,1807470800,717615087,3847203498,384695291,3313910595,3617213773,1432761139,2484176261,3481945413,283769337,100925954,2180939647,4037038160,1148730428,3123027871,3813386408,4087501137,4267549603,3229630528,2315620239,2906624658,3156319645,1215313976,82966005,3747855548,3245848246,1974459098,1665278241,807407632,451280895,251524083,1841287890,1283575245,337120268,891687699,801369324,3787349855,2721421207,3431482436,959321879,1469301956,4065699751,2197585534,1199193405,2898814052,3887750493,724703513,2514908019,2696962144,2551808385,3516813135,2141445340,1715741218,2119445034,2872807568,2198571144,3398190662,700968686,3547052216,1009259540,2041044702,3803995742,487983883,1991105499,1004265696,1449407026,1316239930,504629770,3683797321,168560134,1816667172,3837287516,1570751170,1857934291,4014189740,2797888098,2822345105,2754712981,936633572,2347923833,852879335,1133234376,1500395319,3084545389,2348912013,1689376213,3533459022,3762923945,3034082412,4205598294,133428468,634383082,2949277029,2398386810,3913789102,403703816,3580869306,2297460856,1867130149,1918643758,607656988,4049053350,3346248884,1368901318,600565992,2090982877,2632479860,557719327,3717614411,3697393085,2249034635,2232388234,2430627952,1115438654,3295786421,2865522278,3633334344,84280067,33027830,303828494,2747425121,1600795957,4188952407,3496589753,2434238086,1486471617,658119965,3106381470,953803233,334231800,3005978776,857870609,3151128937,1890179545,2298973838,2805175444,3056442267,574365214,2450884487,550103529,1233637070,4289353045,2018519080,2057691103,2399374476,4166623649,2148108681,387583245,3664101311,836232934,3330556482,3100665960,3280093505,2955516313,2002398509,287182607,3413881008,4238890068,3597515707,975967766],Ue=[1671808611,2089089148,2006576759,2072901243,4061003762,1807603307,1873927791,3310653893,810573872,16974337,1739181671,729634347,4263110654,3613570519,2883997099,1989864566,3393556426,2191335298,3376449993,2106063485,4195741690,1508618841,1204391495,4027317232,2917941677,3563566036,2734514082,2951366063,2629772188,2767672228,1922491506,3227229120,3082974647,4246528509,2477669779,644500518,911895606,1061256767,4144166391,3427763148,878471220,2784252325,3845444069,4043897329,1905517169,3631459288,827548209,356461077,67897348,3344078279,593839651,3277757891,405286936,2527147926,84871685,2595565466,118033927,305538066,2157648768,3795705826,3945188843,661212711,2999812018,1973414517,152769033,2208177539,745822252,439235610,455947803,1857215598,1525593178,2700827552,1391895634,994932283,3596728278,3016654259,695947817,3812548067,795958831,2224493444,1408607827,3513301457,0,3979133421,543178784,4229948412,2982705585,1542305371,1790891114,3410398667,3201918910,961245753,1256100938,1289001036,1491644504,3477767631,3496721360,4012557807,2867154858,4212583931,1137018435,1305975373,861234739,2241073541,1171229253,4178635257,33948674,2139225727,1357946960,1011120188,2679776671,2833468328,1374921297,2751356323,1086357568,2408187279,2460827538,2646352285,944271416,4110742005,3168756668,3066132406,3665145818,560153121,271589392,4279952895,4077846003,3530407890,3444343245,202643468,322250259,3962553324,1608629855,2543990167,1154254916,389623319,3294073796,2817676711,2122513534,1028094525,1689045092,1575467613,422261273,1939203699,1621147744,2174228865,1339137615,3699352540,577127458,712922154,2427141008,2290289544,1187679302,3995715566,3100863416,339486740,3732514782,1591917662,186455563,3681988059,3762019296,844522546,978220090,169743370,1239126601,101321734,611076132,1558493276,3260915650,3547250131,2901361580,1655096418,2443721105,2510565781,3828863972,2039214713,3878868455,3359869896,928607799,1840765549,2374762893,3580146133,1322425422,2850048425,1823791212,1459268694,4094161908,3928346602,1706019429,2056189050,2934523822,135794696,3134549946,2022240376,628050469,779246638,472135708,2800834470,3032970164,3327236038,3894660072,3715932637,1956440180,522272287,1272813131,3185336765,2340818315,2323976074,1888542832,1044544574,3049550261,1722469478,1222152264,50660867,4127324150,236067854,1638122081,895445557,1475980887,3117443513,2257655686,3243809217,489110045,2662934430,3778599393,4162055160,2561878936,288563729,1773916777,3648039385,2391345038,2493985684,2612407707,505560094,2274497927,3911240169,3460925390,1442818645,678973480,3749357023,2358182796,2717407649,2306869641,219617805,3218761151,3862026214,1120306242,1756942440,1103331905,2578459033,762796589,252780047,2966125488,1425844308,3151392187,372911126],Pe=[1667474886,2088535288,2004326894,2071694838,4075949567,1802223062,1869591006,3318043793,808472672,16843522,1734846926,724270422,4278065639,3621216949,2880169549,1987484396,3402253711,2189597983,3385409673,2105378810,4210693615,1499065266,1195886990,4042263547,2913856577,3570689971,2728590687,2947541573,2627518243,2762274643,1920112356,3233831835,3082273397,4261223649,2475929149,640051788,909531756,1061110142,4160160501,3435941763,875846760,2779116625,3857003729,4059105529,1903268834,3638064043,825316194,353713962,67374088,3351728789,589522246,3284360861,404236336,2526454071,84217610,2593830191,117901582,303183396,2155911963,3806477791,3958056653,656894286,2998062463,1970642922,151591698,2206440989,741110872,437923380,454765878,1852748508,1515908788,2694904667,1381168804,993742198,3604373943,3014905469,690584402,3823320797,791638366,2223281939,1398011302,3520161977,0,3991743681,538992704,4244381667,2981218425,1532751286,1785380564,3419096717,3200178535,960056178,1246420628,1280103576,1482221744,3486468741,3503319995,4025428677,2863326543,4227536621,1128514950,1296947098,859002214,2240123921,1162203018,4193849577,33687044,2139062782,1347481760,1010582648,2678045221,2829640523,1364325282,2745433693,1077985408,2408548869,2459086143,2644360225,943212656,4126475505,3166494563,3065430391,3671750063,555836226,269496352,4294908645,4092792573,3537006015,3452783745,202118168,320025894,3974901699,1600119230,2543297077,1145359496,387397934,3301201811,2812801621,2122220284,1027426170,1684319432,1566435258,421079858,1936954854,1616945344,2172753945,1330631070,3705438115,572679748,707427924,2425400123,2290647819,1179044492,4008585671,3099120491,336870440,3739122087,1583276732,185277718,3688593069,3772791771,842159716,976899700,168435220,1229577106,101059084,606366792,1549591736,3267517855,3553849021,2897014595,1650632388,2442242105,2509612081,3840161747,2038008818,3890688725,3368567691,926374254,1835907034,2374863873,3587531953,1313788572,2846482505,1819063512,1448540844,4109633523,3941213647,1701162954,2054852340,2930698567,134748176,3132806511,2021165296,623210314,774795868,471606328,2795958615,3031746419,3334885783,3907527627,3722280097,1953799400,522133822,1263263126,3183336545,2341176845,2324333839,1886425312,1044267644,3048588401,1718004428,1212733584,50529542,4143317495,235803164,1633788866,892690282,1465383342,3115962473,2256965911,3250673817,488449850,2661202215,3789633753,4177007595,2560144171,286339874,1768537042,3654906025,2391705863,2492770099,2610673197,505291324,2273808917,3924369609,3469625735,1431699370,673740880,3755965093,2358021891,2711746649,2307489801,218961690,3217021541,3873845719,1111672452,1751693520,1094828930,2576986153,757954394,252645662,2964376443,1414855848,3149649517,370555436],xe=[1374988112,2118214995,437757123,975658646,1001089995,530400753,2902087851,1273168787,540080725,2910219766,2295101073,4110568485,1340463100,3307916247,641025152,3043140495,3736164937,632953703,1172967064,1576976609,3274667266,2169303058,2370213795,1809054150,59727847,361929877,3211623147,2505202138,3569255213,1484005843,1239443753,2395588676,1975683434,4102977912,2572697195,666464733,3202437046,4035489047,3374361702,2110667444,1675577880,3843699074,2538681184,1649639237,2976151520,3144396420,4269907996,4178062228,1883793496,2403728665,2497604743,1383856311,2876494627,1917518562,3810496343,1716890410,3001755655,800440835,2261089178,3543599269,807962610,599762354,33778362,3977675356,2328828971,2809771154,4077384432,1315562145,1708848333,101039829,3509871135,3299278474,875451293,2733856160,92987698,2767645557,193195065,1080094634,1584504582,3178106961,1042385657,2531067453,3711829422,1306967366,2438237621,1908694277,67556463,1615861247,429456164,3602770327,2302690252,1742315127,2968011453,126454664,3877198648,2043211483,2709260871,2084704233,4169408201,0,159417987,841739592,504459436,1817866830,4245618683,260388950,1034867998,908933415,168810852,1750902305,2606453969,607530554,202008497,2472011535,3035535058,463180190,2160117071,1641816226,1517767529,470948374,3801332234,3231722213,1008918595,303765277,235474187,4069246893,766945465,337553864,1475418501,2943682380,4003061179,2743034109,4144047775,1551037884,1147550661,1543208500,2336434550,3408119516,3069049960,3102011747,3610369226,1113818384,328671808,2227573024,2236228733,3535486456,2935566865,3341394285,496906059,3702665459,226906860,2009195472,733156972,2842737049,294930682,1206477858,2835123396,2700099354,1451044056,573804783,2269728455,3644379585,2362090238,2564033334,2801107407,2776292904,3669462566,1068351396,742039012,1350078989,1784663195,1417561698,4136440770,2430122216,775550814,2193862645,2673705150,1775276924,1876241833,3475313331,3366754619,270040487,3902563182,3678124923,3441850377,1851332852,3969562369,2203032232,3868552805,2868897406,566021896,4011190502,3135740889,1248802510,3936291284,699432150,832877231,708780849,3332740144,899835584,1951317047,4236429990,3767586992,866637845,4043610186,1106041591,2144161806,395441711,1984812685,1139781709,3433712980,3835036895,2664543715,1282050075,3240894392,1181045119,2640243204,25965917,4203181171,4211818798,3009879386,2463879762,3910161971,1842759443,2597806476,933301370,1509430414,3943906441,3467192302,3076639029,3776767469,2051518780,2631065433,1441952575,404016761,1942435775,1408749034,1610459739,3745345300,2017778566,3400528769,3110650942,941896748,3265478751,371049330,3168937228,675039627,4279080257,967311729,135050206,3635733660,1683407248,2076935265,3576870512,1215061108,3501741890],Le=[1347548327,1400783205,3273267108,2520393566,3409685355,4045380933,2880240216,2471224067,1428173050,4138563181,2441661558,636813900,4233094615,3620022987,2149987652,2411029155,1239331162,1730525723,2554718734,3781033664,46346101,310463728,2743944855,3328955385,3875770207,2501218972,3955191162,3667219033,768917123,3545789473,692707433,1150208456,1786102409,2029293177,1805211710,3710368113,3065962831,401639597,1724457132,3028143674,409198410,2196052529,1620529459,1164071807,3769721975,2226875310,486441376,2499348523,1483753576,428819965,2274680428,3075636216,598438867,3799141122,1474502543,711349675,129166120,53458370,2592523643,2782082824,4063242375,2988687269,3120694122,1559041666,730517276,2460449204,4042459122,2706270690,3446004468,3573941694,533804130,2328143614,2637442643,2695033685,839224033,1973745387,957055980,2856345839,106852767,1371368976,4181598602,1033297158,2933734917,1179510461,3046200461,91341917,1862534868,4284502037,605657339,2547432937,3431546947,2003294622,3182487618,2282195339,954669403,3682191598,1201765386,3917234703,3388507166,0,2198438022,1211247597,2887651696,1315723890,4227665663,1443857720,507358933,657861945,1678381017,560487590,3516619604,975451694,2970356327,261314535,3535072918,2652609425,1333838021,2724322336,1767536459,370938394,182621114,3854606378,1128014560,487725847,185469197,2918353863,3106780840,3356761769,2237133081,1286567175,3152976349,4255350624,2683765030,3160175349,3309594171,878443390,1988838185,3704300486,1756818940,1673061617,3403100636,272786309,1075025698,545572369,2105887268,4174560061,296679730,1841768865,1260232239,4091327024,3960309330,3497509347,1814803222,2578018489,4195456072,575138148,3299409036,446754879,3629546796,4011996048,3347532110,3252238545,4270639778,915985419,3483825537,681933534,651868046,2755636671,3828103837,223377554,2607439820,1649704518,3270937875,3901806776,1580087799,4118987695,3198115200,2087309459,2842678573,3016697106,1003007129,2802849917,1860738147,2077965243,164439672,4100872472,32283319,2827177882,1709610350,2125135846,136428751,3874428392,3652904859,3460984630,3572145929,3593056380,2939266226,824852259,818324884,3224740454,930369212,2801566410,2967507152,355706840,1257309336,4148292826,243256656,790073846,2373340630,1296297904,1422699085,3756299780,3818836405,457992840,3099667487,2135319889,77422314,1560382517,1945798516,788204353,1521706781,1385356242,870912086,325965383,2358957921,2050466060,2388260884,2313884476,4006521127,901210569,3990953189,1014646705,1503449823,1062597235,2031621326,3212035895,3931371469,1533017514,350174575,2256028891,2177544179,1052338372,741876788,1606591296,1914052035,213705253,2334669897,1107234197,1899603969,3725069491,2631447780,2422494913,1635502980,1893020342,1950903388,1120974935],Re=[2807058932,1699970625,2764249623,1586903591,1808481195,1173430173,1487645946,59984867,4199882800,1844882806,1989249228,1277555970,3623636965,3419915562,1149249077,2744104290,1514790577,459744698,244860394,3235995134,1963115311,4027744588,2544078150,4190530515,1608975247,2627016082,2062270317,1507497298,2200818878,567498868,1764313568,3359936201,2305455554,2037970062,1047239e3,1910319033,1337376481,2904027272,2892417312,984907214,1243112415,830661914,861968209,2135253587,2011214180,2927934315,2686254721,731183368,1750626376,4246310725,1820824798,4172763771,3542330227,48394827,2404901663,2871682645,671593195,3254988725,2073724613,145085239,2280796200,2779915199,1790575107,2187128086,472615631,3029510009,4075877127,3802222185,4107101658,3201631749,1646252340,4270507174,1402811438,1436590835,3778151818,3950355702,3963161475,4020912224,2667994737,273792366,2331590177,104699613,95345982,3175501286,2377486676,1560637892,3564045318,369057872,4213447064,3919042237,1137477952,2658625497,1119727848,2340947849,1530455833,4007360968,172466556,266959938,516552836,0,2256734592,3980931627,1890328081,1917742170,4294704398,945164165,3575528878,958871085,3647212047,2787207260,1423022939,775562294,1739656202,3876557655,2530391278,2443058075,3310321856,547512796,1265195639,437656594,3121275539,719700128,3762502690,387781147,218828297,3350065803,2830708150,2848461854,428169201,122466165,3720081049,1627235199,648017665,4122762354,1002783846,2117360635,695634755,3336358691,4234721005,4049844452,3704280881,2232435299,574624663,287343814,612205898,1039717051,840019705,2708326185,793451934,821288114,1391201670,3822090177,376187827,3113855344,1224348052,1679968233,2361698556,1058709744,752375421,2431590963,1321699145,3519142200,2734591178,188127444,2177869557,3727205754,2384911031,3215212461,2648976442,2450346104,3432737375,1180849278,331544205,3102249176,4150144569,2952102595,2159976285,2474404304,766078933,313773861,2570832044,2108100632,1668212892,3145456443,2013908262,418672217,3070356634,2594734927,1852171925,3867060991,3473416636,3907448597,2614737639,919489135,164948639,2094410160,2997825956,590424639,2486224549,1723872674,3157750862,3399941250,3501252752,3625268135,2555048196,3673637356,1343127501,4130281361,3599595085,2957853679,1297403050,81781910,3051593425,2283490410,532201772,1367295589,3926170974,895287692,1953757831,1093597963,492483431,3528626907,1446242576,1192455638,1636604631,209336225,344873464,1015671571,669961897,3375740769,3857572124,2973530695,3747192018,1933530610,3464042516,935293895,3454686199,2858115069,1863638845,3683022916,4085369519,3292445032,875313188,1080017571,3279033885,621591778,1233856572,2504130317,24197544,3017672716,3835484340,3247465558,2220981195,3060847922,1551124588,1463996600],Ie=[4104605777,1097159550,396673818,660510266,2875968315,2638606623,4200115116,3808662347,821712160,1986918061,3430322568,38544885,3856137295,718002117,893681702,1654886325,2975484382,3122358053,3926825029,4274053469,796197571,1290801793,1184342925,3556361835,2405426947,2459735317,1836772287,1381620373,3196267988,1948373848,3764988233,3385345166,3263785589,2390325492,1480485785,3111247143,3780097726,2293045232,548169417,3459953789,3746175075,439452389,1362321559,1400849762,1685577905,1806599355,2174754046,137073913,1214797936,1174215055,3731654548,2079897426,1943217067,1258480242,529487843,1437280870,3945269170,3049390895,3313212038,923313619,679998e3,3215307299,57326082,377642221,3474729866,2041877159,133361907,1776460110,3673476453,96392454,878845905,2801699524,777231668,4082475170,2330014213,4142626212,2213296395,1626319424,1906247262,1846563261,562755902,3708173718,1040559837,3871163981,1418573201,3294430577,114585348,1343618912,2566595609,3186202582,1078185097,3651041127,3896688048,2307622919,425408743,3371096953,2081048481,1108339068,2216610296,0,2156299017,736970802,292596766,1517440620,251657213,2235061775,2933202493,758720310,265905162,1554391400,1532285339,908999204,174567692,1474760595,4002861748,2610011675,3234156416,3693126241,2001430874,303699484,2478443234,2687165888,585122620,454499602,151849742,2345119218,3064510765,514443284,4044981591,1963412655,2581445614,2137062819,19308535,1928707164,1715193156,4219352155,1126790795,600235211,3992742070,3841024952,836553431,1669664834,2535604243,3323011204,1243905413,3141400786,4180808110,698445255,2653899549,2989552604,2253581325,3252932727,3004591147,1891211689,2487810577,3915653703,4237083816,4030667424,2100090966,865136418,1229899655,953270745,3399679628,3557504664,4118925222,2061379749,3079546586,2915017791,983426092,2022837584,1607244650,2118541908,2366882550,3635996816,972512814,3283088770,1568718495,3499326569,3576539503,621982671,2895723464,410887952,2623762152,1002142683,645401037,1494807662,2595684844,1335535747,2507040230,4293295786,3167684641,367585007,3885750714,1865862730,2668221674,2960971305,2763173681,1059270954,2777952454,2724642869,1320957812,2194319100,2429595872,2815956275,77089521,3973773121,3444575871,2448830231,1305906550,4021308739,2857194700,2516901860,3518358430,1787304780,740276417,1699839814,1592394909,2352307457,2272556026,188821243,1729977011,3687994002,274084841,3594982253,3613494426,2701949495,4162096729,322734571,2837966542,1640576439,484830689,1202797690,3537852828,4067639125,349075736,3342319475,4157467219,4255800159,1030690015,1155237496,2951971274,1757691577,607398968,2738905026,499347990,3794078908,1011452712,227885567,2818666809,213114376,3034881240,1455525988,3414450555,850817237,1817998408,3092726480],Me=[0,235474187,470948374,303765277,941896748,908933415,607530554,708780849,1883793496,2118214995,1817866830,1649639237,1215061108,1181045119,1417561698,1517767529,3767586992,4003061179,4236429990,4069246893,3635733660,3602770327,3299278474,3400528769,2430122216,2664543715,2362090238,2193862645,2835123396,2801107407,3035535058,3135740889,3678124923,3576870512,3341394285,3374361702,3810496343,3977675356,4279080257,4043610186,2876494627,2776292904,3076639029,3110650942,2472011535,2640243204,2403728665,2169303058,1001089995,899835584,666464733,699432150,59727847,226906860,530400753,294930682,1273168787,1172967064,1475418501,1509430414,1942435775,2110667444,1876241833,1641816226,2910219766,2743034109,2976151520,3211623147,2505202138,2606453969,2302690252,2269728455,3711829422,3543599269,3240894392,3475313331,3843699074,3943906441,4178062228,4144047775,1306967366,1139781709,1374988112,1610459739,1975683434,2076935265,1775276924,1742315127,1034867998,866637845,566021896,800440835,92987698,193195065,429456164,395441711,1984812685,2017778566,1784663195,1683407248,1315562145,1080094634,1383856311,1551037884,101039829,135050206,437757123,337553864,1042385657,807962610,573804783,742039012,2531067453,2564033334,2328828971,2227573024,2935566865,2700099354,3001755655,3168937228,3868552805,3902563182,4203181171,4102977912,3736164937,3501741890,3265478751,3433712980,1106041591,1340463100,1576976609,1408749034,2043211483,2009195472,1708848333,1809054150,832877231,1068351396,766945465,599762354,159417987,126454664,361929877,463180190,2709260871,2943682380,3178106961,3009879386,2572697195,2538681184,2236228733,2336434550,3509871135,3745345300,3441850377,3274667266,3910161971,3877198648,4110568485,4211818798,2597806476,2497604743,2261089178,2295101073,2733856160,2902087851,3202437046,2968011453,3936291284,3835036895,4136440770,4169408201,3535486456,3702665459,3467192302,3231722213,2051518780,1951317047,1716890410,1750902305,1113818384,1282050075,1584504582,1350078989,168810852,67556463,371049330,404016761,841739592,1008918595,775550814,540080725,3969562369,3801332234,4035489047,4269907996,3569255213,3669462566,3366754619,3332740144,2631065433,2463879762,2160117071,2395588676,2767645557,2868897406,3102011747,3069049960,202008497,33778362,270040487,504459436,875451293,975658646,675039627,641025152,2084704233,1917518562,1615861247,1851332852,1147550661,1248802510,1484005843,1451044056,933301370,967311729,733156972,632953703,260388950,25965917,328671808,496906059,1206477858,1239443753,1543208500,1441952575,2144161806,1908694277,1675577880,1842759443,3610369226,3644379585,3408119516,3307916247,4011190502,3776767469,4077384432,4245618683,2809771154,2842737049,3144396420,3043140495,2673705150,2438237621,2203032232,2370213795],$e=[0,185469197,370938394,487725847,741876788,657861945,975451694,824852259,1483753576,1400783205,1315723890,1164071807,1950903388,2135319889,1649704518,1767536459,2967507152,3152976349,2801566410,2918353863,2631447780,2547432937,2328143614,2177544179,3901806776,3818836405,4270639778,4118987695,3299409036,3483825537,3535072918,3652904859,2077965243,1893020342,1841768865,1724457132,1474502543,1559041666,1107234197,1257309336,598438867,681933534,901210569,1052338372,261314535,77422314,428819965,310463728,3409685355,3224740454,3710368113,3593056380,3875770207,3960309330,4045380933,4195456072,2471224067,2554718734,2237133081,2388260884,3212035895,3028143674,2842678573,2724322336,4138563181,4255350624,3769721975,3955191162,3667219033,3516619604,3431546947,3347532110,2933734917,2782082824,3099667487,3016697106,2196052529,2313884476,2499348523,2683765030,1179510461,1296297904,1347548327,1533017514,1786102409,1635502980,2087309459,2003294622,507358933,355706840,136428751,53458370,839224033,957055980,605657339,790073846,2373340630,2256028891,2607439820,2422494913,2706270690,2856345839,3075636216,3160175349,3573941694,3725069491,3273267108,3356761769,4181598602,4063242375,4011996048,3828103837,1033297158,915985419,730517276,545572369,296679730,446754879,129166120,213705253,1709610350,1860738147,1945798516,2029293177,1239331162,1120974935,1606591296,1422699085,4148292826,4233094615,3781033664,3931371469,3682191598,3497509347,3446004468,3328955385,2939266226,2755636671,3106780840,2988687269,2198438022,2282195339,2501218972,2652609425,1201765386,1286567175,1371368976,1521706781,1805211710,1620529459,2105887268,1988838185,533804130,350174575,164439672,46346101,870912086,954669403,636813900,788204353,2358957921,2274680428,2592523643,2441661558,2695033685,2880240216,3065962831,3182487618,3572145929,3756299780,3270937875,3388507166,4174560061,4091327024,4006521127,3854606378,1014646705,930369212,711349675,560487590,272786309,457992840,106852767,223377554,1678381017,1862534868,1914052035,2031621326,1211247597,1128014560,1580087799,1428173050,32283319,182621114,401639597,486441376,768917123,651868046,1003007129,818324884,1503449823,1385356242,1333838021,1150208456,1973745387,2125135846,1673061617,1756818940,2970356327,3120694122,2802849917,2887651696,2637442643,2520393566,2334669897,2149987652,3917234703,3799141122,4284502037,4100872472,3309594171,3460984630,3545789473,3629546796,2050466060,1899603969,1814803222,1730525723,1443857720,1560382517,1075025698,1260232239,575138148,692707433,878443390,1062597235,243256656,91341917,409198410,325965383,3403100636,3252238545,3704300486,3620022987,3874428392,3990953189,4042459122,4227665663,2460449204,2578018489,2226875310,2411029155,3198115200,3046200461,2827177882,2743944855],Ne=[0,218828297,437656594,387781147,875313188,958871085,775562294,590424639,1750626376,1699970625,1917742170,2135253587,1551124588,1367295589,1180849278,1265195639,3501252752,3720081049,3399941250,3350065803,3835484340,3919042237,4270507174,4085369519,3102249176,3051593425,2734591178,2952102595,2361698556,2177869557,2530391278,2614737639,3145456443,3060847922,2708326185,2892417312,2404901663,2187128086,2504130317,2555048196,3542330227,3727205754,3375740769,3292445032,3876557655,3926170974,4246310725,4027744588,1808481195,1723872674,1910319033,2094410160,1608975247,1391201670,1173430173,1224348052,59984867,244860394,428169201,344873464,935293895,984907214,766078933,547512796,1844882806,1627235199,2011214180,2062270317,1507497298,1423022939,1137477952,1321699145,95345982,145085239,532201772,313773861,830661914,1015671571,731183368,648017665,3175501286,2957853679,2807058932,2858115069,2305455554,2220981195,2474404304,2658625497,3575528878,3625268135,3473416636,3254988725,3778151818,3963161475,4213447064,4130281361,3599595085,3683022916,3432737375,3247465558,3802222185,4020912224,4172763771,4122762354,3201631749,3017672716,2764249623,2848461854,2331590177,2280796200,2431590963,2648976442,104699613,188127444,472615631,287343814,840019705,1058709744,671593195,621591778,1852171925,1668212892,1953757831,2037970062,1514790577,1463996600,1080017571,1297403050,3673637356,3623636965,3235995134,3454686199,4007360968,3822090177,4107101658,4190530515,2997825956,3215212461,2830708150,2779915199,2256734592,2340947849,2627016082,2443058075,172466556,122466165,273792366,492483431,1047239e3,861968209,612205898,695634755,1646252340,1863638845,2013908262,1963115311,1446242576,1530455833,1277555970,1093597963,1636604631,1820824798,2073724613,1989249228,1436590835,1487645946,1337376481,1119727848,164948639,81781910,331544205,516552836,1039717051,821288114,669961897,719700128,2973530695,3157750862,2871682645,2787207260,2232435299,2283490410,2667994737,2450346104,3647212047,3564045318,3279033885,3464042516,3980931627,3762502690,4150144569,4199882800,3070356634,3121275539,2904027272,2686254721,2200818878,2384911031,2570832044,2486224549,3747192018,3528626907,3310321856,3359936201,3950355702,3867060991,4049844452,4234721005,1739656202,1790575107,2108100632,1890328081,1402811438,1586903591,1233856572,1149249077,266959938,48394827,369057872,418672217,1002783846,919489135,567498868,752375421,209336225,24197544,376187827,459744698,945164165,895287692,574624663,793451934,1679968233,1764313568,2117360635,1933530610,1343127501,1560637892,1243112415,1192455638,3704280881,3519142200,3336358691,3419915562,3907448597,3857572124,4075877127,4294704398,3029510009,3113855344,2927934315,2744104290,2159976285,2377486676,2594734927,2544078150],Ge=[0,151849742,303699484,454499602,607398968,758720310,908999204,1059270954,1214797936,1097159550,1517440620,1400849762,1817998408,1699839814,2118541908,2001430874,2429595872,2581445614,2194319100,2345119218,3034881240,3186202582,2801699524,2951971274,3635996816,3518358430,3399679628,3283088770,4237083816,4118925222,4002861748,3885750714,1002142683,850817237,698445255,548169417,529487843,377642221,227885567,77089521,1943217067,2061379749,1640576439,1757691577,1474760595,1592394909,1174215055,1290801793,2875968315,2724642869,3111247143,2960971305,2405426947,2253581325,2638606623,2487810577,3808662347,3926825029,4044981591,4162096729,3342319475,3459953789,3576539503,3693126241,1986918061,2137062819,1685577905,1836772287,1381620373,1532285339,1078185097,1229899655,1040559837,923313619,740276417,621982671,439452389,322734571,137073913,19308535,3871163981,4021308739,4104605777,4255800159,3263785589,3414450555,3499326569,3651041127,2933202493,2815956275,3167684641,3049390895,2330014213,2213296395,2566595609,2448830231,1305906550,1155237496,1607244650,1455525988,1776460110,1626319424,2079897426,1928707164,96392454,213114376,396673818,514443284,562755902,679998e3,865136418,983426092,3708173718,3557504664,3474729866,3323011204,4180808110,4030667424,3945269170,3794078908,2507040230,2623762152,2272556026,2390325492,2975484382,3092726480,2738905026,2857194700,3973773121,3856137295,4274053469,4157467219,3371096953,3252932727,3673476453,3556361835,2763173681,2915017791,3064510765,3215307299,2156299017,2307622919,2459735317,2610011675,2081048481,1963412655,1846563261,1729977011,1480485785,1362321559,1243905413,1126790795,878845905,1030690015,645401037,796197571,274084841,425408743,38544885,188821243,3613494426,3731654548,3313212038,3430322568,4082475170,4200115116,3780097726,3896688048,2668221674,2516901860,2366882550,2216610296,3141400786,2989552604,2837966542,2687165888,1202797690,1320957812,1437280870,1554391400,1669664834,1787304780,1906247262,2022837584,265905162,114585348,499347990,349075736,736970802,585122620,972512814,821712160,2595684844,2478443234,2293045232,2174754046,3196267988,3079546586,2895723464,2777952454,3537852828,3687994002,3234156416,3385345166,4142626212,4293295786,3841024952,3992742070,174567692,57326082,410887952,292596766,777231668,660510266,1011452712,893681702,1108339068,1258480242,1343618912,1494807662,1715193156,1865862730,1948373848,2100090966,2701949495,2818666809,3004591147,3122358053,2235061775,2352307457,2535604243,2653899549,3915653703,3764988233,4219352155,4067639125,3444575871,3294430577,3746175075,3594982253,836553431,953270745,600235211,718002117,367585007,484830689,133361907,251657213,2041877159,1891211689,1806599355,1654886325,1568718495,1418573201,1335535747,1184342925];function Oe(e){for(var t=[],r=0;r<e.length;r+=4)t.push(e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3]);return t}function n(e){if(!(this instanceof n))throw Error("AES must be instanitated with `new`");Object.defineProperty(this,"key",{value:a(e,!0)}),this._prepare()}function r(e){if(!(this instanceof r))throw Error("AES must be instanitated with `new`");this.description="Electronic Code Block",this.name="ecb",this._aes=new n(e)}function s(e,t){if(!(this instanceof s))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Block Chaining",this.name="cbc",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=l(16);this._lastCipherblock=a(t,!0),this._aes=new n(e)}function d(e,t,r){if(!(this instanceof d))throw Error("AES must be instanitated with `new`");if(this.description="Cipher Feedback",this.name="cfb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 size)")}else t=l(16);this.segmentSize=r=r||1,this._shiftRegister=a(t,!0),this._aes=new n(e)}function f(e,t){if(!(this instanceof f))throw Error("AES must be instanitated with `new`");if(this.description="Output Feedback",this.name="ofb",t){if(16!=t.length)throw new Error("invalid initialation vector size (must be 16 bytes)")}else t=l(16);this._lastPrecipher=a(t,!0),this._lastPrecipherIndex=16,this._aes=new n(e)}function p(e){if(!(this instanceof p))throw Error("Counter must be instanitated with `new`");"number"==typeof(e=0!==e?e||1:e)?(this._counter=l(16),this.setValue(e)):this.setBytes(e)}function h(e,t){if(!(this instanceof h))throw Error("AES must be instanitated with `new`");this.description="Counter",this.name="ctr",t instanceof p||(t=new p(t)),this._counter=t,this._remainingCounter=null,this._remainingCounterIndex=16,this._aes=new n(e)}n.prototype._prepare=function(){var e=De[this.key.length];if(null==e)throw new Error("invalid key size (must be 16, 24 or 32 bytes)");this._Ke=[],this._Kd=[];for(var t=0;t<=e;t++)this._Ke.push([0,0,0,0]),this._Kd.push([0,0,0,0]);for(var r,n=4*(e+1),o=this.key.length/4,a=Oe(this.key),t=0;t<o;t++)this._Ke[r=t>>2][t%4]=a[t],this._Kd[e-r][t%4]=a[t];for(var i,s=0,l=o;l<n;){if(i=a[o-1],a[0]^=c[i>>16&255]<<24^c[i>>8&255]<<16^c[255&i]<<8^c[i>>24&255]^Se[s]<<24,s+=1,8!=o)for(t=1;t<o;t++)a[t]^=a[t-1];else{for(t=1;t<o/2;t++)a[t]^=a[t-1];for(i=a[o/2-1],a[o/2]^=c[255&i]^c[i>>8&255]<<8^c[i>>16&255]<<16^c[i>>24&255]<<24,t=o/2+1;t<o;t++)a[t]^=a[t-1]}for(t=0;t<o&&l<n;)this._Ke[u=l>>2][d=l%4]=a[t],this._Kd[e-u][d]=a[t++],l++}for(var u=1;u<e;u++)for(var d=0;d<4;d++)i=this._Kd[u][d],this._Kd[u][d]=Me[i>>24&255]^$e[i>>16&255]^Ne[i>>8&255]^Ge[255&i]},n.prototype.encrypt=function(e){if(16!=e.length)throw new Error("invalid plaintext size (must be 16 bytes)");for(var t=this._Ke.length-1,r=[0,0,0,0],n=Oe(e),o=0;o<4;o++)n[o]^=this._Ke[0][o];for(var a=1;a<t;a++){for(o=0;o<4;o++)r[o]=Ce[n[o]>>24&255]^Fe[n[(o+1)%4]>>16&255]^Ue[n[(o+2)%4]>>8&255]^Pe[255&n[(o+3)%4]]^this._Ke[a][o];n=r.slice()}for(var i,s=l(16),o=0;o<4;o++)i=this._Ke[t][o],s[4*o]=255&(c[n[o]>>24&255]^i>>24),s[4*o+1]=255&(c[n[(o+1)%4]>>16&255]^i>>16),s[4*o+2]=255&(c[n[(o+2)%4]>>8&255]^i>>8),s[4*o+3]=255&(c[255&n[(o+3)%4]]^i);return s},n.prototype.decrypt=function(e){if(16!=e.length)throw new Error("invalid ciphertext size (must be 16 bytes)");for(var t=this._Kd.length-1,r=[0,0,0,0],n=Oe(e),o=0;o<4;o++)n[o]^=this._Kd[0][o];for(var a=1;a<t;a++){for(o=0;o<4;o++)r[o]=xe[n[o]>>24&255]^Le[n[(o+3)%4]>>16&255]^Re[n[(o+2)%4]>>8&255]^Ie[255&n[(o+1)%4]]^this._Kd[a][o];n=r.slice()}for(var i,s=l(16),o=0;o<4;o++)i=this._Kd[t][o],s[4*o]=255&(u[n[o]>>24&255]^i>>24),s[4*o+1]=255&(u[n[(o+3)%4]>>16&255]^i>>16),s[4*o+2]=255&(u[n[(o+2)%4]>>8&255]^i>>8),s[4*o+3]=255&(u[255&n[(o+1)%4]]^i);return s},r.prototype.encrypt=function(e){if((e=a(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=l(e.length),r=l(16),n=0;n<e.length;n+=16)i(e,r,0,n,n+16),i(r=this._aes.encrypt(r),t,n);return t},r.prototype.decrypt=function(e){if((e=a(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=l(e.length),r=l(16),n=0;n<e.length;n+=16)i(e,r,0,n,n+16),i(r=this._aes.decrypt(r),t,n);return t},s.prototype.encrypt=function(e){if((e=a(e)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");for(var t=l(e.length),r=l(16),n=0;n<e.length;n+=16){i(e,r,0,n,n+16);for(var o=0;o<16;o++)r[o]^=this._lastCipherblock[o];this._lastCipherblock=this._aes.encrypt(r),i(this._lastCipherblock,t,n)}return t},s.prototype.decrypt=function(e){if((e=a(e)).length%16!=0)throw new Error("invalid ciphertext size (must be multiple of 16 bytes)");for(var t=l(e.length),r=l(16),n=0;n<e.length;n+=16){i(e,r,0,n,n+16);for(var r=this._aes.decrypt(r),o=0;o<16;o++)t[n+o]=r[o]^this._lastCipherblock[o];i(e,this._lastCipherblock,0,n,n+16)}return t},d.prototype.encrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid plaintext size (must be segmentSize bytes)");for(var t=a(e,!0),r=0;r<t.length;r+=this.segmentSize){for(var n=this._aes.encrypt(this._shiftRegister),o=0;o<this.segmentSize;o++)t[r+o]^=n[o];i(this._shiftRegister,this._shiftRegister,0,this.segmentSize),i(t,this._shiftRegister,16-this.segmentSize,r,r+this.segmentSize)}return t},d.prototype.decrypt=function(e){if(e.length%this.segmentSize!=0)throw new Error("invalid ciphertext size (must be segmentSize bytes)");for(var t=a(e,!0),r=0;r<t.length;r+=this.segmentSize){for(var n=this._aes.encrypt(this._shiftRegister),o=0;o<this.segmentSize;o++)t[r+o]^=n[o];i(this._shiftRegister,this._shiftRegister,0,this.segmentSize),i(e,this._shiftRegister,16-this.segmentSize,r,r+this.segmentSize)}return t},f.prototype.decrypt=f.prototype.encrypt=function(e){for(var t=a(e,!0),r=0;r<t.length;r++)16===this._lastPrecipherIndex&&(this._lastPrecipher=this._aes.encrypt(this._lastPrecipher),this._lastPrecipherIndex=0),t[r]^=this._lastPrecipher[this._lastPrecipherIndex++];return t},p.prototype.setValue=function(e){if("number"!=typeof e||parseInt(e)!=e)throw new Error("invalid counter value (must be an integer)");if(e>Number.MAX_SAFE_INTEGER)throw new Error("integer value out of safe range");for(var t=15;0<=t;--t)this._counter[t]=e%256,e=parseInt(e/256)},p.prototype.setBytes=function(e){if(16!=(e=a(e,!0)).length)throw new Error("invalid counter bytes size (must be 16 bytes)");this._counter=e},p.prototype.increment=function(){for(var e=15;0<=e;e--){if(255!==this._counter[e]){this._counter[e]++;break}this._counter[e]=0}};h.prototype.decrypt=h.prototype.encrypt=function(e){for(var t=a(e,!0),r=0;r<t.length;r++)16===this._remainingCounterIndex&&(this._remainingCounter=this._aes.encrypt(this._counter._counter),this._remainingCounterIndex=0,this._counter.increment()),t[r]^=this._remainingCounter[this._remainingCounterIndex++];return t};const Ve={AES:n,Counter:p,ModeOfOperation:{ecb:r,cbc:s,cfb:d,ofb:f,ctr:h},utils:{hex:g,utf8:m},padding:{pkcs7:{pad:function(e){var t=16-(e=a(e,!0)).length%16,r=l(e.length+t);i(e,r);for(var n=e.length;n<r.length;n++)r[n]=t;return r},strip:function(e){if((e=a(e,!0)).length<16)throw new Error("PKCS#7 invalid length");var t=e[e.length-1];if(16<t)throw new Error("PKCS#7 padding byte out of range");for(var r=e.length-t,n=0;n<t;n++)if(e[r+n]!==t)throw new Error("PKCS#7 invalid padding byte");var o=l(r);return i(e,o,0,0,r),o}}},_arrayTest:{coerceArray:a,createArray:l,copyArray:i}};function ze(l){function n(){r&&(r.abort(),r=null)}let o=[],u=[],a={},r=new AbortController,i=null,s=null,e=null,d=null,c=null,f=null,p=!1,h=!1,m=!1,g=!1,y=null,_=null,w=null,v=[],b=0,E=0,t=null,k=null,T=0,B=0,A=!1,D=!1,S=()=>{var e=function(){{var r=Q;let t="";if("object"==typeof r)try{t=JSON.stringify(r),t=JSON.parse(t)}catch(e){t=r}else t=r;return t}}();return{debug:e.debug,debugLevel:e.debugLevel,debugUuid:e.debugUuid,useOffscreen:e.useOffscreen,useWCS:e.useWCS,videoBuffer:e.videoBuffer,videoBufferDelay:e.videoBufferDelay,openWebglAlignment:e.openWebglAlignment,playType:e.playType,hasAudio:e.hasAudio,hasVideo:e.hasVideo,playbackRate:1,playbackForwardMaxRateDecodeIFrame:e.playbackForwardMaxRateDecodeIFrame,playbackIsCacheBeforeDecodeForFpsRender:e.playbackConfig.isCacheBeforeDecodeForFpsRender,sampleRate:0,networkDelay:e.networkDelay,visibility:!0,useSIMD:e.useSIMD,isRecording:!1,recordType:e.recordType,isNakedFlow:e.isNakedFlow,checkFirstIFrame:e.checkFirstIFrame,audioBufferSize:1024,isCrypto:e.isCrypto,cryptoKey:e.cryptoKey,cryptoIV:e.cryptoIV}},C=("VideoEncoder"in self&&(a={hasInit:!1,isEmitInfo:!1,offscreenCanvas:null,offscreenCanvasCtx:null,decoder:new VideoDecoder({output:function(t){var e;a.isEmitInfo||(F.debug.log("worker","Webcodecs Video Decoder initSize"),postMessage({cmd:ee,w:t.codedWidth,h:t.codedHeight}),a.isEmitInfo=!0,a.offscreenCanvas=new OffscreenCanvas(t.codedWidth,t.codedHeight),a.offscreenCanvasCtx=a.offscreenCanvas.getContext("2d")),"function"==typeof t.createImageBitmap?t.createImageBitmap().then(e=>{a.offscreenCanvasCtx.drawImage(e,0,0,t.codedWidth,t.codedHeight);e=a.offscreenCanvas.transferToImageBitmap();postMessage({cmd:R,buffer:e,delay:F.delay,ts:0},[e]),setTimeout(function(){t.close?t.close():t.destroy()},100)}):(a.offscreenCanvasCtx.drawImage(t,0,0,t.codedWidth,t.codedHeight),e=a.offscreenCanvas.transferToImageBitmap(),postMessage({cmd:R,buffer:e,delay:F.delay,ts:0},[e]),setTimeout(function(){t.close?t.close():t.destroy()},100))},error:function(e){F.debug.error("worker","VideoDecoder error",e)}}),decode:function(e,t,r){var n=e[0]>>4==1;if(a.hasInit){const r=new EncodedVideoChunk({data:e.slice(5),timestamp:t,type:n?"key":"delta"});a.decoder.decode(r)}else if(n&&0===e[1]){const t=15&e[0],r=(postMessage({cmd:re,code:t}),function(e){let r=e.subarray(1,4),n="avc1.";for(let t=0;t<3;t++){let e=r[t].toString(16);e.length<2&&(e="0"+e),n+=e}return{codec:n,description:e}}(e.slice(5)));postMessage({cmd:ne,buffer:e,codecId:t},[e.buffer]),a.decoder.configure(r),a.hasInit=!0}},reset(){a.hasInit=!1,a.isEmitInfo=!1,a.offscreenCanvas=null,a.offscreenCanvasCtx=null}}),{init(){C.lastBuf=null,C.vps=null,C.sps=null,C.pps=null,C.streamType=null,C.localDts=0,C.isSendSeqHeader=!1},destroy(){C.lastBuf=null,C.vps=null,C.sps=null,C.pps=null,C.streamType=null,C.localDts=0,C.isSendSeqHeader=!1},dispatch(e){e=new Uint8Array(e);C.extractNALu$2(e)},getNaluDts(){var e=C.localDts;return C.localDts=C.localDts+40,e},getNaluAudioDts(){var e=F._opt.sampleRate,t=F._opt.audioBufferSize;return C.localDts+parseInt(t/e*1e3)},extractNALu(e){let t,r,n=0,o=e.byteLength,a=0,i=[];for(;n<o;)switch(t=e[n++],a){case 0:0===t&&(a=1);break;case 1:a=0===t?2:0;break;case 2:case 3:a=0===t?3:(1===t&&n<o&&(r&&i.push(e.subarray(r,n-a-1)),r=n),0)}return r&&i.push(e.subarray(r,o)),i},extractNALu$2(t){let a=null;if(t&&!(t.byteLength<1)){C.lastBuf?((a=new Uint8Array(t.byteLength+C.lastBuf.length)).set(C.lastBuf),a.set(new Uint8Array(t),C.lastBuf.length)):a=new Uint8Array(t);let r=0,n=-1,o=-2;var i=new Array;for(let t=0;t<a.length;t+=2){const e=a[t],s=a[t+1];0==n&&0==e&&0==s?i.push(t-1):1==s&&0==e&&0==n&&0==o&&i.push(t-2),o=e,n=s}if(1<i.length)for(let t=0;t<i.length-1;++t){const e=a.subarray(i[t],i[t+1]+1);C.handleNALu(e),r=i[t+1]}else r=i[0];if(0!=r&&r<a.length)C.lastBuf=a.subarray(r);else{C.lastBuf||(C.lastBuf=a);const e=new Uint8Array(C.lastBuf.length+t.byteLength);e.set(C.lastBuf),e.set(new Uint8Array(t),C.lastBuf.length),C.lastBuf=e}}},handleNALu(e){e.byteLength<=4?F.debug.warn("worker",`handleNALu nalu byteLength is ${e.byteLength} <= 4`):(e=e.slice(4),C.handleVideoNalu(e))},handleVideoNalu(e){if(C.streamType||(C.streamType=function(e){let t=null,r=31&e[0];return t=(t=r!==G&&r!==O?t:ae)||(r=(126&e[0])>>1)!==V&&r!==z&&r!==j?t:ie}(e)),C.streamType===ae){var t=C.handleAddNaluStartCode(e),t=C.extractNALu(t);if(0===t.length)F.debug.warn("worker","handleVideoNalu","naluList.length === 0");else{const r=[];if(t.forEach(e=>{var t=Y(e);t===O||t===G?C.handleVideoH264Nalu(e):we(t)&&r.push(e)}),1===r.length)C.handleVideoH264Nalu(r[0]);else{const e=function(t){if(0===t.length)return!1;var r=Y(t[0]);for(let e=1;e<t.length;e++)if(r!==Y(t[e]))return!1;return!0}(r);if(e){const e=Y(r[0]),n=ve(e);C.handleVideoH264NaluList(r,n,e)}else r.forEach(e=>{C.handleVideoH264Nalu(e)})}}}else C.streamType===ie&&(ke(e)===j?C.extractH265PPS(e):C.handleVideoH265Nalu(e))},extractH264PPS(e){e=C.handleAddNaluStartCode(e);C.extractNALu(e).forEach(e=>{_e(Y(e))?C.extractH264SEI(e):C.handleVideoH264Nalu(e)})},extractH265PPS(e){e=C.handleAddNaluStartCode(e);C.extractNALu(e).forEach(e=>{39===ke(e)?C.extractH265SEI(e):C.handleVideoH265Nalu(e)})},extractH264SEI(e){e=C.handleAddNaluStartCode(e);C.extractNALu(e).forEach(e=>{C.handleVideoH264Nalu(e)})},extractH265SEI(e){e=C.handleAddNaluStartCode(e);C.extractNALu(e).forEach(e=>{C.handleVideoH265Nalu(e)})},handleAddNaluStartCode(e){var t=[0,0,0,1],r=new Uint8Array(e.length+t.length);return r.set(t),r.set(e,t.length),r},handleVideoH264Nalu(e){var t,r,n,o,a,i=Y(e);switch(i){case G:C.sps=e;break;case O:C.pps=e}if(C.isSendSeqHeader)we(i)?(t=ve(i),r=C.getNaluDts(),t&&F.calcIframeIntervalTimestamp(r),F.calcNetworkDelay(r),n=e,(o=[])[0]=t?23:39,o[1]=1,o[2]=0,o[3]=0,o[4]=0,o[5]=n.byteLength>>24&255,o[6]=n.byteLength>>16&255,o[7]=n.byteLength>>8&255,o[8]=255&n.byteLength,(a=new Uint8Array(o.length+n.byteLength)).set(o,0),a.set(n,o.length),n=a,F.decode(n,{type:2,ts:r,isIFrame:t,cts:0})):F.debug.warn("work","handleVideoH264Nalu Avc Seq Head is "+i);else if(C.sps&&C.pps){C.isSendSeqHeader=!0;const e=function(){var{sps:e,pps:t}={sps:C.sps,pps:C.pps},r=[23,0,0,0,0,1,66,0,30,255];return r[0]=23,r[6]=e[1],r[7]=e[2],r[8]=e[3],r[10]=225,r[11]=e.byteLength>>8&255,r[12]=255&e.byteLength,r.push(...e,1,t.byteLength>>8&255,255&t.byteLength,...t),new Uint8Array(r)}();F.decode(e,{type:2,ts:0,isIFrame:!0,cts:0})}},handleVideoH264NaluList(e,t,r){var n,o,a,i;C.isSendSeqHeader?(n=C.getNaluDts(),F.calcNetworkDelay(n),o=e.reduce((e,t)=>{var e=ye(e),t=ye(t),r=new Uint8Array(e.byteLength+t.byteLength);return r.set(e,0),r.set(t,e.byteLength),r}),(a=[])[0]=t?23:39,a[1]=1,a[2]=0,a[3]=0,a[4]=0,(i=new Uint8Array(a.length+o.byteLength)).set(a,0),i.set(o,a.length),o=i,F.decode(o,{type:2,ts:n,isIFrame:t,cts:0}),F.debug.log("worker",`handleVideoH264NaluList list size is ${e.length} package length is ${o.byteLength} isIFrame is ${t},nalu type is ${r}, dts is `+n)):F.debug.warn("worker","handleVideoH264NaluList isSendSeqHeader is false")},handleVideoH265Nalu(e){var t,r,n,o,a=ke(e);switch(a){case V:C.vps=e;break;case z:C.sps=e;break;case j:C.pps=e}if(C.isSendSeqHeader)32<=a&&a<=40?F.debug.warn("work","handleVideoH265Nalu HevcSeqHead is "+a):(a=16<=a&&a<=21,t=C.getNaluDts(),F.calcNetworkDelay(t),r=e,(n=[])[0]=a?28:44,n[1]=1,n[2]=0,n[3]=0,n[4]=0,n[5]=r.byteLength>>24&255,n[6]=r.byteLength>>16&255,n[7]=r.byteLength>>8&255,n[8]=255&r.byteLength,(o=new Uint8Array(n.length+r.byteLength)).set(n,0),o.set(r,n.length),r=o,a&&F.calcIframeIntervalTimestamp(t),F.decode(r,{type:2,ts:t,isIFrame:a,cts:0}));else if(C.vps&&C.sps&&C.pps){C.isSendSeqHeader=!0;const e=function(e){var{vps:e,pps:t,sps:r}=e,n={configurationVersion:1},o=(e=>{e=be(e),e=new X(e);return e.readByte(),e.readByte(),e.readBits(4),e.readBits(2),e.readBits(6),{num_temporal_layers:e.readBits(3)+1,temporal_id_nested:e.readBool()}})(e),a=(e=>{let t=be(e),a=new X(t),r=(a.readByte(),a.readByte(),0),n=0,o=0,i=0;a.readBits(4);var s=a.readBits(3),e=(a.readBool(),a.readBits(2)),l=a.readBool(),u=a.readBits(5),d=a.readByte(),c=a.readByte(),f=a.readByte(),p=a.readByte(),h=a.readByte(),m=a.readByte(),g=a.readByte(),I=a.readByte(),M=a.readByte(),$=a.readByte(),y=a.readByte(),_=[],N=[];for(let e=0;e<s;e++)_.push(a.readBool()),N.push(a.readBool());if(0<s)for(let e=s;e<8;e++)a.readBits(2);for(let e=0;e<s;e++)_[e]&&(a.readByte(),a.readByte(),a.readByte(),a.readByte(),a.readByte(),a.readByte(),a.readByte(),a.readByte(),a.readByte(),a.readByte(),a.readByte()),_[e]&&a.readByte();a.readUEG();var w,v=a.readUEG(),G=(3==v&&a.readBits(1),a.readUEG()),O=a.readUEG(),b=(a.readBool()&&(r+=a.readUEG(),n+=a.readUEG(),o+=a.readUEG(),i+=a.readUEG()),a.readUEG()),V=a.readUEG(),z=a.readUEG();for(let e=a.readBool()?0:s;e<=s;e++)a.readUEG(),a.readUEG(),a.readUEG();if(a.readUEG(),a.readUEG(),a.readUEG(),a.readUEG(),a.readUEG(),a.readUEG(),a.readBool()&&a.readBool())for(let t=0;t<4;t++)for(let e=0;e<(3===t?2:6);e++)if(a.readBool()){var j=Math.min(64,1<<4+(t<<1));1<t&&a.readSEG();for(let e=0;e<j;e++)a.readSEG()}else a.readUEG();a.readBool(),a.readBool(),a.readBool()&&(a.readByte(),a.readUEG(),a.readUEG(),a.readBool());let E=a.readUEG(),k=0;for(let t=0;t<E;t++){let e=!1;if(e=0!==t?a.readBool():e){t===E&&a.readUEG(),a.readBool(),a.readUEG();let r=0;for(let e=0;e<=k;e++){let e=a.readBool(),t=!1;e||(t=a.readBool()),(e||t)&&r++}k=r}else{var T=a.readUEG(),B=a.readUEG();k=T+B;for(let e=0;e<T;e++)a.readUEG(),a.readBool();for(let e=0;e<B;e++)a.readUEG(),a.readBool()}}if(a.readBool()){var W=a.readUEG();for(let e=0;e<W;e++){for(let e=0;e<z+4;e++)a.readBits(1);a.readBits(1)}}let A=0,D=1,S=1,C=!1,F=1,U=1;if(a.readBool(),a.readBool(),a.readBool()){if(a.readBool()&&(0<(w=a.readByte())&&w<16?(D=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][w-1],S=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][w-1]):255===w&&(D=a.readBits(16),S=a.readBits(16))),a.readBool()&&a.readBool(),a.readBool()&&(a.readBits(3),a.readBool(),a.readBool())&&(a.readByte(),a.readByte(),a.readByte()),a.readBool()&&(a.readUEG(),a.readUEG()),a.readBool(),a.readBool(),a.readBool(),a.readBool()&&(r+=a.readUEG(),n+=a.readUEG(),o+=a.readUEG(),i+=a.readUEG()),a.readBool()&&(F=a.readBits(32),U=a.readBits(32),a.readBool())&&(a.readUEG(),a.readBool())){let r,n,o=!1;r=a.readBool(),n=a.readBool(),(r||n)&&((o=a.readBool())&&(a.readByte(),a.readBits(5),a.readBool(),a.readBits(5)),a.readBits(4),a.readBits(4),o&&a.readBits(4),a.readBits(5),a.readBits(5),a.readBits(5));for(let e=0;e<=s;e++){var P=a.readBool();C=P;let e=!1,t=!1;if((e=P?e:a.readBool())?a.readSEG():t=a.readBool(),t||(cpbcnt=a.readUEG()+1),r)for(let e=0;e<1;e++)a.readUEG(),a.readUEG(),o&&(a.readUEG(),a.readUEG());if(n)for(let e=0;e<1;e++)a.readUEG(),a.readUEG(),o&&(a.readUEG(),a.readUEG())}}a.readBool()&&(a.readBool(),a.readBool(),a.readBool(),A=a.readUEG(),a.readUEG(),a.readUEG(),a.readUEG(),a.readUEG())}a.readBool();let H=`hvc1.${u}.1.L${y}.B0`,x=G,L=O,R=1;return 1!==D&&1!==S&&(R=D/S),a.destroy(),a=null,{codec_mimetype:H,level_string:(y/30).toFixed(1),profile_idc:u,bit_depth:b+8,ref_frames:1,chroma_format:v,chroma_format_string:Ee(v),general_level_idc:y,general_profile_space:e,general_tier_flag:l,general_profile_idc:u,general_profile_compatibility_flags_1:d,general_profile_compatibility_flags_2:c,general_profile_compatibility_flags_3:f,general_profile_compatibility_flags_4:p,general_constraint_indicator_flags_1:h,general_constraint_indicator_flags_2:m,general_constraint_indicator_flags_3:g,general_constraint_indicator_flags_4:I,general_constraint_indicator_flags_5:M,general_constraint_indicator_flags_6:$,min_spatial_segmentation_idc:A,constant_frame_rate:0,chroma_format_idc:v,bit_depth_luma_minus8:b,bit_depth_chroma_minus8:V,frame_rate:{fixed:C,fps:U/F,fps_den:F,fps_num:U},sar_ratio:{width:D,height:S},codec_size:{width:x,height:L},present_size:{width:x*R,height:L}}})(r),i=(e=>{e=be(e),e=new X(e);e.readByte(),e.readByte(),e.readUEG(),e.readUEG(),e.readBool(),e.readBool(),e.readBits(3),e.readBool(),e.readBool(),e.readUEG(),e.readUEG(),e.readSEG(),e.readBool(),e.readBool(),e.readBool()&&e.readUEG(),e.readSEG(),e.readSEG(),e.readBool(),e.readBool(),e.readBool(),e.readBool();let t=e.readBool(),r=e.readBool(),n=1;return r&&t?n=0:r?n=3:t&&(n=2),{parallelismType:n}})(t),n=Object.assign(n,o,a,i),o=23+(5+e.byteLength)+(5+r.byteLength)+(5+t.byteLength),i=((a=new Uint8Array(o))[0]=1,a[1]=(3&n.general_profile_space)<<6|(n.general_tier_flag?1:0)<<5|31&n.general_profile_idc,a[2]=n.general_profile_compatibility_flags_1||0,a[3]=n.general_profile_compatibility_flags_2||0,a[4]=n.general_profile_compatibility_flags_3||0,a[5]=n.general_profile_compatibility_flags_4||0,a[6]=n.general_constraint_indicator_flags_1||0,a[7]=n.general_constraint_indicator_flags_2||0,a[8]=n.general_constraint_indicator_flags_3||0,a[9]=n.general_constraint_indicator_flags_4||0,a[10]=n.general_constraint_indicator_flags_5||0,a[11]=n.general_constraint_indicator_flags_6||0,a[12]=60,a[13]=240|(3840&n.min_spatial_segmentation_idc)>>8,a[14]=255&n.min_spatial_segmentation_idc,a[15]=252|3&n.parallelismType,a[16]=252|3&n.chroma_format_idc,a[17]=248|7&n.bit_depth_luma_minus8,a[18]=248|7&n.bit_depth_chroma_minus8,a[19]=0,a[20]=0,a[21]=(3&n.constant_frame_rate)<<6|(7&n.num_temporal_layers)<<3|(n.temporal_id_nested?1:0)<<2|3,a[22]=3,a[23]=128|V,a[24]=0,a[25]=1,a[26]=(65280&e.byteLength)>>8,a[27]=(255&e.byteLength)>>0,a.set(e,28),a[23+(5+e.byteLength)+0]=128|z,a[23+(5+e.byteLength)+1]=0,a[23+(5+e.byteLength)+2]=1,a[23+(5+e.byteLength)+3]=(65280&r.byteLength)>>8,a[23+(5+e.byteLength)+4]=(255&r.byteLength)>>0,a.set(r,23+(5+e.byteLength)+5),a[23+(5+e.byteLength+5+r.byteLength)+0]=128|j,a[23+(5+e.byteLength+5+r.byteLength)+1]=0,a[23+(5+e.byteLength+5+r.byteLength)+2]=1,a[23+(5+e.byteLength+5+r.byteLength)+3]=(65280&t.byteLength)>>8,a[23+(5+e.byteLength+5+r.byteLength)+4]=(255&t.byteLength)>>0,a.set(t,23+(5+e.byteLength+5+r.byteLength)+5),[28,0,0,0,0]);return(o=new Uint8Array(i.length+a.byteLength)).set(i,0),o.set(a,i.length),o}({vps:C.vps,sps:C.sps,pps:C.pps});F.decode(e,{type:2,ts:0,isIFrame:!0,cts:0})}}}),F={isPlayer:!0,isPlayback:!1,isPushDropping:!1,isDestroyed:!1,_opt:S(),startStreamRateAndStatsInterval:function(){F.stopStreamRateAndStatsInterval(),e=setInterval(()=>{s&&s(0);var e=JSON.stringify({demuxBufferDelay:F.getVideoBufferLength(),audioDemuxBufferDelay:F.getAudioBufferLength(),flvBufferByteLength:F.getFlvBufferLength(),netBuf:F.networkDelay||0,pushLatestDelay:F.pushLatestDelay||0,isDropping:fe(F.dropping)||fe(F.isPushDropping),isStreamTsMoreThanLocal:g});postMessage({cmd:I,type:"streamStats",value:e})},1e3)},stopStreamRateAndStatsInterval:function(){e&&(clearInterval(e),e=null)},useOffscreen:function(){return F._opt.useOffscreen&&"undefined"!=typeof OffscreenCanvas},getDelay:function(e){var t,r;return!e||F._opt.hasVideo&&!m?-1:(F.preDelayTimestamp&&F.preDelayTimestamp>e?1e3<F.preDelayTimestamp-e&&F.debug.warn("worker",`getDelay() and preDelayTimestamp is ${F.preDelayTimestamp} > timestamp is ${e} more than ${F.preDelayTimestamp-e}ms`):F.firstTimestamp?e&&(t=Date.now()-F.startTimestamp,(r=e-F.firstTimestamp)<=t?(g=!1,F.delay=t-r):(g=!0,F.delay=r-t)):(F.firstTimestamp=e,F.startTimestamp=Date.now(),F.delay=-1),F.preDelayTimestamp=e,F.delay)},getDelayNotUpdateDelay:function(t){if(t&&(!F._opt.hasVideo||m))if(F.preDelayTimestamp&&1e3<F.preDelayTimestamp-t)F.debug.warn("worker",`getDelayNotUpdateDelay and preDelayTimestamp is ${F.preDelayTimestamp} > timestamp is ${t} more than ${F.preDelayTimestamp-t}ms`);else if(F.firstTimestamp){let e=-1;var r;return t&&(r=Date.now()-F.startTimestamp,t=t-F.firstTimestamp,e=t<=r?(g=!1,r-t):(g=!0,t-r)),e}return-1},resetDelay:function(){F.firstTimestamp=null,F.startTimestamp=null,F.delay=-1},resetAllDelay:function(){F.resetDelay(),F.preDelayTimestamp=null},doDecode:function(e){F._opt.useWCS&&F.useOffscreen()&&2===e.type&&a.decode?a.decode(e.payload,e.ts,e.cts):e.decoder.decode(e.payload,e.ts,e.isIFrame,e.cts)},init:function(){F.debug.log("worker","init and opt is",F._opt);const t=F._opt.playType===x,r="playbackTF"===F._opt.playType;if(C.init(),F.isPlayer=t,F.isPlayback=r,F.isPlaybackCacheBeforeDecodeForFpsRender())F.debug.log("worker","playback and playbackIsCacheBeforeDecodeForFpsRender is true");else{const r=()=>{let e=null;if(o.length)if(F.isPushDropping)F.debug.warn("worker","loop() isPushDropping is true and bufferList length is "+this.bufferList.length);else if(F.dropping){for(e=o.shift(),F.debug.warn("worker",`loop() dropBuffer is dropping and isIFrame ${e.isIFrame} and delay is ${F.delay} and bufferlist is `+o.length);!e.isIFrame&&o.length;)e=o.shift();const t=F.getDelayNotUpdateDelay(e.ts);e.isIFrame&&t<=F.getNotDroppingDelayTs()&&(F.debug.log("worker","loop() is dropping = false, is iFrame"),F.dropping=!1,F.doDecode(e))}else if(e=o[0],-1===F.getDelay(e.ts)||F.isPlayback)F.isPlayer&&F.debug.log("worker","loop() common dumex delay is -1 ,data.ts is",e.ts),o.shift(),F.doDecode(e);else if(F.delay>F._opt.videoBuffer+F._opt.videoBufferDelay&&t)F.hasIframeInBufferList()?(F.debug.log("worker",`delay is ${F.delay}, set dropping is true`),F.resetAllDelay(),F.dropping=!0):(o.shift(),F.doDecode(e));else for(;o.length;){if(e=o[0],!(F.getDelay(e.ts)>F._opt.videoBuffer)){F.delay<0&&F.debug.warn("worker",`loop() do not decode and delay is ${F.delay}, bufferList is `+o.length);break}o.shift(),F.doDecode(e)}else-1!==F.delay&&F.debug.log("worker","loop() bufferList is empty and reset delay"),F.resetAllDelay()};F.stopId=setInterval(()=>{var e=(new Date).getTime(),e=e-(y=y||e);100<e&&F.debug.warn("worker","loop demux diff time is "+e),r(),y=(new Date).getTime()},10)}F._opt.checkFirstIFrame||(m=!0)},playbackCacheLoop:function(){F.stopId&&(clearInterval(F.stopId),F.stopId=null);var e=()=>{var e;o.length&&(e=o.shift(),F.doDecode(e))},t=(e(),Math.ceil(1e3/(F.streamFps*F._opt.playbackRate)));F.debug.log("worker",`playbackCacheLoop fragDuration is ${t}, streamFps is ${F.streamFps}, playbackRate is `+F._opt.playbackRate),F.stopId=setInterval(e,t)},close:function(){F.debug.log("worker","close"),F.isDestroyed=!0,F.stopStreamRateAndStatsInterval(),F.stopId&&(clearInterval(F.stopId),F.stopId=null),U.clear&&U.clear(),P.clear&&P.clear(),U=null,P=null,s=null,y=null,g=!1,a&&(a.reset&&a.reset(),a=null),F.firstTimestamp=null,F.startTimestamp=null,F.networkDelay=0,F.streamFps=null,F.streamAudioFps=null,F.streamVideoFps=null,F.delay=-1,F.pushLatestDelay=-1,F.preDelayTimestamp=null,F.dropping=!1,F.isPushDropping=!1,F.isPlayer=!0,F.isPlayback=!1,F._opt=S(),F.webglObj&&(F.webglObj.destroy(),F.offscreenCanvas.removeEventListener("webglcontextlost",F.onOffscreenCanvasWebglContextLost),F.offscreenCanvas.removeEventListener("webglcontextrestored",F.onOffscreenCanvasWebglContextRestored),F.offscreenCanvas=null,F.offscreenCanvasGL=null,F.offscreenCanvasCtx=null),o=[],u=[],n(),d=null,i&&(i.close(),i=null),c=null,f=null,p=!1,h=!1,m=!1,A=!1,D=!1,v=[],b=0,E=0,_=null,w=null,t=null,k=null,T=0,B=0,C.destroy(),postMessage({cmd:"closeEnd"})},pushBuffer:function(e,t){if(1===t.type&&le(e))F.decodeAudio(e,t.ts);else{if(F.isPlayer&&0<T&&0<k&&2===t.type){const e=t.ts-k;e>2*T-5&&F.debug.error("worker",`pushBuffer video
                    ts is ${t.ts}, preTimestamp is ${k},
                    diff is ${e} and preTimestampDuration is ${T}
                    maybe trigger black screen or flower screen
                    `)}if(F.isPlayer&&0<k&&2===t.type&&t.ts<k&&36e5<k-t.ts&&(F.debug.warn("worker",`pushBuffer,
                preTimestamp is ${k}, options.ts is ${t.ts},
                diff is ${k-t.ts} more than 3600000,
                and resetAllDelay`),F.resetAllDelay()),F.isPlayer&&t.ts<=k&&0<k&&2===t.type&&F.debug.warn("worker",`pushBuffer,
                options.ts is ${t.ts} less than (or equal) preTimestamp is ${k} and
                payloadBufferSize is ${e.byteLength} and prevPayloadBufferSize is `+B),F.isPlayer&&m){const e=F._opt.videoBuffer+F._opt.videoBufferDelay,o=F.getDelayNotUpdateDelay(t.ts);(F.pushLatestDelay=o)>e&&F.delay<e&&0<F.delay&&F.hasIframeInBufferList()&&!1===F.isPushDropping&&(F.debug.log("worker",`pushBuffer, pushLatestDelay is ${o} more than ${e} and decoder.delay is ${F.delay} and has iIframe and next decoder.dropBuffer$2()`),F.dropBuffer$2())}if(2===t.type&&(0<k&&(T=t.ts-k),B=e.byteLength,k=t.ts),1===t.type?o.push({ts:t.ts,payload:e,decoder:{decode:F.decodeAudio},type:1,isIFrame:!1}):2===t.type&&o.push({ts:t.ts,cts:t.cts,payload:e,decoder:{decode:F.decodeVideo},type:2,isIFrame:t.isIFrame}),F.isPlaybackCacheBeforeDecodeForFpsRender()&&(H(F.streamVideoFps)||H(F.streamAudioFps))){let e=F.streamVideoFps,t=F.streamAudioFps;H(F.streamVideoFps)&&(e=ce(o,2))&&(F.streamVideoFps=e,postMessage({cmd:"playbackStreamVideoFps",value:F.streamVideoFps}),F.streamFps=t?e+t:e,F.playbackCacheLoop()),H(F.streamAudioFps)&&(t=ce(o,1))&&(F.streamAudioFps=t,F.streamFps=e?e+t:t,F.playbackCacheLoop()),H(F.streamVideoFps)&&H(F.streamAudioFps)&&F.debug.log("worker",`playbackCacheBeforeDecodeForFpsRender, calc streamAudioFps is ${t}, streamVideoFps is ${e}, bufferListLength  is `+o.length)}}},getVideoBufferLength(){let t=0;return o.forEach(e=>{2===e.type&&(t+=1)}),t},hasIframeInBufferList:()=>o.some(e=>2===e.type&&e.isIFrame),getNotDroppingDelayTs:()=>F._opt.videoBuffer+F._opt.videoBufferDelay/2,getAudioBufferLength(){let t=0;return o.forEach(e=>{1===e.type&&(t+=1)}),t},getFlvBufferLength(){let e=0;return d&&d.buffer&&(e=d.buffer.byteLength),e=F._opt.isNakedFlow&&C.lastBuf?C.lastBuf.byteLength:e},fetchStream:function(e,t){F.debug.log("worker","fetchStream, url is "+e,"options:",JSON.stringify(t)),s=function(r){let n=0,o=de();return e=>{var t;"[object Number]"===Object.prototype.toString.call(e)&&(n+=e,1e3<=(t=(e=de())-o))&&(r(n/t*1e3),o=e,n=0)}}(e=>{postMessage({cmd:I,type:"streamRate",value:e})}),F.startStreamRateAndStatsInterval(),2===t.protocol?(d=new q(F.demuxFlv()),fetch(e,{signal:r.signal}).then(e=>{if(e.ok&&200<=e.status&&e.status<=299)if(postMessage({cmd:I,type:oe}),"undefined"!=typeof WritableStream)e.body.pipeTo(new WritableStream({write:e=>{s(e.byteLength),d.write(e)},close:()=>{d=null,n(),postMessage({cmd:I,type:M,value:Z})},abort:e=>{d=null,e.name!==W&&(n(),postMessage({cmd:I,type:$,value:e.toString()}))}}));else{const t=e.body.getReader(),r=()=>{t.read().then(e=>{var{done:e,value:t}=e;e?(d=null,n(),postMessage({cmd:I,type:M,value:Z})):(s(t.byteLength),d.write(t),r())}).catch(e=>{d=null,e.name!==W&&(n(),postMessage({cmd:I,type:$,value:e.toString()}))})};r()}else F.debug.error("worker",`fetch response status is ${e.status} and ok is ${e.ok} and emit error and next abort()`),n(),postMessage({cmd:I,type:$,value:`fetch response status is ${e.status} and ok is `+e.ok})}).catch(e=>{e.name!==W&&(n(),postMessage({cmd:I,type:$,value:e.toString()}),d=null)})):1===t.protocol&&(t.isFlv&&(d=new q(F.demuxFlv())),(i=new WebSocket(e)).binaryType="arraybuffer",i.onopen=()=>{F.debug.log("worker","fetchStream, WebsocketStream  socket open"),postMessage({cmd:I,type:oe}),postMessage({cmd:I,type:"websocketOpen"})},i.onclose=()=>{F.debug.log("worker","fetchStream, WebsocketStream socket close"),d=null,postMessage({cmd:I,type:M,value:"websocket"})},i.onerror=e=>{F.debug.error("worker","fetchStream, WebsocketStream socket error",e),d=null,postMessage({cmd:I,type:"websocketError",value:e.isTrusted?"websocket user aborted":"websocket error"})},i.onmessage=e=>{s(e.data.byteLength),t.isFlv?d.write(e.data):F._opt.isNakedFlow?F.demuxNakedFlow(e.data):F.demuxM7s(e.data)})},demuxFlv:function*(){yield 9;const r=new ArrayBuffer(4),e=new Uint8Array(r),n=new Uint32Array(r);for(;;){e[3]=0;const r=yield 15,i=r[4];e[0]=r[7],e[1]=r[6],e[2]=r[5];var t=n[0],o=(e[0]=r[10],e[1]=r[9],e[2]=r[8],e[3]=r[11],n[0]),a=(yield t).slice();switch(i){case 8:F.decode(a,{type:1,ts:o});break;case 9:if(0<a.byteLength){const r=a[0]>>4==1;F.isPlayer&&(F.calcNetworkDelay(o),r)&&F.calcIframeIntervalTimestamp(o),n[0]=a[4],n[1]=a[3],n[2]=a[2],n[3]=0;let e=n[0],t=a;F._opt.isCrypto&&(F._opt.cryptoIV&&0<F._opt.cryptoIV.byteLength&&F._opt.cryptoKey&&0<F._opt.cryptoKey.byteLength?t=function(t,r,n){r=new Uint8Array(r),n=new Uint8Array(n);const o=t.byteLength;let a=5;for(;a<o;){s=(s=t.slice(a,a+4))[3]|s[2]<<8|s[1]<<16|s[0]<<24;if(s>o)break;var i=t[a+4];if(1==(i&=31)||5==i){const o=t.slice(a+4+2,a+4+s);let e=new Ve.ModeOfOperation.ctr(r,new Ve.Counter(n));i=e.decrypt(o);e=null,t.set(i,a+4+2)}a=a+4+s}var s;return t}(a,F._opt.cryptoKey,F._opt.cryptoIV):F.debug.error("worker",`cryptoKey.length is ${F._opt.cryptoKey&&F._opt.cryptoKey.byteLength} or cryptoIV.length is ${F._opt.cryptoIV&&F._opt.cryptoIV.byteLength} null`)),F.decode(t,{type:2,ts:o,isIFrame:r,cts:e})}break;default:F.debug.log("worker","demuxFlv() type is "+i)}}},decode:function(e,t){1===t.type?F._opt.hasAudio&&(postMessage({cmd:I,type:"streamAbps",value:e.byteLength}),F.isPlayer?F.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts}):F.isPlayback&&(F.isPlaybackCacheBeforeDecodeForFpsRender()||1===F._opt.playbackRate?F.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts}):F.decodeAudio(e,t.ts))):2===t.type&&F._opt.hasVideo&&(postMessage({cmd:I,type:"streamVbps",value:e.byteLength}),postMessage({cmd:I,type:"streamDts",value:t.ts}),F.isPlayer?F.pushBuffer(e,{type:t.type,ts:t.ts,isIFrame:t.isIFrame,cts:t.cts}):F.isPlayback&&(F.isPlaybackOnlyDecodeIFrame()?t.isIFrame&&F.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}):(F.isPlaybackCacheBeforeDecodeForFpsRender(),F.pushBuffer(e,{type:t.type,ts:t.ts,cts:t.cts,isIFrame:t.isIFrame}))))},setCodecAudio:function(e){var t=e[0]>>4;le(e)||7==t||8==t?(F.debug.log("worker","setCodecAudio: init audio codec, codeId is "+t),e=t===N?e.slice(2):e.slice(1),U.setCodec(t,F._opt.sampleRate,e),h=!0):F.debug.warn("worker","setCodecAudio: hasInitAudioCodec is false, codecId is ",t)},decodeAudio:function(e,t){var r=e[0]>>4;h?U.decode(r===N?e.slice(2):e.slice(1),t):F.setCodecAudio(e)},setCodecVideo:function(e){var t=15&e[0];if(F.isVideoSequenceHeader(e))if(7==t||12==t){F.debug.log("worker","setCodecVideo: init video codec , codecId is "+t);var r=e.slice(5);if(7==t&&F._opt.useSIMD){const e=ge(r);if(4080<e.codecWidth||4080<e.codecHeight)return postMessage({cmd:"simdH264DecodeVideoWidthIsTooLarge"}),void F.debug.warn("worker",`setCodecVideo: SIMD H264 decode video width is too large, width is ${e.codecWidth}, height is `+e.codecHeight)}p=!0,P.setCodec(t,r),F._opt.recordType===L&&postMessage({cmd:ne,buffer:e,codecId:t},[e.buffer])}else F.debug.warn("worker",`setCodecVideo: hasInitVideoCodec is false, codecId is ${t} is not H264 or H265`);else F.debug.warn("worker",`decodeVideo: hasInitVideoCodec is false, codecId is ${t} and frameType is ${e[0]>>4} and packetType is `+e[1])},decodeVideo:function(t,r,e,n){if(p)if(m=!m&&e?!0:m){if(e&&F.isVideoSequenceHeader(t)){const r=15&t[0];let e={};7==r?e=ge(t.slice(5)):12==r&&(e=function(e){var n={width:0,height:0,profile:0,level:0};e=e.slice(5);do{var o={};if(e.length<23)break;if(o.configurationVersion=e[0],1!=o.configurationVersion)break;o.general_profile_space=e[1]>>6&3,o.general_tier_flag=e[1]>>5&1,o.general_profile_idc=31&e[1],o.general_profile_compatibility_flags=e[2]<<24|e[3]<<16|e[4]<<8|e[5],o.general_constraint_indicator_flags=e[6]<<24|e[7]<<16|e[8]<<8|e[9],o.general_constraint_indicator_flags=o.general_constraint_indicator_flags<<16|e[10]<<8|e[11],o.general_level_idc=e[12],o.min_spatial_segmentation_idc=(15&e[13])<<8|e[14],o.parallelismType=3&e[15],o.chromaFormat=3&e[16],o.bitDepthLumaMinus8=7&e[17],o.bitDepthChromaMinus8=7&e[18],o.avgFrameRate=e[19]<<8|e[20],o.constantFrameRate=e[21]>>6&3,o.numTemporalLayers=e[21]>>3&7,o.temporalIdNested=e[21]>>2&1,o.lengthSizeMinusOne=3&e[21];let t=e[22],r=e.slice(23);for(let e=0;e<t&&!(r.length<3);e++){var a=63&r[0],i=r[1]<<8|r[2];r=r.slice(3);for(let e=0;e<i&&!(r.length<2);e++){var s,l=r[0]<<8|r[1];if(r.length<2+l)break;r=r.slice(2),33==a&&((s=new Uint8Array(l)).set(r.slice(0,l),0),o.psps=function(e){var t={},r=e.length,n=[],o=new me(e);o.read(1),o.read(6),o.read(6),o.read(3);for(let e=2;e<r;e++)e+2<r&&3==o.look(24)?(n.push(o.read(8)),n.push(o.read(8)),e+=2,o.read(8)):n.push(o.read(8));var a,i,e=new Uint8Array(n),e=new me(e);return t.sps_video_parameter_set_id=e.read(4),t.sps_max_sub_layers_minus1=e.read(3),t.sps_temporal_id_nesting_flag=e.read(1),t.profile_tier_level=function(t,r){var n={};n.profile_space=t.read(2),n.tier_flag=t.read(1),n.profile_idc=t.read(5),n.profile_compatibility_flags=t.read(32),n.general_progressive_source_flag=t.read(1),n.general_interlaced_source_flag=t.read(1),n.general_non_packed_constraint_flag=t.read(1),n.general_frame_only_constraint_flag=t.read(1),t.read(32),t.read(12),n.level_idc=t.read(8),n.sub_layer_profile_present_flag=[],n.sub_layer_level_present_flag=[];for(let e=0;e<r;e++)n.sub_layer_profile_present_flag[e]=t.read(1),n.sub_layer_level_present_flag[e]=t.read(1);if(0<r)for(let e=r;e<8;e++)t.read(2);n.sub_layer_profile_space=[],n.sub_layer_tier_flag=[],n.sub_layer_profile_idc=[],n.sub_layer_profile_compatibility_flag=[],n.sub_layer_progressive_source_flag=[],n.sub_layer_interlaced_source_flag=[],n.sub_layer_non_packed_constraint_flag=[],n.sub_layer_frame_only_constraint_flag=[],n.sub_layer_level_idc=[];for(let e=0;e<r;e++)n.sub_layer_profile_present_flag[e]&&(n.sub_layer_profile_space[e]=t.read(2),n.sub_layer_tier_flag[e]=t.read(1),n.sub_layer_profile_idc[e]=t.read(5),n.sub_layer_profile_compatibility_flag[e]=t.read(32),n.sub_layer_progressive_source_flag[e]=t.read(1),n.sub_layer_interlaced_source_flag[e]=t.read(1),n.sub_layer_non_packed_constraint_flag[e]=t.read(1),n.sub_layer_frame_only_constraint_flag[e]=t.read(1),t.read(32),t.read(12)),n.sub_layer_level_present_flag[e]?n.sub_layer_level_idc[e]=t.read(8):n.sub_layer_level_idc[e]=1;return n}(e,t.sps_max_sub_layers_minus1),t.sps_seq_parameter_set_id=e.read_golomb(),t.chroma_format_idc=e.read_golomb(),t.separate_colour_plane_flag=3==t.chroma_format_idc?e.read(1):0,t.pic_width_in_luma_samples=e.read_golomb(),t.pic_height_in_luma_samples=e.read_golomb(),t.conformance_window_flag=e.read(1),t.conformance_window_flag?(a=1+(t.chroma_format_idc<2),i=1+(t.chroma_format_idc<3),t.conf_win_left_offset=e.read_golomb()*i,t.conf_win_right_offset=e.read_golomb()*i,t.conf_win_top_offset=e.read_golomb()*a,t.conf_win_bottom_offset=e.read_golomb()*a):(t.conf_win_left_offset=0,t.conf_win_right_offset=0,t.conf_win_top_offset=0,t.conf_win_bottom_offset=0),t}(s),n.profile=o.general_profile_idc,n.level=o.general_level_idc/30,n.width=o.psps.pic_width_in_luma_samples-(o.psps.conf_win_left_offset+o.psps.conf_win_right_offset),n.height=o.psps.pic_height_in_luma_samples-(o.psps.conf_win_top_offset+o.psps.conf_win_bottom_offset)),r=r.slice(l)}}}while(0);return n.codecWidth=n.width||1920,n.codecHeight=n.height||1080,n.presentHeight=n.codecHeight,n.presentWidth=n.codecWidth,n.timescale=1e3,n.refSampleDuration=1e3/23976*1e3,n.videoType="hevc",n}(t)),e.codecWidth&&e.codecHeight&&c&&f&&(e.codecWidth!==c||e.codecHeight!==f)&&(F.debug.warn("worker",`
                            decodeVideo: video width or height is changed,
                            old width is ${c}, old height is ${f},
                            new width is ${e.codecWidth}, new height is ${e.codecHeight},
                            and emit change event`),D=!0,postMessage({cmd:"wasmWidthOrHeightChange"}))}var o;D?F.debug.warn("worker","decodeVideo: video width or height is changed, and return"):F.isVideoSequenceHeader(t)?F.debug.warn("worker","decodeVideo and payload is video sequence header so drop this frame"):t.byteLength<12?F.debug.warn("worker","decodeVideo and payload is too small , payload length is "+t.byteLength):(o=t.slice(5),P.decode(o,e?1:0,r),F._opt.isRecording&&F._opt.recordType===L&&postMessage({cmd:"videoNalu",buffer:o,isIFrame:e,ts:r,cts:n},[t.buffer]))}else F.debug.warn("worker","decodeVideo: first frame is not iframe");else F.setCodecVideo(t)},clearBuffer:function(){var e=0<arguments.length&&void 0!==arguments[0]&&arguments[0];F.debug.log("worker",`clearBuffer,bufferList length is ${o.length}, need clear is `+e),e&&(o=[]),F.resetAllDelay(),F.dropping=!0},dropBuffer$2:function(){var e,t,r;0<o.length&&(F.isPushDropping=!0,0<=(e=o.findIndex(e=>fe(e.isIFrame)&&2===e.type))?(t=(o=o.slice(e)).shift(),r=F.getDelayNotUpdateDelay(t.ts),F.doDecode(t),F.isPushDropping=!1,F.debug.log("worker",`dropBuffer$2() iFrameIndex is ${e},and bufferList length is ${o.length} and tempDelay is ${r} ,delay is ${F.delay} `)):F.isPushDropping=!1),0===o.length&&(F.isPushDropping=!1)},demuxM7s:function(e){var t,r=new DataView(e),n=r.getUint32(1,!1);switch(r.getUint8(0)){case 1:F.decode(new Uint8Array(e,5),{type:1,ts:n});break;case 2:5<r.byteLength&&(t=r.getUint8(5)>>4==1,F.isPlayer&&(F.calcNetworkDelay(n),t)&&F.calcIframeIntervalTimestamp(n),F.decode(new Uint8Array(e,5),{type:2,ts:n,isIFrame:t}))}},demuxNakedFlow:function(e){C.dispatch(e)},calcNetworkDelay:function(e){var t,r,n;m&&0<e&&(null===_?(_=e,w=ue()):e<_&&(F.debug.warn("worker",`calcNetworkDelay, dts is ${e} less than bufferStartDts is `+_),_=e,w=ue()),n=(t=e-_)<(r=ue()-w)?r-t:0,(F.networkDelay=n)>F._opt.networkDelay)&&F._opt.playType===x&&(F.debug.warn("worker",`calcNetworkDelay now dts:${e}, start dts is ${_} vs start is ${t},local diff is ${r} ,delay is `+n),postMessage({cmd:I,type:"networkDelayTimeout",value:n}))},calcIframeIntervalTimestamp:function(e){null===t?t=e:t<e&&(postMessage({cmd:"iframeIntervalTs",value:e-t}),t=e)},canVisibilityDecodeNotDrop:function(){return F._opt.visibility&&c*f<=2073600},isPlaybackCacheBeforeDecodeForFpsRender:function(){return!F.isPlayer&&F._opt.playbackIsCacheBeforeDecodeForFpsRender},isPlaybackOnlyDecodeIFrame:function(){return F._opt.playbackRate>=F._opt.playbackForwardMaxRateDecodeIFrame},playbackUpdatePlaybackRate:function(){F.clearBuffer(!0)},isVideoSequenceHeader:function(e){return e[0]>>4==1&&e[1]===se},onOffscreenCanvasWebglContextLost:function(e){F.debug.error("worker","handleOffscreenCanvasWebglContextLost and next try to create webgl"),e.preventDefault(),A=!0,F.webglObj.destroy(),F.webglObj=null,F.offscreenCanvasGL=null,setTimeout(()=>{F.offscreenCanvasGL=F.offscreenCanvas.getContext("webgl"),F.offscreenCanvasGL&&F.offscreenCanvasGL.getContextAttributes().stencil?(F.webglObj=J(F.offscreenCanvasGL,F._opt.openWebglAlignment),A=!1):F.debug.error("worker","handleOffscreenCanvasWebglContextLost, stencil is false")},500)},onOffscreenCanvasWebglContextRestored:function(e){F.debug.log("worker","handleOffscreenCanvasWebglContextRestored"),e.preventDefault()},videoInfo:function(e,t,r){postMessage({cmd:re,code:e}),postMessage({cmd:ee,w:t,h:r}),c=t,f=r,F.useOffscreen()&&(F.offscreenCanvas=new OffscreenCanvas(t,r),F.offscreenCanvasGL=F.offscreenCanvas.getContext("webgl"),F.webglObj=J(F.offscreenCanvasGL,F._opt.openWebglAlignment),F.offscreenCanvas.addEventListener("webglcontextlost",F.onOffscreenCanvasWebglContextLost,!1),F.offscreenCanvas.addEventListener("webglcontextrestored",F.onOffscreenCanvasWebglContextRestored,!1))},audioInfo:function(e,t,r){postMessage({cmd:"audioCode",code:e}),postMessage({cmd:"initAudio",sampleRate:t,channels:r}),E=r},yuvData:function(e,t){if(F.isDestroyed)F.debug.log("worker","yuvData, decoder is destroyed and return");else{var r,n=c*f*3/2,e=l.HEAPU8.subarray(e,e+n),n=new Uint8Array(e);if(F.useOffscreen())try{A||(F.webglObj.renderYUV(c,f,n),r=F.offscreenCanvas.transferToImageBitmap(),postMessage({cmd:R,buffer:r,delay:F.delay,ts:t},[r]))}catch(e){F.debug.error("worker","yuvData, transferToImageBitmap error is",e)}else postMessage({cmd:R,output:n,delay:F.delay,ts:t},[n.buffer])}},pcmData:function(o,a,i){if(F.isDestroyed)F.debug.log("worker","pcmData, decoder is destroyed and return");else{let t=a,r=[],e=0,n=F._opt.audioBufferSize;for(let e=0;e<2;e++){var s=l.HEAPU32[(o>>2)+e]>>2;r[e]=l.HEAPF32.subarray(s,s+t)}if(b){if(!(t>=(a=n-b)))return b+=t,u[0]=Float32Array.of(...u[0],...r[0]),void(2==E&&(u[1]=Float32Array.of(...u[1],...r[1])));v[0]=Float32Array.of(...u[0],...r[0].subarray(0,a)),2==E&&(v[1]=Float32Array.of(...u[1],...r[1].subarray(0,a))),postMessage({cmd:te,buffer:v,ts:i},v.map(e=>e.buffer)),e=a,t-=a}for(b=t;b>=n;b-=n)v[0]=r[0].slice(e,e+=n),2==E&&(v[1]=r[1].slice(e-n,e)),postMessage({cmd:te,buffer:v,ts:i},v.map(e=>e.buffer));b&&(u[0]=r[0].slice(e),2==E)&&(u[1]=r[1].slice(e))}},sendWebsocketMessage:function(e){i?1===i.readyState?i.send(e):F.debug.error("worker","socket is not open"):F.debug.error("worker","socket is null")},timeEnd:function(){postMessage({cmd:"workerEnd"})}},U=(F.debug=new he(F),new l.AudioDecoder(F)),P=new l.VideoDecoder(F);postMessage({cmd:"init"}),self.onmessage=function(e){var t=e.data;switch(t.cmd){case"init":try{F._opt=Object.assign(F._opt,JSON.parse(t.opt))}catch(e){}F.init();break;case"decode":F.pushBuffer(t.buffer,t.options);break;case"audioDecode":F.decodeAudio(t.buffer,t.ts);break;case"videoDecode":F.decodeVideo(t.buffer,t.ts,t.isIFrame);break;case"clearBuffer":F.clearBuffer(t.needClear);break;case"fetchStream":F.fetchStream(t.url,JSON.parse(t.opt));break;case"close":F.close();break;case"updateConfig":F.debug.log("worker","updateConfig",t.key,t.value),F._opt[t.key]=t.value,"playbackRate"===t.key&&(F.playbackUpdatePlaybackRate(),F.isPlaybackCacheBeforeDecodeForFpsRender())&&F.playbackCacheLoop();break;case"sendWsMessage":F.sendWebsocketMessage(t.message)}}}Date.now||(Date.now=function(){return(new Date).getTime()}),_.postRun=function(){ze(_)}});
