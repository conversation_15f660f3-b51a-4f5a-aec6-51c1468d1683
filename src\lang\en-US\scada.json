{"scada.center.indeieScada.373453-0": "name", "scada.center.indeieScada.373453-1": "Please enter the page name", "scada.center.indeieScada.373453-2": "Switch cards/lists", "scada.center.indeieScada.373453-3": "unknown", "scada.center.indeieScada.373453-4": "Configuration Cover", "scada.center.indeieScada.373453-5": "Configuration Name", "scada.center.indeieScada.373453-6": "resolving power", "scada.center.indeieScada.373453-7": "Please enter a name", "scada.center.indeieScada.373453-8": "Configuration Description", "scada.center.indeieScada.373453-9": "Please enter a description", "scada.center.indeieScada.373453-10": "Please enter the configuration name", "scada.center.indeieScada.373453-11": "Associated Scenarios", "scada.center.indeieScada.373453-12": "Please select the associated scene", "scada.center.indeieScada.373453-13": "Add configuration information", "scada.center.indeieScada.373453-14": "Modifying configuration information", "scada.center.indeieScada.373453-15": "Are you sure to delete the data item with configuration number {0}?", "scada.center.indeieScada.373453-16": "Edit", "scada.center.indeieScada.373453-17": "Design", "scada.center.indeieScada.373453-18": "Running", "scada.center.tempScada.342311-0": "Related products", "scada.center.tempScada.342311-1": "Please select an associated product", "scada.center.tempScada.342311-2": "Edit", "scada.center.tempScada.342311-3": "Design", "scada.center.sceneScada.098325-0": "Edit", "scada.center.sceneScada.098325-1": "Design", "scada.center.sceneScada.098325-2": "Running", "scada.component.302923-0": "working", "scada.component.302923-1": "Saving, please wait", "scada.component.302923-2": "private", "scada.component.302923-3": "public", "scada.component.302923-4": "placeholder ", "scada.component.302923-5": "thumbnail", "scada.component.302923-6": "type", "scada.component.302923-7": "picture", "scada.component.302923-8": "Please select the type", "scada.component.302923-9": "Add component management", "scada.component.302923-10": "Modify component management", "scada.component.302923-11": "Are you sure to delete the data item with component number {0}?", "scada.echart.209302-0": "Add chart management", "scada.echart.209302-1": "Modify Chart Management", "scada.echart.209302-2": "Are you sure to delete the data item with chart number {0}?", "scada.echart.209302-3": "Chart Editing", "scada.echart.209302-4": "Chart Library", "scada.echart.209302-5": "Chart display", "scada.echart.209302-6": "download", "scada.gallery.309456-0": "System Library", "scada.gallery.309456-1": "File Name", "scada.gallery.309456-2": "Please enter the file name", "scada.gallery.309456-3": "upload", "scada.gallery.309456-4": "placeholder ", "scada.gallery.309456-5": "Cloud configuration", "scada.gallery.309456-6": "Please select the type of upload on the left side", "scada.gallery.309456-7": "The size of the uploaded image cannot exceed 20MB!", "scada.gallery.309456-8": "Upload successful", "scada.gallery.309456-9": "Are you sure to delete this icon file?", "scada.gallery.309456-10": "warning", "scada.gallery.309456-11": "file type", "scada.gallery.309456-12": "Please select a file type", "scada.gallery.309456-13": "File Name", "scada.gallery.309456-14": "Please enter the file name", "scada.model.649850-0": "Model Name", "scada.model.649850-1": "Please enter the model name", "scada.model.649850-2": "Whether to abandon", "scada.model.649850-3": "no", "scada.model.649850-4": "yes", "scada.model.649850-5": "Model address", "scada.model.649850-6": "Please select a status", "scada.model.649850-7": "Please enter the model address", "scada.model.649850-8": "Add Model Management", "scada.model.649850-9": "Modify Model Management", "scada.model.649850-10": "Please fill in the correct model URL", "scada.model.649850-11": "Are you sure to delete the data item with model number {0}?", "scada.topo.editor.023345-0": "copy", "scada.topo.editor.023345-1": "Topping", "scada.topo.editor.023345-2": "Bottom placement", "scada.topo.editor.023345-3": "rotate", "scada.topo.editor.023345-4": "Clockwise 90 °", "scada.topo.editor.023345-5": "90 ° counterclockwise", "scada.topo.editor.023345-6": "Horizontal Mirror", "scada.topo.editor.023345-7": "mirror vertically ", "scada.topo.editor.023345-8": "Custom angle", "scada.topo.editor.023345-9": "alignment", "scada.topo.editor.023345-10": "Left aligned", "scada.topo.editor.023345-11": "Right aligned", "scada.topo.editor.023345-12": "Align Up", "scada.topo.editor.023345-13": "bottom justify", "scada.topo.editor.023345-14": "Horizontally equidistant", "scada.topo.editor.023345-15": "Vertical equidistant", "scada.topo.editor.023345-16": "combination", "scada.topo.editor.023345-17": "Ungroup", "scada.topo.editor.023345-18": "Unlock", "scada.topo.editor.023345-19": "locking", "scada.topo.editor.023345-20": "gallery", "scada.topo.editor.023345-21": "Device binding", "scada.topo.editor.023345-22": "revoke", "scada.topo.editor.023345-23": "recovery", "scada.topo.editor.023345-24": "zoom", "scada.topo.editor.023345-25": "help", "scada.topo.editor.023345-26": "gallery", "scada.topo.editor.023345-27": "Clockwise rotation", "scada.topo.editor.023345-28": "Counterclockwise rotation", "scada.topo.editor.023345-29": "Horizontal Mirror", "scada.topo.editor.023345-30": "mirror vertically ", "scada.topo.editor.023345-31": "Custom rotation angle", "scada.topo.editor.023345-32": "The rotation angle cannot be empty", "scada.topo.editor.023345-33": "Angle cannot be empty", "scada.topo.editor.023345-34": "The rotation angle must be a number", "scada.topo.editor.023345-35": "Custom angle", "scada.topo.editor.023345-36": "Please select an image", "scada.topo.editor.023345-37": "Preview it under your device", "scada.topo.editor.023345-38": "Align the horizontal center", "scada.topo.editor.023345-39": "Align the vertical center", "scada.topo.fullscreenShare.034354-0": "Initializing", "scada.topo.fullscreenShare.034354-1": "Password verification", "scada.topo.fullscreenShare.034354-2": "Share password", "scada.topo.fullscreenShare.034354-3": "No sharing yet", "scada.topo.fullscreenShare.034354-4": "The password is incorrect, please re-enter it", "scada.topoMain.320129-0": "Desperately importing", "scada.topoMain.320129-1": "Tip: Only allow importing \"Json\" format files!", "scada.topoMain.320129-2": "Tip: After importing the interface, you need to rebind the device parameters!", "scada.topoMain.320129-3": "Please enter the content", "scada.topoMain.320129-4": "Configuration import", "scada.topoMain.320129-5": "Import Results", "scada.topoMain.320129-6": "Full screen", "scada.topoMain.320129-7": "Reload", "scada.topoMain.320129-8": "The weather component already exists and cannot be added again", "scada.topoMain.320129-9": "Combination component not selected", "scada.topoMain.320129-10": "animation", "scada.topoMain.320129-11": "click ", "scada.topoMain.320129-12": "Component color", "scada.topoMain.320129-13": "Filter rendering", "scada.topoMain.320129-14": "Component filling", "scada.topoMain.320129-15": "Parameter binding", "scada.topoMain.320129-16": "Do you want to save the current page and switch to another page?", "scada.topoMain.320129-17": "prompt", "scada.topoMain.320129-18": "Switching, please wait", "scada.topoMain.320129-19": "Do you want to delete this page? Once deleted, it will not be restored!", "scada.topoMain.320129-20": "Unknown Page", "scada.topoMain.320129-21": "Saving, please wait", "scada.topoMain.320129-22": "Please wait", "scada.topoMain.320129-23": "The system may not save your changes.", "scada.topoMain.320129-24": "Leave this page?", "scada.topoMain.320129-25": "leave", "scada.topoMain.320129-26": "Deleting, please wait", "scada.topoMain.320129-27": "Unknown Page", "topo.topoRender.038944-0": "Please add components to the configuration panel", "topo.topoRender.038944-1": "Instruction is being issued", "topo.topoRender.038944-2": "Sub interface", "topo.topoRender.038944-3": "Kind reminder", "topo.topoRender.038944-4": "Press F11 or right-click on the keyboard to turn on full screen, and press Esc to exit full screen.", "topo.topoRender.038944-5": "Image generation", "topo.topoRender.038944-6": "Full screen display", "topo.topoRender.038944-7": "Generating, please wait", "topo.topoRender.038944-8": "Please enter password", "topo.topoRender.038944-9": "Password cannot be empty", "topo.topoRender.038944-10": "Please enter the correct password", "topo.topoRender.038944-11": "click ", "topo.topoRender.038944-12": "External links", "topo.topoRender.038944-13": "Configuration interface", "topo.topoRender.038944-14": "Unbound configuration interface", "topo.topoRender.038944-15": "Operational variables", "topo.topoRender.038944-16": "Tip: Please enter the variable value", "topo.topoRender.038944-17": "Tip:", "topo.topoRender.038944-18": "Variable value cannot be empty", "topo.topoRender.038944-19": "Variable value must be a number", "topo.topoRender.038944-20": "The device is not online and cannot be set!", "topo.topoRender.038944-21": "Command issued successfully", "topo.topoSelectImage.034642-0": "My favorites", "topo.topoSelectImage.034642-1": "System Library", "topo.topoSelectImage.034642-2": "File Name", "topo.topoSelectImage.034642-3": "Please enter the file name", "topo.topoSelectImage.034642-4": "collection", "topo.topoSelectImage.034642-5": "upload", "topo.topoSelectImage.034642-6": "Collection successful", "topo.topoSelectImage.034642-7": "The size of the uploaded image cannot exceed 5MB!", "topo.topoSelectImage.034642-8": "Are you sure to delete this icon file?", "topo.topoSelectImage.034642-9": "warning", "topo.topoToolbox.250932-0": "Basic components", "topo.topoToolbox.250932-1": "Basic shape", "topo.topoToolbox.250932-2": "Statistical graphics", "topo.topoToolbox.250932-3": "Library components", "topo.topoToolbox.250932-4": "Chart component", "topo.topoToolbox.250932-5": "More components", "topo.topoToolbox.250932-6": "in", "topo.topoToolbox.250932-7": "Variable identification", "topo.topoToolbox.250932-8": "Upload time", "topo.topoToolbox.250932-9": "remove", "topo.topoToolbox.250932-10": "device management", "topo.topoToolbox.250932-11": "Are you sure you want to remove this associated device", "topo.topoToolbox.250932-12": "Removal successful", "scada.topo.components.chart.view-chart.073848-0": "Double click on the bound variable", "scada.topo.components.chart.view-chart.073848-1": "Variable binding", "scada.topo.components.chart.view-chart.073848-2": "When comparing multiple attributes, the reporting time for multiple attributes must be the same!", "scada.topo.components.chart.view-chart.073848-3": "Please bind the device or create a physical model for the device first", "scada.topo.components.chart.view-chart.073848-4": "Confirm", "scada.topo.components.chart.view-chart.073848-5": "Cancel", "scada.topo.components.chart.view-chart.073848-6": "Please select properties for the same device!", "scada.topo.components.chart.view-chart.073848-7": "Binding variable success", "topo.components.chart.073848-5": "Outermost layer", "topo.components.chart.073848-6": "Inner gradient zone", "topo.components.chart.073848-7": "Middle layer", "topo.components.chart.073848-8": "custom", "topo.components.chart.073848-9": "Run successfully", "topo.components.chart.073848-10": "Chart initialization failed, please check the code view!", "topo.components.chart.073848-13": "Please enter the correct URL!", "topo.components.chart.073848-14": "Please enter the correct JSON data", "topo.components.chart.073848-15": "Alarm status", "topo.components.chart.073848-16": "Gateway device", "topo.components.chart.073848-17": "Alarm", "topo.components.chart.073848-18": "Processed", "topo.components.chart.073848-19": "Unprocessed", "topo.components.chart.073848-20": "Work Order Status", "topo.components.chart.073848-21": "Inspected", "topo.components.chart.073848-22": "Not inspected", "topo.components.chart.073848-23": "obsolete ", "topo.components.chart.073848-24": "Transparent box", "topo.components.chart.073848-25": "circular", "topo.components.chart.073848-26": "White circle", "topo.components.chart.073848-27": "name", "topo.components.chart.073848-28": "Please enter the correct JSON data", "topo.components.chart.073848-29": "Run successfully", "topo.components.chart.073848-30": "Chart initialization failed, please check the code view!", "topo.three.028394-0": "Capacity: 100 tons", "topo.three.028394-1": "Temperature: 30 ° C", "topo.three.028394-2": "Warehouse height -20 meters", "topo.three.028394-3": "Beam height -15.8 meters", "topo.three.028394-4": "granary", "topo.three.028394-5": "Granary 02", "topo.three.028394-6": "Granary 03", "topo.three.028394-7": "Granary 04", "topo.three.028394-8": "Granary 11", "topo.three.028394-9": "Granary 12", "topo.three.028394-10": "Granary 13", "topo.three.028394-11": "Granary 05", "topo.three.028394-12": "Granary 06", "topo.three.028394-13": "Granary 07", "topo.three.028394-14": "Granary 08", "topo.three.028394-15": "Granary 09", "topo.three.028394-16": "Granary 010", "topo.three.028394-17": "Granary 14", "topo.three.028394-18": "Granary 15", "topo.three.028394-19": "Granary 16", "topo.three.028394-20": "Bottom panel", "topo.components.data-toolbox.302495-0": "basic", "topo.components.data-toolbox.302495-1": "panel", "topo.components.data-toolbox.302495-2": "Configuration interface", "topo.components.data-toolbox.302495-3": "click ", "topo.components.data-toolbox.302495-4": "Component color", "topo.components.data-toolbox.302495-5": "animation", "topo.components.topoProperties.038495-0": "Component Style", "topo.components.topoProperties.038495-1": "Data binding", "topo.components.topoProperties.038495-2": "Position and size", "topo.components.topoProperties.038495-3": "X coordinate", "topo.components.topoProperties.038495-4": "Y coordinate", "topo.components.topoProperties.038495-5": "width", "topo.components.topoProperties.038495-6": "height", "topo.components.topoProperties.038495-7": "basic style", "topo.components.topoProperties.038495-8": "Component Name", "topo.components.topoProperties.038495-9": "Please fill in the component name", "topo.components.topoProperties.038495-10": "minimum value", "topo.components.topoProperties.038495-11": "Please fill in the minimum value", "topo.components.topoProperties.038495-12": "Maximum value", "topo.components.topoProperties.038495-13": "Please fill in the maximum value", "topo.components.topoProperties.038495-14": "Scale interval", "topo.components.topoProperties.038495-15": "Please fill in the scale interval", "topo.components.topoProperties.038495-16": "Weather Style", "topo.components.topoProperties.038495-17": "Please select weather style", "topo.components.topoProperties.038495-18": "Complete mode", "topo.components.topoProperties.038495-19": "reduction mode ", "topo.components.topoProperties.038495-20": "Map Theme", "topo.components.topoProperties.038495-21": "Please select a map theme", "topo.components.topoProperties.038495-22": "Default Theme", "topo.components.topoProperties.038495-23": "Fresh Blue", "topo.components.topoProperties.038495-24": "night", "topo.components.topoProperties.038495-25": "<PERSON>", "topo.components.topoProperties.038495-26": "Simplify", "topo.components.topoProperties.038495-27": "Natural Green", "topo.components.topoProperties.038495-28": "Midnight Blue", "topo.components.topoProperties.038495-29": "Romantic fans", "topo.components.topoProperties.038495-30": "Youth Green", "topo.components.topoProperties.038495-31": "Fresh blue-green", "topo.components.topoProperties.038495-32": "High end gray wind", "topo.components.topoProperties.038495-33": "Strong boundary", "topo.components.topoProperties.038495-34": "Layer Height ", "topo.components.topoProperties.038495-35": "Please fill in the layer height", "topo.components.topoProperties.038495-36": "background color ", "topo.components.topoProperties.038495-37": "transparency", "topo.components.topoProperties.038495-38": "Border rounded corners", "topo.components.topoProperties.038495-39": "Shadow length", "topo.components.topoProperties.038495-40": "Shadow color", "topo.components.topoProperties.038495-41": "Statistical type", "topo.components.topoProperties.038495-42": "Equipment statistics", "topo.components.topoProperties.038495-43": "Please select the statistical type", "topo.components.topoProperties.038495-44": "Device status", "topo.components.topoProperties.038495-45": "Alarm status", "topo.components.topoProperties.038495-46": "Image selection", "topo.components.topoProperties.038495-47": "Image path", "topo.components.topoProperties.038495-48": "Upload images", "topo.components.topoProperties.038495-49": "written words", "topo.components.topoProperties.038495-50": "Please fill in the text", "topo.components.topoProperties.038495-51": "Alignment method", "topo.components.topoProperties.038495-52": "Please select alignment method", "topo.components.topoProperties.038495-53": "be at the left side", "topo.components.topoProperties.038495-54": "Centered", "topo.components.topoProperties.038495-55": "be at the right", "topo.components.topoProperties.038495-56": "Font type", "topo.components.topoProperties.038495-57": "Please select a font type", "topo.components.topoProperties.038495-58": "font size", "topo.components.topoProperties.038495-59": "Please fill in the font size", "topo.components.topoProperties.038495-60": "Filter/Shadow", "topo.components.topoProperties.038495-61": "Filter rendering", "topo.components.topoProperties.038495-62": "Filter", "topo.components.topoProperties.038495-63": "shadow", "topo.components.topoProperties.038495-64": "Component color", "topo.components.topoProperties.038495-65": "Component Hiding", "topo.components.topoProperties.038495-66": "Please select the component to display or hide", "topo.components.topoProperties.038495-67": "display", "topo.components.topoProperties.038495-68": "hide", "topo.components.topoProperties.038495-69": "Code View", "topo.components.topoProperties.038495-70": "edit", "topo.components.topoProperties.038495-71": "Map selection", "topo.components.topoProperties.038495-72": "Map files", "topo.components.topoProperties.038495-73": "Please select a map", "topo.components.topoProperties.038495-74": "<PERSON><PERSON>", "topo.components.topoProperties.038495-75": "Macao", "topo.components.topoProperties.038495-76": "Beijing", "topo.components.topoProperties.038495-77": "Chongqing", "topo.components.topoProperties.038495-78": "Fujian", "topo.components.topoProperties.038495-79": "Gansu", "topo.components.topoProperties.038495-80": "Guangdong", "topo.components.topoProperties.038495-81": "Guangxi", "topo.components.topoProperties.038495-82": "Guizhou", "topo.components.topoProperties.038495-83": "Hainan", "topo.components.topoProperties.038495-84": "Hebei Province", "topo.components.topoProperties.038495-85": "Heilongjiang", "topo.components.topoProperties.038495-86": "Henan Province", "topo.components.topoProperties.038495-87": "Hubei Province", "topo.components.topoProperties.038495-88": "Hunan", "topo.components.topoProperties.038495-89": "Jiangsu Province", "topo.components.topoProperties.038495-90": "Jiangxi", "topo.components.topoProperties.038495-91": "<PERSON><PERSON>", "topo.components.topoProperties.038495-92": "Liaoning", "topo.components.topoProperties.038495-93": "Inner Mongolia", "topo.components.topoProperties.038495-94": "Ningxia", "topo.components.topoProperties.038495-95": "Qinghai Province", "topo.components.topoProperties.038495-96": "Shandong Province", "topo.components.topoProperties.038495-97": "Shanghai", "topo.components.topoProperties.038495-98": "Shanxi", "topo.components.topoProperties.038495-99": "Sichuan", "topo.components.topoProperties.038495-100": "Taiwan", "topo.components.topoProperties.038495-101": "Tianjin", "topo.components.topoProperties.038495-102": "Hong Kong", "topo.components.topoProperties.038495-103": "Xinjiang", "topo.components.topoProperties.038495-104": "Xizang", "topo.components.topoProperties.038495-105": "Yunnan", "topo.components.topoProperties.038495-106": "Zhejiang", "topo.components.topoProperties.038495-107": "custom", "topo.components.topoProperties.038495-108": "Map data", "topo.components.topoProperties.038495-109": "Please fill in the map data address", "topo.components.topoProperties.038495-110": "Chart refresh", "topo.components.topoProperties.038495-111": "Suspension prompt", "topo.components.topoProperties.038495-112": "Suspension mode", "topo.components.topoProperties.038495-113": "Long term display", "topo.components.topoProperties.038495-114": "Short display", "topo.components.topoProperties.038495-115": "Suspension position", "topo.components.topoProperties.038495-116": "Please select the floating position", "topo.components.topoProperties.038495-117": "Above", "topo.components.topoProperties.038495-118": "Upper Left", "topo.components.topoProperties.038495-119": "Up Right", "topo.components.topoProperties.038495-120": "Lower edge", "topo.components.topoProperties.038495-121": "Lower Left", "topo.components.topoProperties.038495-122": "Lower Right", "topo.components.topoProperties.038495-123": "left", "topo.components.topoProperties.038495-124": "Upper Left", "topo.components.topoProperties.038495-125": "Lower left", "topo.components.topoProperties.038495-126": "right", "topo.components.topoProperties.038495-127": "Upper right", "topo.components.topoProperties.038495-128": "lower right", "topo.components.topoProperties.038495-129": "<PERSON><PERSON><PERSON>", "topo.components.topoProperties.038495-130": "bright", "topo.components.topoProperties.038495-131": "Statistics column", "topo.components.topoProperties.038495-132": "Please select a statistics column", "topo.components.topoProperties.038495-133": "Line width", "topo.components.topoProperties.038495-134": "Please fill in the line width", "topo.components.topoProperties.038495-135": "Line height", "topo.components.topoProperties.038495-136": "Please fill in the line height", "topo.components.topoProperties.038495-137": "Line spacing", "topo.components.topoProperties.038495-138": "Please fill in the line spacing", "topo.components.topoProperties.038495-139": "Line shape", "topo.components.topoProperties.038495-140": "Please select a line shape", "topo.components.topoProperties.038495-141": "rectangle", "topo.components.topoProperties.038495-142": "ellipse", "topo.components.topoProperties.038495-143": "Flow direction", "topo.components.topoProperties.038495-144": "Please select the flow direction", "topo.components.topoProperties.038495-145": "Forward", "topo.components.topoProperties.038495-146": "reverse", "topo.components.topoProperties.038495-147": "static", "topo.components.topoProperties.038495-148": "Flow velocity", "topo.components.topoProperties.038495-149": "fast", "topo.components.topoProperties.038495-150": "in", "topo.components.topoProperties.038495-151": "slow", "topo.components.topoProperties.038495-152": "Number of anchor points", "topo.components.topoProperties.038495-153": "Please fill in the number of anchor points", "topo.components.topoProperties.038495-154": "Liquid level shape", "topo.components.topoProperties.038495-155": "Please select the liquid level shape", "topo.components.topoProperties.038495-156": "container", "topo.components.topoProperties.038495-157": "rotundity", "topo.components.topoProperties.038495-158": "rectangle", "topo.components.topoProperties.038495-159": "Rounded rectangle", "topo.components.topoProperties.038495-160": "triangle", "topo.components.topoProperties.038495-161": "diamond", "topo.components.topoProperties.038495-162": "Hot gas sphere", "topo.components.topoProperties.038495-163": "Inverted triangle", "topo.components.topoProperties.038495-164": "Wave color", "topo.components.topoProperties.038495-165": "Border width", "topo.components.topoProperties.038495-166": "Please fill in the border width", "topo.components.topoProperties.038495-167": "Border color", "topo.components.topoProperties.038495-168": "Border background color", "topo.components.topoProperties.038495-169": "Table Style", "topo.components.topoProperties.038495-170": "Rows", "topo.components.topoProperties.038495-171": "Please fill in the number of rows", "topo.components.topoProperties.038495-172": "Font color", "topo.components.topoProperties.038495-173": "Table header background color", "topo.components.topoProperties.038495-174": "Odd row background color", "topo.components.topoProperties.038495-175": "Even row background color", "topo.components.topoProperties.038495-176": "Rotation time interval", "topo.components.topoProperties.038495-177": "Please fill in the broadcast interval", "topo.components.topoProperties.038495-178": "Meter head height", "topo.components.topoProperties.038495-179": "Please fill in the header height", "topo.components.topoProperties.038495-180": "Meter head width", "topo.components.topoProperties.038495-181": "Please fill in the width of the header", "topo.components.topoProperties.038495-182": "Is line number displayed", "topo.components.topoProperties.038495-183": "display", "topo.components.topoProperties.038495-184": "hide", "topo.components.topoProperties.038495-185": "Rotation method", "topo.components.topoProperties.038495-186": "Please select the rotation method", "topo.components.topoProperties.038495-187": "that 's ok", "topo.components.topoProperties.038495-188": "page", "topo.components.topoProperties.038495-189": "Parameter binding", "topo.components.topoProperties.038495-190": "State type", "topo.components.topoProperties.038495-191": "Please select a status", "topo.components.topoProperties.038495-192": "Variable state", "topo.components.topoProperties.038495-193": "Device status", "topo.components.topoProperties.038495-194": "Please select a device", "topo.components.topoProperties.038495-195": "Please select a variable", "topo.components.topoProperties.038495-196": "data source", "topo.components.topoProperties.038495-197": "Configuration interface", "topo.components.topoProperties.038495-198": "Please select the configuration interface", "topo.components.topoProperties.038495-199": "Firefly cloud", "topo.components.topoProperties.038495-200": "Equipment serial number", "topo.components.topoProperties.038495-201": "Obtaining from the Yingshi Cloud Console", "topo.components.topoProperties.038495-202": "Channel number", "topo.components.topoProperties.038495-203": "Live video", "topo.components.topoProperties.038495-204": "Video streaming URL", "topo.components.topoProperties.038495-205": "Flv format live streaming", "topo.components.topoProperties.038495-206": "Universal Video", "topo.components.topoProperties.038495-207": "Video address", "topo.components.topoProperties.038495-208": "MP4 video address", "topo.components.topoProperties.038495-209": "Cover address", "topo.components.topoProperties.038495-210": "Video Cover Address", "topo.components.topoProperties.038495-211": "three-dimensional", "topo.components.topoProperties.038495-212": "3D scene", "topo.components.topoProperties.038495-213": "Please contact business personnel to obtain", "topo.components.topoProperties.038495-214": "Customize echarts/map files", "topo.components.topoProperties.038495-215": "Engine address", "topo.components.topoProperties.038495-216": "Currently, only GET requests are supported", "topo.components.topoProperties.038495-217": "Response example", "topo.components.topoProperties.038495-218": "copy", "topo.components.topoProperties.038495-219": "request", "topo.components.topoProperties.038495-220": "event", "topo.components.topoProperties.038495-221": "Component password", "topo.components.topoProperties.038495-222": "Set password", "topo.components.topoProperties.038495-223": "flow", "topo.components.topoProperties.038495-224": "Flow conditions", "topo.components.topoProperties.038495-225": "Please enter a value", "topo.components.topoProperties.038495-226": "Flow direction", "topo.components.topoProperties.038495-227": "Forward", "topo.components.topoProperties.038495-228": "reverse", "topo.components.topoProperties.038495-229": "Animation - Unique Effectiveness", "topo.components.topoProperties.038495-230": "Explicit and implicit", "topo.components.topoProperties.038495-231": "rotate", "topo.components.topoProperties.038495-232": "twinkle", "topo.components.topoProperties.038495-233": "slide", "topo.components.topoProperties.038495-234": "Status switch", "topo.components.topoProperties.038495-235": "condition", "topo.components.topoProperties.038495-236": "Device offline", "topo.components.topoProperties.038495-237": "Device online", "topo.components.topoProperties.038495-238": "<PERSON><PERSON> Disabled", "topo.components.topoProperties.038495-239": "fill color", "topo.components.topoProperties.038495-240": "position", "topo.components.topoProperties.038495-241": "province", "topo.components.topoProperties.038495-242": "Please select a province", "topo.components.topoProperties.038495-243": "city", "topo.components.topoProperties.038495-244": "Please select a city", "topo.components.topoProperties.038495-245": "area", "topo.components.topoProperties.038495-246": "Please select the area", "topo.components.topoProperties.038495-247": "reminder", "topo.components.topoProperties.038495-248": "Only one weather component can exist on the same configuration interface", "topo.components.topoProperties.038495-249": "Background name", "topo.components.topoProperties.038495-250": "Please fill in the background name", "topo.components.topoProperties.038495-251": "Wide resolution", "topo.components.topoProperties.038495-252": "Please fill in the resolution width", "topo.components.topoProperties.038495-253": "High resolution", "topo.components.topoProperties.038495-254": "Please fill in High Resolution", "topo.components.topoProperties.038495-255": "background color ", "topo.components.topoProperties.038495-256": "Background image", "topo.components.topoProperties.038495-257": "Drag and Zoom", "topo.components.topoProperties.038495-258": "open", "topo.components.topoProperties.038495-259": "gallery", "topo.components.topoProperties.038495-260": "Animation settings", "topo.components.topoProperties.038495-261": "Variable Name", "topo.components.topoProperties.038495-262": "Judging conditions", "topo.components.topoProperties.038495-263": "rotate", "topo.components.topoProperties.038495-264": "Animation effects", "topo.components.topoProperties.038495-265": "slide", "topo.components.topoProperties.038495-266": "Sliding configuration", "topo.components.topoProperties.038495-267": "Sliding period", "topo.components.topoProperties.038495-268": "Please enter the sliding cycle", "topo.components.topoProperties.038495-269": "Sliding position", "topo.components.topoProperties.038495-270": "Please enter the offset degree", "topo.components.topoProperties.038495-271": "level", "topo.components.topoProperties.038495-272": "vertical", "topo.components.topoProperties.038495-273": "variable selection ", "topo.components.topoProperties.038495-274": "click setting", "topo.components.topoProperties.038495-275": "action", "topo.components.topoProperties.038495-276": "Operational variables", "topo.components.topoProperties.038495-277": "External links", "topo.components.topoProperties.038495-278": "Configuration interface", "topo.components.topoProperties.038495-279": "Switch control", "topo.components.topoProperties.038495-280": "Write value", "topo.components.topoProperties.038495-281": "Prompt information", "topo.components.topoProperties.038495-282": "Please enter the prompt information", "topo.components.topoProperties.038495-283": "Jump link", "topo.components.topoProperties.038495-284": "Please fill in the link to jump to", "topo.components.topoProperties.038495-285": "Switch control", "topo.components.topoProperties.038495-286": "0 off 1 on", "topo.components.topoProperties.038495-287": "0 on 1 off", "topo.components.topoProperties.038495-288": "Open Method", "topo.components.topoProperties.038495-289": "Current window open", "topo.components.topoProperties.038495-290": "Open a new window", "topo.components.topoProperties.**********": "Pop up small window", "topo.components.topoProperties.**********": "Window width", "topo.components.topoProperties.**********": "Please fill in the window width", "topo.components.topoProperties.**********": "Window height", "topo.components.topoProperties.**********": "Please fill in the window height", "topo.components.topoProperties.**********": "Data engine", "topo.components.topoProperties.**********": "Echarts chart collection - only supports Vue syntax sugar, data engine - echartData", "topo.components.topoProperties.**********": "Please enter a response example", "topo.components.topoProperties.**********": "Running", "topo.components.topoProperties.**********": "Data Engine Example", "topo.components.topoProperties.**********": "Component password", "topo.components.topoProperties.**********": "Set password", "topo.components.topoProperties.**********": "custom", "topo.components.topoProperties.**********": "User password", "topo.components.topoProperties.**********": "Shading", "topo.components.topoProperties.**********": "There is no need to bind data. The videos that have been associated with the device or scene are automatically bound during use", "topo.components.topoProperties.**********": "<PERSON><PERSON>", "scada.topo.components.topo-variable.764509-0": "Serial Number", "scada.topo.components.topo-variable.764509-1": "Please enter the serial number", "scada.topo.components.topo-variable.764509-2": "Data Sources", "scada.topo.components.topo-variable.764509-3": "Please enter data source", "scada.topo.components.topo-variable.764509-4": "Variable Name", "scada.topo.components.topo-variable.764509-5": "Please choose a variable name", "scada.topo.components.topo-variable.764509-6": "Device Name", "scada.topo.components.topo-variable.764509-7": "Product Name", "scada.topo.components.topo-variable.764509-8": "Identifier", "scada.topo.components.topo-variable.764509-9": "Search", "scada.topo.components.topo-variable.764509-10": "Reset"}