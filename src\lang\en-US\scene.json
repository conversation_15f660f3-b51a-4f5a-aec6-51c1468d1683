{"script.349087-0": "Script identification", "script.349087-1": "Please enter the script identifier", "script.349087-2": "Script Name", "script.349087-3": "Please enter the script name", "script.349087-4": "Script Name", "script.349087-5": "Belonging products", "script.349087-6": "Script events", "script.349087-7": "Script Actions", "script.349087-8": "scripting language ", "script.349087-9": "Execution sequence", "script.349087-10": "Script events", "script.349087-11": "Please select a script event", "script.349087-12": "Please select a script action", "script.349087-13": "Script Status", "script.349087-14": "Please select a product", "script.349087-15": "Select product", "script.349087-16": "Script using Groovy engine, view tutorial>>", "script.349087-17": "Verification", "script.349087-18": "The script identifier can only input letters and numbers, and starts with a letter", "script.349087-19": "Script ID cannot be empty", "script.349087-20": "The product cannot be empty", "script.349087-21": "The script name cannot be empty", "script.349087-22": "Script type cannot be empty", "script.349087-23": "Script language cannot be empty", "script.349087-24": "Status cannot be empty", "script.349087-25": "<PERSON> <PERSON>", "script.349087-26": "Modifying Rule Engine Scripts", "script.349087-27": "Are you sure to delete the data item with rule engine script number {0}?", "script.349087-28": "Script Generation", "script.349087-29": "HTTP Service Script", "script.349087-30": "MQTT Bridge Script", "script.349087-31": "Database Storage Script", "script.349087-32": "Access Point", "script.349087-33": "Please select an access point", "script.349087-34": "Select an access point", "script.349087-35": "Access point cannot be empty", "script.349087-36": "Log", "script.349087-37": "Refresh", "script.349087-38": "Close", "script.349087-39": "Reading logs", "script.349087-40": "Log information", "scene.index.670805-0": "Is there an alarm", "scene.index.670805-1": "yes", "scene.index.670805-2": "no", "scene.index.670805-3": "Silent time", "scene.index.670805-4": "minute", "scene.index.670805-5": "Seconds", "scene.index.670805-6": "Delayed execution", "scene.index.670805-7": "Execute once", "scene.index.670805-8": "Scene state", "scene.index.670805-9": "trigger", "scene.index.670805-10": "Trigger conditions:", "scene.index.670805-11": "Please select the triggering condition", "scene.index.670805-12": "Not satisfied", "scene.index.670805-13": "Tip: If the trigger has only one timing, the alarm during the execution of the action is invalid", "scene.index.670805-14": "quantity", "scene.index.670805-15": "Custom CRON", "scene.index.670805-16": "Cron execution expression", "scene.index.670805-17": "Generate expression", "scene.index.670805-18": "Please select the type", "scene.index.670805-19": "Please select the parent object model", "scene.index.670805-20": "Please select an operator", "scene.index.670805-21": "value", "scene.index.670805-22": "Please enter a string", "scene.index.670805-23": "Add trigger", "scene.index.670805-24": "Execute action", "scene.index.670805-25": "Will no longer be executed repeatedly within the set time range", "scene.index.670805-26": "Silence time:", "scene.index.670805-27": "Execution method:", "scene.index.670805-28": "Please select the execution method", "scene.index.670805-29": "Serial (sequential execution)", "scene.index.670805-30": "<PERSON><PERSON><PERSON> (simultaneous execution)", "scene.index.670805-31": "Delay will not be stored, limited to 90 seconds", "scene.index.670805-32": "Delayed execution:", "scene.index.670805-33": "Please select a device", "scene.index.670805-34": "Select device", "scene.index.670805-35": "Please select a product", "scene.index.670805-36": "Select product", "scene.index.670805-37": "Add execution action", "scene.index.670805-38": "Cron expression generator", "scene.index.670805-39": "Monday", "scene.index.670805-40": "Tuesday", "scene.index.670805-41": "Wednesday", "scene.index.670805-42": "Thursday", "scene.index.670805-43": "Friday", "scene.index.670805-44": "Saturday", "scene.index.670805-45": "Sunday", "scene.index.670805-46": "Device triggered", "scene.index.670805-47": "Timed trigger", "scene.index.670805-48": "Product triggering", "scene.index.670805-49": "Custom trigger", "scene.index.670805-50": "attribute", "scene.index.670805-51": "function", "scene.index.670805-52": "event", "scene.index.670805-53": "Device online", "scene.index.670805-54": "Equipment offline", "scene.index.670805-55": "Device Execution", "scene.index.670805-56": "Product Execution", "scene.index.670805-57": "Alarm execution", "scene.index.670805-58": "Alarm recovery", "scene.index.670805-59": "Scene name cannot be empty", "scene.index.670805-60": "Delay 0-90", "scene.index.670805-61": "Delay 0-600", "scene.index.670805-62": "Add scene linkage", "scene.index.670805-63": "Modify scene linkage", "scene.index.670805-64": "No unit", "scene.index.670805-65": "Are you sure to delete the data item with scene linkage number {0}?", "scene.index.670805-66": "Delete successFul", "scene.index.670805-67": "The options and values in the trigger cannot be empty", "scene.index.670805-68": "The inter range value in the trigger cannot be empty", "scene.index.670805-69": "Execution time cannot be empty", "scene.index.670805-70": "Please select the week to be executed", "scene.index.670805-71": "The cron expression cannot be empty", "scene.index.670805-72": "The options and values in the execution action cannot be empty", "scene.index.670805-73": "Modified successfully", "scene.index.670805-74": "New successfully added", "scene.index.670805-75": "Please choose to restore the alarm scenario", "scene.index.670805-76": "Select recovery alarm scenario", "scene.detail.index.209809-0": "Scene Overview", "scene.detail.index.209809-1": "Configuration application", "scene.detail.index.209809-2": "Video surveillance", "scene.detail.index.209809-3": "Loading, please wait", "scene.detail.index.209809-4": "All data sources", "scene.overview.324354-0": "Scene information", "scene.overview.324354-1": "Organization:", "scene.overview.324354-2": "Associated devices:", "scene.overview.324354-3": "Update time:", "scene.overview.324354-4": "Basic scene attributes", "scene.overview.324354-5": "The basic attributes of the scene have not been maintained yet. Please go to the scene model and refer to the process configuration below!", "scene.overview.324354-6": "Overview of variables", "scene.overview.324354-7": "data sources ", "scene.overview.324354-8": "Please select the data source", "scene.overview.324354-9": "Slave name", "scene.overview.324354-10": "Please enter the slave name", "scene.overview.324354-11": "Variable Name", "scene.overview.324354-12": "Please enter the variable name", "scene.overview.324354-13": "Variable ID", "scene.overview.324354-14": "Update time", "scene.overview.324354-15": "Current value", "scene.overview.324354-16": "Historical Query", "scene.overview.324354-17": "Please enter data", "scene.overview.324354-18": "Device not activated", "scene.overview.324354-19": "The device is in a disabled state", "scene.overview.324354-20": "The device is offline", "scene.scada.433893-0": "No configuration currently available", "scene.scada.433893-1": "There are currently no videos available", "scene.edit.202832-0": "Basic information", "scene.edit.202832-1": "Scene Name", "scene.edit.202832-2": "Affiliated institution", "scene.edit.202832-3": "Scene images", "scene.edit.202832-4": "Scenario Description", "scene.edit.202832-5": "Please enter the scene name", "scene.edit.202832-6": "Please select the affiliated institution", "scene.edit.202832-7": "Please enter a scene description", "scene.edit.202832-8": "Scenario configuration", "scene.edit.202832-9": "Equipment configuration", "scene.edit.202832-10": "Input type variable", "scene.edit.202832-11": "Operational variables", "scene.edit.202832-12": "Select device", "scene.edit.202832-13": "Number", "scene.edit.202832-14": "Device Name", "scene.edit.202832-15": "Please select a device", "scene.edit.202832-16": "Variable List", "scene.edit.202832-17": "Enable All", "scene.edit.202832-18": "Enable", "scene.edit.202832-19": "Variable units", "scene.edit.202832-20": "Variable type", "scene.edit.202832-21": "numerical value", "scene.edit.202832-22": "character string", "scene.edit.202832-23": "Storage method", "scene.edit.202832-24": "Do not store", "scene.edit.202832-25": "storage", "scene.edit.202832-26": "Reading and writing methods", "scene.edit.202832-27": "Reading and writing", "scene.edit.202832-28": "read-only", "scene.edit.202832-29": "Please enter variable units", "scene.edit.202832-30": "data type", "scene.edit.202832-31": "Please select a data type", "scene.edit.202832-32": "Default value", "scene.edit.202832-33": "Please enter default values", "scene.edit.202832-34": "cycle time", "scene.edit.202832-35": "Periodic cycle", "scene.edit.202832-36": "In scenarios where the cycle rule applies, a fixed cycle calculation is performed once on a natural day.", "scene.edit.202832-37": "For example, perform the calculation once a day at 07:00 (value range: 07:00 today to 07:00 yesterday)", "scene.edit.202832-38": "each", "scene.edit.202832-39": "Calculate once", "scene.edit.202832-40": "Custom time period", "scene.edit.202832-41": "For scenarios with irregular time periods, set data participation in calculations during the time period.", "scene.edit.202832-42": "For example, calculate once a day from 02:00 to 00:00 the next day.", "scene.edit.202832-43": "day", "scene.edit.202832-44": "week", "scene.edit.202832-45": "month", "scene.edit.202832-46": "to", "scene.edit.202832-47": "On the same day", "scene.edit.202832-48": "The next day", "scene.edit.202832-49": "This week", "scene.edit.202832-50": "This month", "scene.edit.202832-51": "Calculate once", "scene.edit.202832-52": "Add time period", "scene.edit.202832-53": "Variables and calculation formulas", "scene.edit.202832-54": "Support the combination of \"reference variables\", operation symbols or numbers, and display the operation variable data after calculation by the operation formula.", "scene.edit.202832-55": "Operation instructions: Clicking on the preceding number of a referenced variable will automatically fill in the \"Calculation Formula\" input box,", "scene.edit.202832-56": "If the reference variable is \"voltage\", the calculation formula will be filled with \"A\", and manual writing of the uppercase letter A is also supported. The rules for using calculation symbols refer to the following instructions:", "scene.edit.202832-57": "Add: A+B+10", "scene.edit.202832-58": "Minus: A-B-10", "scene.edit.202832-59": "Multiply: A * B * 10", "scene.edit.202832-60": "Except for: A/B/10", "scene.edit.202832-61": "Remainder: A% 10", "scene.edit.202832-62": "Brackets: (A+B) * 10", "scene.edit.202832-63": "Note: Please enter at least one variable number, supporting only a single variable number,", "scene.edit.202832-64": "For example, A is suitable for scenarios with irregular time periods, and sets the data within the time period to participate in the calculation.", "scene.edit.202832-65": "Calculation formula", "scene.edit.202832-66": "variable", "scene.edit.202832-67": "Please select a variable", "scene.edit.202832-68": "Statistical methods", "scene.edit.202832-69": "Please select the statistical method", "scene.edit.202832-70": "Insert Variable", "scene.edit.202832-71": "Storage method", "scene.edit.202832-72": "yes", "scene.edit.202832-73": "no", "scene.edit.202832-74": "Add variables", "scene.edit.202832-75": "Please select a time period", "scene.edit.202832-76": "Please enter the calculation formula", "scene.edit.202832-77": "Please select storage method", "scene.edit.202832-78": "Add variables", "scene.edit.202832-79": "Edit variables", "scene.edit.202832-80": "Please insert variables", "scene.edit.202832-81": "Calculation formula error", "scene.edit.202832-82": "Supports a maximum length of 200 characters", "scene.edit.202832-83": "Please enter the calculation formula", "scene.list.index.079839-0": "Institution name", "scene.list.index.079839-1": "Please select a status", "scene.list.index.079839-2": "Deactivate", "scene.list.index.079839-3": "Number of associated devices", "scene.list.index.079839-4": "Scenario Description", "scene.list.index.079839-5": "Creator", "scene.list.index.079839-6": "Update time", "scene.list.index.079839-7": "Configuration design", "scene.list.index.079839-8": "Operation configuration", "scene.list.index.079839-9": "Upload images", "scene.list.index.079839-10": "Add scene", "scene.list.index.079839-11": "Modifying Scenes", "scene.list.index.079839-12": "Are you sure to delete the data item with scene number {0}?", "scene.list.index.079839-13": "The scene has not yet created a configuration", "scene.list.index.079839-14": "Please select the institution name", "scene.bridgelist.784127-0": "Select Bridge", "scene.bridgelist.784127-1": "Connector Name", "scene.bridgelist.784127-2": "Please enter the connector name", "scene.bridgelist.784127-3": "Search", "scene.bridgelist.784127-4": "Reset", "scene.bridgelist.784127-5": "Select", "scene.bridgelist.784127-6": "Connector Name", "scene.bridgelist.784127-7": "Is it effective?", "scene.bridgelist.784127-8": "Status", "scene.bridgelist.784127-9": "Disconnected", "scene.bridgelist.784127-10": "Connecting", "scene.bridgelist.784127-11": "Bridge Type", "scene.bridgelist.784127-12": "Http Push", "scene.bridgelist.784127-13": "Mqtt Bridge", "scene.bridgelist.784127-14": "Bridge Direction", "scene.bridgelist.784127-15": "Input", "scene.bridgelist.784127-16": "Cancel", "scene.bridgelist.784127-17": "Confirm", "scene.bridgelist.784127-18": "Output"}