{"netty.clients.654908-0": "client", "netty.clients.654908-1": "Please enter the client ID", "netty.clients.654908-2": "Only look at the device side", "netty.clients.654908-3": "MQTT client", "netty.clients.654908-4": "client ID ", "netty.clients.654908-5": "type", "netty.clients.654908-6": "Server side", "netty.clients.654908-7": "Web end", "netty.clients.654908-8": "Mobile end", "netty.clients.654908-9": "Testing end", "netty.clients.654908-10": "Device side", "netty.clients.654908-11": "Connection status", "netty.clients.654908-12": "Connected", "netty.clients.654908-13": "Disconnected", "netty.clients.654908-14": "Heartbeat (seconds)", "netty.clients.654908-15": "Account", "netty.clients.654908-16": "Current subscription quantity", "netty.clients.654908-17": "Connection time", "netty.clients.654908-18": "Kick out", "netty.clients.654908-19": "Subscription List", "netty.clients.654908-20": "theme", "netty.clients.654908-21": "TCP Client ", "netty.clients.654908-22": "Add subscription", "netty.clients.654908-23": "Please enter the theme", "netty.clients.654908-24": "Please select message type", "netty.clients.654908-25": "Add subscription", "netty.clients.654908-26": "Theme cannot be empty", "netty.clients.654908-27": "Are you sure to delete the data item with MQTT client number {0}?", "netty.clients.654908-28": "Are you sure to unsubscribe from the data item with topic {0}?", "netty.clients.654908-29": "Unsubscribed successfully", "netty.clients.654908-30": "Successfully added subscription", "netty.mqtt.564432-0": "Mqtt statistical indicators", "netty.mqtt.564432-1": "send message", "netty.mqtt.564432-2": "receive messages", "netty.mqtt.564432-3": "Certification frequency", "netty.mqtt.564432-4": "Connection count", "netty.mqtt.564432-5": "Subscription count", "netty.mqtt.564432-6": "Received today", "netty.mqtt.564432-7": "Send Today", "netty.mqtt.564432-8": "Mqtt message", "netty.mqtt.564432-9": "Mqtt Message%", "netty.mqtt.564432-10": "Total number of messages sent", "netty.mqtt.564432-11": "Total number of received messages", "netty.mqtt.564432-12": "Mqtt status data", "netty.mqtt.564432-13": "Number of connections", "netty.mqtt.564432-14": "Number of sessions", "netty.mqtt.564432-15": "Subscription quantity", "netty.mqtt.564432-16": "Number of routes", "netty.mqtt.564432-17": "Keep messages", "netty.mqtt.564432-18": "Current quantity", "netty.mqtt.564432-19": "Accumulated total"}