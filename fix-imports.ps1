# 修复后端项目import错误的PowerShell脚本
# 将所有 com.ssac 的import替换为 com.fastbee

param(
    [string]$BackendPath = "H:\fastbee2.5.1\SSAC-smart-v2.5.1-A\springboot"
)

Write-Host "修复后端项目import错误..." -ForegroundColor Green
Write-Host "目标路径: $BackendPath" -ForegroundColor Yellow

# 检查路径是否存在
if (-not (Test-Path $BackendPath)) {
    Write-Host "错误: 路径不存在 $BackendPath" -ForegroundColor Red
    exit 1
}

# 获取所有Java文件
Write-Host "🔍 搜索Java文件..." -ForegroundColor Cyan
$javaFiles = Get-ChildItem -Path $BackendPath -Filter "*.java" -Recurse

Write-Host "📊 找到 $($javaFiles.Count) 个Java文件" -ForegroundColor Yellow

# 统计需要修复的文件
Write-Host "🔍 检查需要修复的文件..." -ForegroundColor Cyan
$filesToFix = @()

foreach ($file in $javaFiles) {
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    if ($content -match "import com\.ssac") {
        $filesToFix += $file
    }
}

Write-Host "📊 需要修复的文件数量: $($filesToFix.Count)" -ForegroundColor Yellow

if ($filesToFix.Count -eq 0) {
    Write-Host "✅ 没有发现需要修复的import错误!" -ForegroundColor Green
    exit 0
}

# 显示需要修复的文件列表
Write-Host "`n📋 需要修复的文件列表:" -ForegroundColor Cyan
foreach ($file in $filesToFix) {
    $relativePath = $file.FullName.Replace($BackendPath, "")
    Write-Host "   $relativePath" -ForegroundColor Gray
}

# 询问用户确认
Write-Host "`n⚠️  即将修复 $($filesToFix.Count) 个文件中的import错误" -ForegroundColor Yellow
$confirm = Read-Host "是否继续? (y/N)"

if ($confirm -ne "y" -and $confirm -ne "Y") {
    Write-Host "❌ 操作已取消" -ForegroundColor Red
    exit 0
}

# 创建备份目录
$backupDir = Join-Path $BackendPath "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
Write-Host "📦 创建备份目录: $backupDir" -ForegroundColor Cyan
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

# 开始修复
$fixedCount = 0
$errorCount = 0

Write-Host "`n🔧 开始修复import错误..." -ForegroundColor Green

foreach ($file in $filesToFix) {
    try {
        Write-Host "🔄 处理: $($file.Name)" -ForegroundColor Cyan
        
        # 备份原文件
        $backupFile = Join-Path $backupDir $file.Name
        Copy-Item $file.FullName $backupFile -Force
        
        # 读取文件内容
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        
        # 执行替换
        $newContent = $content -replace "import com\.ssac", "import com.fastbee"
        
        # 写回文件
        Set-Content -Path $file.FullName -Value $newContent -Encoding UTF8
        
        $fixedCount++
        Write-Host "✅ 已修复: $($file.Name)" -ForegroundColor Green
        
    } catch {
        $errorCount++
        Write-Host "❌ 修复失败: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示结果
Write-Host "`n📊 修复完成!" -ForegroundColor Green
Write-Host "✅ 成功修复: $fixedCount 个文件" -ForegroundColor Green
Write-Host "❌ 修复失败: $errorCount 个文件" -ForegroundColor Red
Write-Host "📦 备份位置: $backupDir" -ForegroundColor Yellow

# 验证修复结果
Write-Host "`n🔍 验证修复结果..." -ForegroundColor Cyan
$remainingErrors = Get-ChildItem -Path $BackendPath -Filter "*.java" -Recurse | Select-String "import com\.ssac"

if ($remainingErrors.Count -eq 0) {
    Write-Host "✅ 所有import错误已修复!" -ForegroundColor Green
} else {
    Write-Host "⚠️  仍有 $($remainingErrors.Count) 个import错误未修复" -ForegroundColor Yellow
    Write-Host "剩余错误文件:" -ForegroundColor Yellow
    foreach ($error in $remainingErrors | Select-Object -First 5) {
        Write-Host "   $($error.Filename):$($error.LineNumber)" -ForegroundColor Gray
    }
}

Write-Host "`n🎉 修复脚本执行完成!" -ForegroundColor Green
