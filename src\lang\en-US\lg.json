{"login.989807-0": "Open source IoT platform", "login.989807-1": "Account", "login.989807-2": "Sms", "login.989807-3": "<PERSON><PERSON>", "login.989807-4": "Username", "login.989807-5": "Password", "login.989807-6": "Code", "login.989807-7": "Remember Me", "login.989807-8": "Please Enter phoneNumber", "login.989807-9": "Please Enter Sms Code", "login.989807-10": "If you already have an account, please directly enter the account to bind it，", "login.989807-11": "If you don't have an account yet, please go first", "login.989807-12": "Register", "login.989807-13": "Logging in", "login.989807-14": "Registering", "login.989807-15": "Bind", "login.989807-16": "Binding", "login.989807-17": "Please enter a valid phone number!", "login.989807-18": "Register an account", "login.989807-19": "Send code", "login.989807-20": "Please enter your account", "login.989807-21": "Please enter your password", "login.989807-22": "Please enter the verification code", "login.989807-23": "Mobile phone number cannot be empty", "login.989807-24": "Please enter the correct phone number", "login.989807-25": "The verification code cannot be empty", "login.989807-26": "Obtained successfully", "login.989807-27": "Send verification code", "login.989807-28": "Acquisition failed", "login.989807-29": "The phone number format is incorrect, please enter the correct phone number!", "login.989807-30": "Get in seconds", "login.989807-31": "Are you sure to log out and exit the system?", "login.989807-32": "prompt", "login.989807-33": "Demo Account：fastbee 123456", "login.989807-34": "<PERSON><PERSON><PERSON> login", "login.989807-35": "Register an account", "login.989807-36": "Register and bind account", "register.platForm": "Open source IoT platform", "register.phone": "Phone Number", "register.confirm": "Confirm password", "register.register": "Register", "register.registering": "Registering", "register.bind": "Registration binding", "register.binding": "Binding...", "register.return": "Return to the official website", "register.view": "Viewing Documents", "register.account": "Account login", "register.inconsistent": "The passwords entered twice are inconsistent", "register.accountCount": "The length of the user account must be between 2 and 20", "register.enterPhone": "Please enter your phone number", "register.phoneLength": "The length of the phone number is 11", "register.passWordlength": "User password length must be between 5 and 20", "register.passwordAgain": "Please enter your password again", "register.congratulations": "Congratulations, your account", "register.registered": "registered successfully!", "register.system": "System prompt", "home.equip": "Equipment statistics", "home.number": "Number of devices", "home.monitoring": "monitoring data", "home.product": "Product quantity", "home.alarm": "Number of alarms", "home.records": "Operation records", "home.reports": "Reporting events", "home.information": "Information bar", "home.announcement": "announcement", "home.message": "Message", "home.close": "Close", "home.serverName": "Server Name", "home.javaName": "Java Name", "home.serverIp": "Server IP", "home.startTime": "Start Time", "home.system": "System", "home.javaVer": "Java Version", "home.architecture": "System Architecture", "home.runtime": "Runtime", "home.core": "CPU core", "home.memory": "Memory usage rate", "home.size": "memory size", "home.JVM": "JVM total memory", "home.onlineDevice": "Device distribution (number of online devices ", "home.deviceName": "DeviceName：", "home.deviceNum": "Equipment Number", "home.deviceStatus": "DeviceStatus：", "home.notActive": "Not Active", "home.disabled": "disAbled", "home.onLine": "onLine", "home.offline": "offline", "home.shadow": "Shadow", "home.enable": "Enable", "home.notEnabled": "Not Enabled", "home.productName": "productName：", "home.version": "Firmware version", "home.position": "Positioning method", "home.automatic": "Automatic positioning", "home.equipment": "Equipment positioning", "home.custom": "Custom Location", "home.unknown": "unknown", "home.location": "Location", "home.usage": "CPU usage rate", "home.rate": "CPU usage rate %", "home.user": "User", "home.free": "Free", "home.memoryRate": "Memory usage rate G", "home.used": "Used", "home.surplus": "surplus", "home.disk": "System disk usage rate", "home.diskStatus": "Disk status G", "home.available": "available", "tagsView.refresh": "Refresh", "tagsView.close": "Close", "tagsView.closeOthers": "Close Others", "tagsView.closeAll": "Close All", "settings.title": "Page style setting", "settings.theme": "Theme Color", "settings.tagsView": "Open Tags-View", "settings.fixedHeader": "Fixed Header", "settings.sidebarLogo": "Sidebar Logo", "navbar.full": "Full Screen", "navbar.language": "Language", "navbar.dashboard": "Dashboard", "navbar.sourceCode": "Source code address", "navbar.document": "Document", "navbar.message": "Message", "navbar.layoutSize": "Layout Size", "navbar.layoutSetting": "Layout Setting", "navbar.personalCenter": "Personal Center", "navbar.logout": "Logout", "home.active": "Activation Time:", "home.logOut": "Are you sure to log out and exit the system?", "home.prompt": "Prompt", "layout.components.settings.index.909453-0": "Customize trigger theme style settings", "layout.components.settings.index.909453-1": "Theme color", "layout.components.settings.index.909453-2": "System layout configuration", "layout.components.settings.index.909453-3": "Open TopNav", "layout.components.settings.index.909453-4": "Open Tags Views", "layout.components.settings.index.909453-5": "Fixed Header", "layout.components.settings.index.909453-6": "Display Logo", "layout.components.settings.index.909453-7": "Dynamic Title", "layout.components.settings.index.909453-8": "Save configuration", "layout.components.settings.index.909453-9": "Reset configuration"}