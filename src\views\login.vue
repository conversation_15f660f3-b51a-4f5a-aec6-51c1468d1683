<template>
    <div class="login">
        <vue-particles
            class="login-bg"
            color="#39AFFD"
            :particleOpacity="0.7"
            :particlesNumber="100"
            shapeType="circle"
            :particleSize="4"
            linesColor="#8DD1FE"
            :linesWidth="1"
            :lineLinked="true"
            :lineOpacity="0.4"
            :linesDistance="150"
            :moveSpeed="3"
            :hoverEffect="true"
            hoverMode="grab"
            :clickEffect="true"
            clickMode="push"
        ></vue-particles>
        <div class="login-top hidden-sm-and-down">
            <h1>物联网平台</h1>
        </div>
        <img class="login-img hidden-sm-and-down" src="../assets/images/tu.png" alt="" />
        <div class="main">
            <!-- <div
              style="
                max-width: 330px;
                text-align: left;
                margin: 0 auto;
                display: none;
              "
            >
              <div v-if="!bindAccount" style="padding: 20px 0">
                <span style="margin-right: 10px">登录方式</span>
                <el-button
                  type="success"
                  title="微信登录"
                  size="mini"
                  @click="authLogin"
                  style="border: 1px solid #fff"
                  disabled
                >
                  <svg-icon icon-class="wechat" /> 微信
                </el-button>
                <el-button
                  type="danger"
                  title="QQ登录"
                  size="mini"
                  @click="qqLogin"
                  style="border: 1px solid #fff"
                  disabled
                >
                  <svg-icon icon-class="qq" /> QQ
                </el-button>
                <el-button
                  type="primary"
                  title="支付宝登录"
                  size="mini"
                  @click="authLogin"
                  style="border: 1px solid #fff"
                  disabled
                >
                  <svg-icon icon-class="zhifubao" /> 支付宝
                </el-button>
              </div>
            </div> -->

            <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
                <h3 class="title" v-if="!bindAccount">账号登录</h3>
                <h3 class="bindAccountTitle" v-else>绑定账户</h3>
                <el-form-item prop="username">
                    <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
                        <svg-icon slot="prefix" icon-class="user" class="input-icon" />
                    </el-input>
                </el-form-item>
                <el-form-item prop="password">
                    <el-input v-model="loginForm.password" type="password" auto-complete="off" placeholder="密码" @keyup.enter.native="handleLogin">
                        <svg-icon slot="prefix" icon-class="password" class="input-icon" />
                    </el-input>
                </el-form-item>
                <el-form-item prop="code" v-if="captchaOnOff">
                    <el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" style="width: 63%" @keyup.enter.native="handleLogin">
                        <svg-icon slot="prefix" icon-class="validCode" class="input-icon" />
                    </el-input>
                    <div class="login-code">
                        <img :src="codeUrl" @click="getCode" />
                    </div>
                </el-form-item>
                <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 25px 0px; color: #c4c3c3">记住密码</el-checkbox>
                <el-form-item style="width: 100%">
                    <el-button v-if="!bindAccount" :loading="loading" type="primary" style="width: 100%" @click.native.prevent="handleLogin">
                        <span v-if="!loading">登 录</span>
                        <span v-else>登 录 中...</span>
                    </el-button>
                    <el-button v-else :loading="loading" type="primary" style="width: 100%" @click.native.prevent="handleLogin">
                        <span v-if="!loading">绑定</span>
                        <span v-else>绑 定 中...</span>
                    </el-button>
                </el-form-item>
            </el-form>
        </div>
        <!--  底部  -->
        <!-- <div class="el-login-footer">
        <span>Copyright © 2018-2021 <a target="_blank" href="http://www.yunhewulian.com/">byc</a> All Rights Reserved.</span>
    </div> -->
    </div>
</template>

<script>
import 'element-ui/lib/theme-chalk/display.css';
import { getCodeImg, checkBindId, getErrorMsg } from '@/api/login';
import Cookies from 'js-cookie';
import { encrypt, decrypt } from '@/utils/jsencrypt';

export default {
    name: 'Login',
    data() {
        return {
            codeUrl: '',
            loginForm: {
                username: '',
                password: '',
                rememberMe: false,
                code: '',
                uuid: '',
                bindId: '',
            },
            loginRules: {
                username: [
                    {
                        required: true,
                        trigger: 'blur',
                        message: '请输入您的账号',
                    },
                ],
                password: [
                    {
                        required: true,
                        trigger: 'blur',
                        message: '请输入您的密码',
                    },
                ],
                code: [
                    {
                        required: true,
                        trigger: 'change',
                        message: '请输入验证码',
                    },
                ],
            },
            loading: false,
            // 验证码开关
            captchaOnOff: true,
            bindAccount: false,
            // 注册开关
            register: true,
            redirect: undefined,
        };
    },
    watch: {
        $route: {
            handler: function (route) {
                this.redirect = route.query && route.query.redirect;
            },
            immediate: true,
        },
    },
    created() {
        let loginId = this.$route.query.loginId;
        if (loginId === undefined || loginId === null) {
            this.checkBind();
            this.checkErrorMsg();
            this.getCode();
            this.getCookie();
        } else {
            this.redirectLogin(loginId);
        }
    },
    methods: {
        redirectLogin(loginId) {
            this.loading = true;
            this.$store
                .dispatch('RedirectLogin', loginId)
                .then(() => {
                    this.$router
                        .push({
                            path: this.redirect || '/',
                        })
                        .catch(() => {});
                })
                .catch(() => {
                    this.loading = false;
                    if (this.captchaOnOff) {
                        this.getCode();
                    }
                    this.$router.push({
                        query: {},
                    });
                });
        },
        checkBind() {
            let query = this.$route.query;
            let bindId = query.bindId;
            if (bindId === undefined || bindId === null) {
                this.bindAccount = false;
            } else {
                this.bindAccount = true;
                checkBindId(bindId).then((res) => {
                    this.bindAccount = res.bindAccount === undefined ? true : res.bindAccount;
                    if (this.bindAccount) {
                        this.loginForm.bindId = bindId;
                    } else {
                        this.loginForm.bindId = '';
                        this.$router.push({
                            query: {},
                        });
                    }
                });
            }
        },
        checkErrorMsg() {
            let errorId = this.$route.query.errorId;
            if (errorId !== undefined && errorId !== null) {
                getErrorMsg(errorId)
                    .then((res) => {})
                    .catch((err) => {
                        this.$router.push({
                            query: {},
                        });
                    });
            }
        },
        getCode() {
            getCodeImg().then((res) => {
                this.captchaOnOff = res.captchaOnOff === undefined ? true : res.captchaOnOff;
                if (this.captchaOnOff) {
                    this.codeUrl = 'data:image/gif;base64,' + res.img;
                    this.loginForm.uuid = res.uuid;
                }
            });
        },
        getCookie() {
            const username = Cookies.get('username');
            const password = Cookies.get('password');
            const rememberMe = Cookies.get('rememberMe');
            this.loginForm = {
                username: username === undefined ? this.loginForm.username : username,
                password: password === undefined ? this.loginForm.password : decrypt(password),
                rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
            };
        },
        qqLogin() {
            window.location.href = 'http://localhost:8080/auth/render/qq';
        },
        authLogin() {
            this.$alert('第三方登录正在集成中...', '提示消息', {
                confirmButtonText: '确定',
                callback: (action) => {
                    this.$message({
                        type: 'info',
                        message: `action: ${action}`,
                    });
                },
            });
        },
        handleLogin() {
            this.$refs.loginForm.validate((valid) => {
                if (valid) {
                    this.loading = true;
                    if (this.loginForm.rememberMe) {
                        Cookies.set('username', this.loginForm.username, {
                            expires: 30,
                        });
                        Cookies.set('password', encrypt(this.loginForm.password), {
                            expires: 30,
                        });
                        Cookies.set('rememberMe', this.loginForm.rememberMe, {
                            expires: 30,
                        });
                    } else {
                        Cookies.remove('username');
                        Cookies.remove('password');
                        Cookies.remove('rememberMe');
                    }
                    console.log(this.loginForm);
                    this.$store
                        .dispatch('Login', this.loginForm)
                        .then(() => {
                            this.$router
                                .push({
                                    path: this.redirect || '/',
                                })
                                .catch(() => {});
                        })
                        .catch(() => {
                            this.loading = false;
                            if (this.captchaOnOff) {
                                this.getCode();
                            }
                        });
                }
            });
        },
    },
};
</script>

<style lang="scss">
.login {
    position: relative;
    background: url('../assets/images/background.png') no-repeat;
    background-size: 100% 100%;
    height: 100%;
    overflow: auto;
}

.login-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.main {
    width: 100%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.login-img {
    position: absolute;
    top: 55%;
    right: 45%;
    transform: translateY(-50%);
}

.title {
    margin: 0px auto 20px auto;
    text-align: center;
    color: #06cae1;
    font-size: 28px;
}

.bindAccountTitle {
    margin: 0px auto 30px auto;
    text-align: center;
    color: #06cae1;
    font-size: 24px;
}
.login-top {
    position: absolute;
    top: 20px;
    left: 8%;
    color: #fff;
}
.login-top,
.login-img {
    @media screen and (min-width: 992px) {
        h1 {
            font-size: 36px;
        }

        h2 {
            font-size: 23px;
            margin-top: -20px;
        }
        &.login-img {
            width: 340px;
            height: 560px;
            right: 55%;
        }
    }

    @media screen and (min-width: 1200px) {
        h1 {
            font-size: 46px;
        }

        h2 {
            font-size: 29px;
            margin-top: -25px;
        }
        &.login-img {
            width: 440px;
            height: 680px;
            right: 50%;
        }
    }

    @media screen and (min-width: 1800px) {
        padding: 0px;

        h1 {
            font-size: 52px;
        }

        h2 {
            font-size: 33px;
            margin-top: -30px;
        }
        &.login-img {
            width: 500px;
            height: 780px;
            right: 45%;
        }
    }
}

.login-form {
    box-shadow: 0 20px 30px 0 rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    background: url('../assets/images/denlu.png') no-repeat;
    background-size: 100% 100%;
    padding: 25px 25px 5px 25px;
    margin: 0 auto;
    z-index: 1000;
    max-width: 370px;
    position: absolute;
    top: 50%;
    right: 18%;
    transform: translateY(-50%);

    .el-input {
        height: 38px;

        input {
            height: 38px;
            color: #06cae1 !important;
            border-width: 1px;
            border-color: rgba(#00ffff, 0.4);
            border-style: solid;
            border-radius: 4px;
            background-color: rgba(#2174f3, 0.4) !important;
            &:-webkit-autofill {
                -webkit-text-fill-color: #00ffff !important;
                -webkit-box-shadow: 0 0 0px 1000px transparent inset;
                transition: background-color 5000s ease-in-out 0s;
            }
        }

        // 谷歌
        input::-webkit-input-placeholder {
            color: #fff;
        }

        // 火狐19+版本
        input::-moz-placeholder {
            /* Mozilla Firefox 19+ */
            color: #fff;
        }

        // 火狐4-18版本
        input:-moz-placeholder {
            /* Mozilla Firefox 4 to 18 */
            color: #fff;
        }

        // IE10-11版本
        input:-ms-input-placeholder {
            /* Internet Explorer 10-11 */
            color: #fff;
        }
    }

    .input-icon {
        height: 39px;
        width: 14px;
        margin-left: 2px;
        color: #fff;
    }
}

.login-code {
    min-width: 93px;
    width: 33%;
    height: 38px;
    float: right;

    img {
        cursor: pointer;
        vertical-align: middle;
        border-radius: 5px;
        height: 38px;
    }
}

.el-login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
}
</style>
