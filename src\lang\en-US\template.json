{"template.index.891112-0": "name", "template.index.891112-1": "Please enter the name of the object model", "template.index.891112-2": "category", "template.index.891112-3": "Please select a model category", "template.index.891112-7": "identifier ", "template.index.891112-8": "Chart display", "template.index.891112-9": "Real time monitoring", "template.index.891112-10": "read-only", "template.index.891112-11": "Historical storage", "template.index.891112-12": "System definition", "template.index.891112-13": "Object model category", "template.index.891112-14": "data type", "template.index.891112-15": "Data definition", "template.index.891112-16": "sort", "template.index.891112-17": "Creation time", "template.index.891112-21": "System defined, cannot be modified", "template.index.891112-22": "Model Name", "template.index.891112-23": "Please enter the name of the object model, for example: temperature", "template.index.891112-24": "Model identification", "template.index.891112-25": "Please enter an identifier, for example: temperature", "template.index.891112-26": "Model sorting", "template.index.891112-27": "Please enter sorting", "template.index.891112-28": "Model category", "template.index.891112-29": "attribute", "template.index.891112-30": "function", "template.index.891112-31": "event", "template.index.891112-32": "Model characteristics", "template.index.891112-33": "Displayed in graphical form in device details", "template.index.891112-34": "Real time display of monitoring data, but not stored in the database", "template.index.891112-35": "The device reports data, but the platform cannot issue instructions", "template.index.891112-36": "Read only data", "template.index.891112-37": "The data reported by the device will be stored in the database as historical data", "template.index.891112-38": "When sharing devices, it is necessary to specify whether you have this permission", "template.index.891112-39": "Share permissions", "template.index.891112-40": "Please select a data type", "template.index.891112-41": "integer", "template.index.891112-42": "decimal", "template.index.891112-43": "Boolean", "template.index.891112-44": "enumeration", "template.index.891112-45": "character string", "template.index.891112-46": "array", "template.index.891112-47": "object", "template.index.891112-48": "Value range", "template.index.891112-49": "minimum value", "template.index.891112-50": "reach", "template.index.891112-51": "Maximum value", "template.index.891112-52": "unit", "template.index.891112-53": "Please enter the unit, for example: ℃", "template.index.891112-54": "step", "template.index.891112-55": "Please enter the step size, for example: 1", "template.index.891112-56": "Boolean value", "template.index.891112-57": "For example: Close", "template.index.891112-58": "(0 value corresponds to text)", "template.index.891112-59": "For example: Open", "template.index.891112-60": "(1 value corresponds to text)", "template.index.891112-61": "Display method", "template.index.891112-62": "Please choose the display method", "template.index.891112-63": "Dropdown box", "template.index.891112-64": "button", "template.index.891112-65": "Enumeration item", "template.index.891112-66": "Parameter value, for example: 0", "template.index.891112-67": "Parameter description, such as medium speed gear", "template.index.891112-68": "Add enumeration item", "template.index.891112-69": "Maximum length", "template.index.891112-70": "For example: 1024", "template.index.891112-71": "(Maximum length of string)", "template.index.891112-72": "Number of elements", "template.index.891112-73": "For example: 5", "template.index.891112-74": "Array type", "template.index.891112-75": "Object parameters", "template.index.891112-76": "Please select a device", "template.index.891112-78": "Add parameters", "template.index.891112-84": "The object model name cannot be empty", "template.index.891112-85": "Identifier, unique under the product cannot be empty", "template.index.891112-86": "Model sorting cannot be empty", "template.index.891112-87": "Model category cannot be empty", "template.index.891112-88": "The data type cannot be empty", "template.index.891112-89": "Add a universal object model", "template.index.891112-90": "Modify the universal object model", "template.index.891112-91": "The parameters of an object cannot be empty", "template.index.891112-92": "The input for object type model identification cannot contain an underline. Please fill in the model identification again!", "template.index.891112-93": "Parameter identification", "template.index.891112-95": "Please reselect the data type!", "template.index.891112-98": "Are you sure to delete the data item with the generic object model number {0}?", "template.index.891112-103": "low", "template.index.891112-104": "high", "template.index.891112-105": "Maximum value:", "template.index.891112-106": "Minimum value:", "template.index.891112-107": "Step size:", "template.index.891112-108": "Unit:", "template.index.891112-109": "Maximum length:", "template.index.891112-110": "Array type:", "template.index.891112-111": "Number of elements:", "template.index.891112-112": "open", "template.index.891112-113": "Put it away", "template.paramter.038405-0": "Edit parameters", "template.paramter.038405-1": "Please enter the name of the object model", "template.paramter.038405-2": "search", "template.paramter.038405-3": "Click on the application template", "template.paramter.038405-4": "choice", "template.paramter.038405-5": "name", "template.paramter.038405-6": "identifier ", "template.paramter.038405-7": "data type", "template.paramter.038405-8": "Parameter Name", "template.paramter.038405-9": "For example: temperature", "template.paramter.038405-10": "Parameter identification", "template.paramter.038405-11": "For example: temperature", "template.paramter.038405-12": "Parameter sorting", "template.paramter.038405-13": "Please enter sorting", "template.paramter.038405-14": "Parameter characteristics", "template.paramter.038405-15": "Chart display", "template.paramter.038405-16": "Real time monitoring", "template.paramter.038405-17": "Read only data", "template.paramter.038405-18": "Historical storage", "template.paramter.038405-19": "Sharing permission", "template.paramter.038405-20": "data type", "template.paramter.038405-21": "Please select data type", "template.paramter.038405-22": "integer", "template.paramter.038405-23": "decimal", "template.paramter.038405-24": "Boer", "template.paramter.038405-25": "enumeration", "template.paramter.038405-26": "character string", "template.paramter.038405-27": "Value range", "template.paramter.038405-28": "minimum value", "template.paramter.038405-29": "reach", "template.paramter.038405-30": "Maximum value", "template.paramter.038405-31": "Company", "template.paramter.038405-32": "For example: ℃", "template.paramter.038405-33": "step", "template.paramter.038405-34": "For example: 1", "template.paramter.038405-35": "Boolean value", "template.paramter.038405-36": "For example: Close", "template.paramter.038405-37": "(0 value corresponds to text)", "template.paramter.038405-38": "For example: Open", "template.paramter.038405-39": "(1 value corresponds to text)", "template.paramter.038405-40": "Display method", "template.paramter.038405-41": "Please choose a display method", "template.paramter.038405-42": "Drop down box", "template.paramter.038405-43": "<PERSON><PERSON>", "template.paramter.038405-44": "Enumeration item", "template.paramter.038405-45": "For example: 0", "template.paramter.038405-46": "For example: medium speed gear", "template.paramter.038405-47": "delete", "template.paramter.038405-48": "Add enumeration items", "template.paramter.038405-49": "Maximum length", "template.paramter.038405-50": "For example: 1024", "template.paramter.038405-51": "determine", "template.paramter.038405-52": "Cancel", "template.paramter.038405-53": "Parameter name cannot be empty", "template.paramter.038405-54": "Parameter identifier cannot be empty", "template.paramter.038405-55": "Model sorting cannot be empty", "template.paramter.038405-56": "The data type cannot be empty", "template.paramter.038405-57": "close", "template.paramter.038405-58": "open", "template.paramter.038405-59": "low", "template.paramter.038405-60": "high"}