// API测试脚本
// 用于测试前端API与新后端的兼容性

const axios = require('axios');

// 配置基础URL
const baseURL = 'http://localhost:8080';

// 创建axios实例
const api = axios.create({
    baseURL: baseURL,
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json;charset=utf-8'
    }
});

// 测试API列表
const testAPIs = [
    {
        name: '获取验证码',
        method: 'GET',
        url: '/captchaImage',
        description: '测试验证码接口'
    },
    {
        name: '获取产品列表',
        method: 'GET', 
        url: '/iot/product/list?pageNum=1&pageSize=10',
        description: '测试产品管理接口',
        needAuth: true
    },
    {
        name: '获取设备列表',
        method: 'GET',
        url: '/iot/device/list?pageNum=1&pageSize=10', 
        description: '测试设备管理接口',
        needAuth: true
    },
    {
        name: '获取用户列表',
        method: 'GET',
        url: '/system/user/list?pageNum=1&pageSize=10',
        description: '测试用户管理接口',
        needAuth: true
    },
    {
        name: '数据中心-设备历史数据',
        method: 'POST',
        url: '/data/center/deviceHistory',
        description: '测试数据中心接口',
        needAuth: true,
        data: {
            serialNumber: 'TEST001',
            startTime: '2024-01-01 00:00:00',
            endTime: '2024-12-31 23:59:59'
        }
    },
    {
        name: '场景模型列表',
        method: 'GET',
        url: '/scene/model/list?pageNum=1&pageSize=10',
        description: '测试场景模型接口',
        needAuth: true
    }
];

// 测试函数
async function testAPI(apiConfig) {
    try {
        console.log(`\n🧪 测试: ${apiConfig.name}`);
        console.log(`📝 描述: ${apiConfig.description}`);
        console.log(`🔗 URL: ${apiConfig.method} ${apiConfig.url}`);
        
        let response;
        if (apiConfig.method === 'GET') {
            response = await api.get(apiConfig.url);
        } else if (apiConfig.method === 'POST') {
            response = await api.post(apiConfig.url, apiConfig.data || {});
        }
        
        console.log(`✅ 状态码: ${response.status}`);
        console.log(`📊 响应数据结构:`, {
            hasCode: 'code' in response.data,
            hasMsg: 'msg' in response.data,
            hasData: 'data' in response.data,
            hasRows: 'rows' in response.data,
            hasTotal: 'total' in response.data
        });
        
        return { success: true, status: response.status, data: response.data };
    } catch (error) {
        console.log(`❌ 错误: ${error.message}`);
        if (error.response) {
            console.log(`📊 错误状态码: ${error.response.status}`);
            console.log(`📊 错误响应:`, error.response.data);
        }
        return { success: false, error: error.message };
    }
}

// 主测试函数
async function runTests() {
    console.log('🚀 开始API兼容性测试...');
    console.log(`🎯 目标服务器: ${baseURL}`);
    
    const results = [];
    
    for (const apiConfig of testAPIs) {
        const result = await testAPI(apiConfig);
        results.push({
            name: apiConfig.name,
            ...result
        });
        
        // 等待一秒避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 输出测试总结
    console.log('\n📋 测试总结:');
    console.log('='.repeat(50));
    
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    results.forEach(result => {
        const status = result.success ? '✅' : '❌';
        console.log(`${status} ${result.name}`);
    });
    
    console.log('='.repeat(50));
    console.log(`📊 成功率: ${successCount}/${totalCount} (${Math.round(successCount/totalCount*100)}%)`);
    
    if (successCount === totalCount) {
        console.log('🎉 所有API测试通过！前端与新后端完全兼容！');
    } else {
        console.log('⚠️  部分API测试失败，请检查后端服务状态或API变化');
    }
}

// 运行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { testAPI, runTests };
