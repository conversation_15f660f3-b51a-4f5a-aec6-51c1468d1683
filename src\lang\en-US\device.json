{"device.allot-import-dialog.060657-0": "Product", "device.allot-import-dialog.060657-1": "Please Select A Product", "device.allot-import-dialog.060657-2": "Target Organization", "device.allot-import-dialog.060657-3": "Please Select The Target Institution", "device.allot-import-dialog.060657-7": "1. Only xls and xlsx format files are allowed to be imported.", "device.allot-import-dialog.060657-8": "2. A maximum of 1000 devices can be allocated at a time, and when there are many devices at a time, it requires a longer verification and import time.", "device.allot-import-dialog.060657-9": "3. After uploading the file and assigning it, you can view the details of the failed devices in the Device List - More Operations - Device Import Record.", "device.allot-import-dialog.060657-10": "Device Allocation Template", "device.allot-import-dialog.060657-12": "Confirm Allocation", "device.allot-import-dialog.060657-13": "Import Allocation", "device.allot-import-dialog.060657-14": "Product Cannot Be Empty", "device.allot-import-dialog.060657-15": "The Target Organization Cannot Be Empty", "device.allot-import-dialog.060657-17": "Import Results", "device.allot-record.155854-0": "Equipment Allocation Records", "device.allot-record.155854-1": "Belonging Organization", "device.allot-record.155854-2": "Product Name", "device.allot-record.155854-5": "The Affiliation Of The Device Before Its Allocation", "device.allot-record.155854-7": "The Affiliation Of The Device After Allocation", "device.allot-record.155854-8": "Total Allocation", "device.allot-record.155854-9": "Allocation Successful", "device.allot-record.155854-10": "allocation Failed", "device.allot-record.155854-11": "Allocation Status", "device.allot-record.155854-12": "Allocation Method", "device.allot-record.155854-13": "Allocate Time", "device.allot-record.155854-15": "Download Details", "device.batch-import-dialog.850870-5": "Tip: Only xls and xlsx format files are allowed to be imported.", "device.batch-import-dialog.850870-6": "Download Device Import Template", "device.device-edit.148398-0": "*Basic Information", "device.device-edit.148398-1": "Device Name", "device.device-edit.148398-2": "Please Enter The Device Name", "device.device-edit.148398-3": "Abstract", "device.device-edit.148398-4": "Belonging Products", "device.device-edit.148398-5": "ProductName", "device.device-edit.148398-6": "Choice", "device.device-edit.148398-7": "Equipment Number", "device.device-edit.148398-8": "Please Enter The Device Number", "device.device-edit.148398-9": "Generate", "device.device-edit.148398-10": "The current TCP protocol is selected, and the device number is generated in HEX format", "device.device-edit.148398-11": "The currently selected product belongs to the modbus protocol and will generate sub devices based on the collection point template after the gateway device is created", "device.device-edit.148398-12": "Firmware Version", "device.device-edit.148398-13": "Please Enter The Firmware Version", "device.device-edit.148398-14": "Simulated Device", "device.device-edit.148398-15": "<PERSON><PERSON>", "device.device-edit.148398-16": "Disable Device", "device.device-edit.148398-17": "Remarks", "device.device-edit.148398-18": "Please enter remark", "device.device-edit.148398-19": "Positioning Method", "device.device-edit.148398-20": "Please Select Tthe Device Status", "device.device-edit.148398-21": "Equipment Longitude", "device.device-edit.148398-22": "Please Enter The Device Longitude", "device.device-edit.148398-23": "Coordinate Picking", "device.device-edit.148398-24": "Device Latitude", "device.device-edit.148398-25": "Please Enter The Device Latitude", "device.device-edit.148398-26": "Address", "device.device-edit.148398-27": "Please Enter The Address Where The Device Is Located", "device.device-edit.148398-28": "Network access address", "device.device-edit.148398-29": "Device Access IP", "device.device-edit.148398-30": "Activation Time", "device.device-edit.148398-31": "Device Activation Time", "device.device-edit.148398-32": "Equipment Signal", "device.device-edit.148398-33": "Equipment Signal Strength", "device.device-edit.148398-34": "Other Information", "device.device-edit.148398-35": "Certification Information", "device.device-edit.148398-36": "QR Code", "device.device-edit.148398-37": "Map Display Area, Displayed After Addition", "device.device-edit.148398-38": "Repair", "device.device-edit.148398-39": "Change", "device.device-edit.148398-40": "New", "device.device-edit.148398-41": "Increase", "device.device-edit.148398-42": "Running State ", "device.device-edit.148398-43": "Sub Devices", "device.device-edit.148398-44": "Device Channel", "device.device-edit.148398-45": "Live Streaming On Devices", "device.device-edit.148398-46": "Live Video Recording", "device.device-edit.148398-47": "<PERSON><PERSON>", "device.device-edit.148398-48": "<PERSON><PERSON>haring", "device.device-edit.148398-49": "Event Log", "device.device-edit.148398-50": "Instruction Log", "device.device-edit.148398-51": "Real Time Monitoring", "device.device-edit.148398-52": "Monitoring Statistics", "device.device-edit.148398-53": "Return To List", "device.device-edit.148398-54": "Summary (read-only data uploaded by devices)", "device.device-edit.148398-55": "Copy", "device.device-edit.148398-56": "Device QR Code", "device.device-edit.148398-57": "Close", "device.device-edit.148398-58": "Mqtt Connection Parameters", "device.device-edit.148398-59": "One Click Copy", "device.device-edit.148398-60": "The Device Name Cannot Be Empty", "device.device-edit.148398-61": "The Length Of The Device Name Is Between 2 And 32 Characters", "device.device-edit.148398-62": "The Firmware Version Cannot Be Empty", "device.device-edit.148398-63": "Received [Device Status - Details] Topic:", "device.device-edit.148398-64": "Received [Device Status - Details] content:", "device.device-edit.148398-65": "Equipment Number Cannot Be Empty", "device.device-edit.148398-66": "The Equipment Number Can Only Be Letters And Numbers", "device.device-edit.148398-67": "The Product Cannot Be Empty", "device.device-edit.148398-68": "Modified Successfully", "device.device-edit.148398-69": "Device Number Already Exists, Adding Device Failed", "device.device-edit.148398-70": "Successfully Added <PERSON><PERSON>", "device.device-edit.148398-71": "Copy Successful", "device.device-edit.148398-72": "Please Select The Product First", "device.device-edit.148398-73": "Configuration application", "device.device-edit.148398-74": "Data collection", "device.device-edit.148398-75": "video surveillance", "device.device-edit.148398-76": "Data debugging", "device.device-edit.148398-77": "Polling task", "device.device-edit.148398-78": "Gateway", "device.device-edit.148398-79": "Cloud recording", "device.device-edit.148398-80": "Alert user", "device.device-edit.148398-81": "equipment alarm ", "device.device-functionlog.399522-0": "Please Select A Device Slave:", "device.device-functionlog.399522-1": "Please Select A Device Slave", "device.device-functionlog.399522-2": "Slave Address:$", "device.device-functionlog.399522-3": "Log Type", "device.device-functionlog.399522-4": "Please Select The Type", "device.device-functionlog.399522-5": "Identifier ", "device.device-functionlog.399522-6": "Please Enter Identifier", "device.device-functionlog.399522-7": "Time Frame", "device.device-functionlog.399522-8": "Start Date", "device.device-functionlog.399522-9": "End Date", "device.device-functionlog.399522-10": "Search", "device.device-functionlog.399522-11": "Reset", "device.device-functionlog.399522-12": "Instruction Type", "device.device-functionlog.399522-13": "Setting Values", "device.device-functionlog.399522-15": "Issuing Time", "device.device-functionlog.399522-16": "Description Of Distribution Results", "device.device-functionlog.399522-18": "Delete", "device.device-functionlog.399522-19": "Register Address", "device.device-functionlog.399522-20": "The Identifier Cannot Be Empty", "device.device-functionlog.399522-21": "1=Service issuance, 2=Attribute acquisition, 3. OTA upgrade cannot be empty", "device.device-functionlog.399522-22": "The Log Value Cannot Be Empty", "device.device-functionlog.399522-23": "Equipment Number Cannot Be Empty", "device.device-functionlog.399522-24": "Are you sure to delete the data item with log number {0} issued by the device service?", "device.device-functionlog.399522-26": "Delete Successful", "device.device-linkage.188958-0": "Please Enter The Institution name", "device.device-linkage.188958-1": "Whole", "device.device-linkage.188958-2": "On Line", "device.device-linkage.188958-3": "Off-Line", "device.device-linkage.188958-5": "Device List", "device.device-linkage.188958-25": "Please Select The Device Status", "device.device-linkage.188958-26": "Equipment Longitude", "device.device-linkage.188958-27": "Please Enter The Device Longitude", "device.device-linkage.188958-28": "Coordinate Picking", "device.device-linkage.188958-29": "Device Latitude", "device.device-linkage.188958-30": "Please Enter The Device Latitude", "device.device-linkage.188958-31": "Address", "device.device-linkage.188958-32": "Please Enter The Address Where The Device Is Located", "device.device-linkage.188958-33": "Network Access Address", "device.device-linkage.188958-34": "Device Access IP", "device.device-linkage.188958-35": "Activation Time", "device.device-linkage.188958-36": "Device Activation Time", "device.device-linkage.188958-37": "Equipment Signal", "device.device-linkage.188958-38": "Equipment Signal Strength", "device.device-linkage.188958-39": "Other Information", "device.device-linkage.188958-40": "Certification Information", "device.device-linkage.188958-41": "QR Code", "device.device-linkage.188958-42": "Map Display Area, Displayed After Addition", "device.device-linkage.188958-43": "Running State ", "device.device-linkage.188958-44": "Sub Devices", "device.device-linkage.188958-45": "Device Channel", "device.device-linkage.188958-46": "Live Streaming On Devices", "device.device-linkage.188958-47": "Live Video Recording", "device.device-linkage.188958-48": "<PERSON><PERSON>", "device.device-linkage.188958-49": "<PERSON><PERSON>haring", "device.device-linkage.188958-50": "Event Log", "device.device-linkage.188958-51": "Instruction Log", "device.device-linkage.188958-52": "Real time Monitoring", "device.device-linkage.188958-53": "Monitoring Statistics", "device.device-linkage.188958-54": "Summary (read-only data uploaded by devices)", "device.device-linkage.188958-55": "Copy", "device.device-linkage.188958-56": "Device QR Code", "device.device-linkage.188958-57": "Close", "device.device-linkage.188958-58": "Mqtt Connection Parameters", "device.device-linkage.188958-59": "One Click Copy", "device.device-linkage.188958-60": "The Device Name Cannot Be Empty", "device.device-linkage.188958-61": "The Length Of The Device Name Is Between 2 And 32 Characters", "device.device-linkage.188958-62": "The Firmware Version Cannot Be Empty", "device.device-linkage.188958-63": "Received [Device Status - Details] Topic:", "device.device-linkage.188958-64": "Received [Device Status - Details] content:", "device.device-linkage.188958-65": "Equipment Number Cannot Be Empty", "device.device-linkage.188958-66": "The Equipment Number Can Only Be Letters And Numbers", "device.device-linkage.188958-67": "The Product Cannot Be Empty", "device.device-linkage.188958-68": "Modified Successfully", "device.device-linkage.188958-69": "Device Number Already Exists, Adding Device Failed", "device.device-linkage.188958-70": "Successfully Added <PERSON><PERSON>", "device.device-linkage.188958-71": "Copy Successful", "device.device-linkage.188958-72": "Please Select The Product First", "device.device-linkage.188958-73": "Alert <PERSON>r", "device.device-log.798283-0": "Log Type", "device.device-log.798283-1": "Please Select The Type", "device.device-log.798283-2": "Identifier ", "device.device-log.798283-3": "Please Enter Identifier", "device.device-log.798283-4": "Time Frame", "device.device-log.798283-5": "Start Date", "device.device-log.798283-6": "End Date", "device.device-log.798283-7": "Search", "device.device-log.798283-8": "Reset", "device.device-log.798283-9": "Type", "device.device-log.798283-10": "Mode", "device.device-log.798283-11": "Shadow Mode", "device.device-log.798283-12": "Online Mode", "device.device-log.798283-13": "Other Information", "device.device-log.798283-14": "Time", "device.device-log.798283-15": "Action", "device.device-log.798283-16": "Remarks", "device.device-log.798283-17": "Nothing", "device.device-log.798283-18": "Equipment Upgrade", "device.device-log.798283-19": "Device Online", "device.device-log.798283-20": "Device Offline", "device.device-log.798283-21": "Element", "device.device-monitor.817489-0": "Monitoring Interval", "device.device-monitor.817489-1": "Value Range: 500-10000 Milliseconds", "device.device-monitor.817489-2": "Please Enter The Monitoring Interval", "device.device-monitor.817489-3": "Monitoring Frequency", "device.device-monitor.817489-4": "Value Orientation 1-300", "device.device-monitor.817489-5": "Please Enter The Number Of Monitoring Times", "device.device-monitor.817489-6": "Start Monitoring", "device.device-monitor.817489-7": "Stop Monitoring", "device.device-monitor.817489-8": "Receiving Device Data, Please Be Patient And Wait", "device.device-monitor.817489-9": "Received [Device Status] Topic:", "device.device-monitor.817489-10": "Received [Device Status] Content:", "device.device-monitor.817489-11": "Received [Real Time Monitoring] Topic:", "device.device-monitor.817489-12": "Received Real-Time monitoring Content:", "device.device-monitor.817489-13": "Device Not Online, Failed To Issue Command", "device.device-monitor.817489-14": "Real Time Monitoring Interval Range Of 500-10000 Milliseconds", "device.device-monitor.817489-15": "Real Time Monitoring Quantity Range 1-300", "device.device-monitor.817489-16": "Update Real-Time Monitoring", "device.device-monitor.817489-17": "Turn Off Real-Time Monitoring", "device.device-monitor.817489-18": "(Unit)", "device.device-monitor.817489-19": "Nothing", "device.device-recycle.864193-0": "Equipment Recycling", "device.device-recycle.864193-1": "Recycling Institutions", "device.device-recycle.864193-2": "Please Select A Recycling Institution", "device.device-recycle.864193-5": "Query", "device.device-recycle.864193-6": "Reset", "device.device-recycle.864193-7": "All Devices", "device.device-recycle.864193-8": "There Is Currently No Data Available", "device.device-recycle.864193-12": "Selected Devices", "device.device-recycle.864193-13": "Determine Recycling", "device.device-recycle.864193-14": "Recycling Organization Cannot Be Empty", "device.device-select-allot.903153-0": "Assign <PERSON><PERSON>", "device.device-select-allot.903153-4": "Query", "device.device-select-allot.903153-5": "Reset", "device.device-select-allot.903153-6": "All Devices", "device.device-select-allot.903153-7": "There Is Currently No Data Available", "device.device-select-allot.903153-11": "Selected Devices", "device.device-select-allot.903153-14": "Determine Allocation", "device.device-statistic.932674-0": "Please Select A Device Slave:", "device.device-statistic.932674-1": "Please Select A Device Slave", "device.device-statistic.932674-2": "Time Frame", "device.device-statistic.932674-3": "Start Date", "device.device-statistic.932674-4": "End Date", "device.device-statistic.932674-5": "Query", "device.device-statistic.932674-6": "Statistics (Unit)", "device.device-statistic.932674-7": "Nothing", "device.device-sub.299018-2": "Device Status", "device.device-sub.299018-3": "Please Select The Device Status", "device.device-sub.299018-4": "Search", "device.device-sub.299018-5": "Reset", "device.device-sub.299018-7": "Gateway Encoding", "device.device-sub.299018-8": "Slave Address", "device.device-sub.299018-10": "Activation Time", "device.device-sub.299018-11": "Creation Time", "device.device-sub.299018-13": "Modify", "device.device-sub.299018-14": "Device Name", "device.device-sub.299018-15": "Please Enter The Device Name", "device.device-sub.299018-19": "The Device Name Cannot Be Empty", "device.device-sub.299018-20": "The Firmware Version Cannot Be Empty", "device.device-sub.299018-21": "Add <PERSON>", "device.device-sub.299018-22": "Modify Device", "device.device-sub.299018-23": "Modified Successfully", "device.device-sub.299018-24": "New Successfully Added", "device.device-sub.299018-25": "Are you sure to delete the data item with device number {0}?", "device.device-sub.299018-27": "Delete Successful", "device.device-sub.299018-28": "The default sub device address of the bound sub device inherits the product", "device.device-user.037521-0": "Refresh", "device.device-user.037521-1": "User ID", "device.device-user.037521-2": "User Name", "device.device-user.037521-3": "Mobile Phone Number", "device.device-user.037521-4": "Customer Type", "device.device-user.037521-5": "Owner", "device.device-user.037521-6": "Share", "device.device-user.037521-7": "Share Time", "device.device-user.037521-8": "Remarks", "device.device-user.037521-10": "Check", "device.device-user.037521-11": "Cancel Sharing", "device.device-user.037521-12": "<PERSON><PERSON>haring", "device.device-user.037521-13": "Please enter the user's mobile phone number", "device.device-user.037521-14": "Query Users", "device.device-user.037521-15": "User Information", "device.device-user.037521-16": "User ID:", "device.device-user.037521-17": "Mobile Number:", "device.device-user.037521-18": "User Name:", "device.device-user.037521-19": "Set User Permissions", "device.device-user.037521-20": "Permission Name", "device.device-user.037521-21": "Permission Identification", "device.device-user.037521-24": "Modify", "device.device-user.037521-25": "Close", "device.device-user.037521-26": "Mobile Phone Number Cannot Be Empty", "device.device-user.037521-27": "The Length Of The Mobile Phone Number Is 11 Digits", "device.device-user.037521-28": "Confirm To Cancel Sharing Device?", "device.device-user.037521-29": "Cancel Sharing Successful", "device.device-user.037521-30": "Unable to find device information. Please refresh and try again", "device.device-user.037521-31": "Unable to find user information, or the user is already a device user", "device.device-user.037521-32": "Equipment Upgrade", "device.device-user.037521-33": "Device OTA Upgrade", "device.device-user.037521-34": "<PERSON><PERSON>", "device.device-user.037521-35": "Timed Task Execution", "device.device-user.037521-36": "<PERSON><PERSON> Logs", "device.device-user.037521-37": "Contains Event Logs And Instruction Logs", "device.device-user.037521-38": "Real Time Monitoring", "device.device-user.037521-39": "After issuing real-time monitoring instructions, the chart displays the data reported by the device in real time", "device.device-user.037521-40": "Monitoring Statistics", "device.device-user.037521-41": "Chart Displays Stored Historical Monitoring Data", "device.device-user.037521-42": "Update Successful", "device.device-user.037521-43": "New Successfully Added", "device.device-timer.433369-0": "Timed Name", "device.device-timer.433369-1": "Please Enter The Timing Name", "device.device-timer.433369-2": "Timed State", "device.device-timer.433369-3": "Please Select The Scheduled Status", "device.device-timer.433369-4": "Search", "device.device-timer.433369-5": "Reset", "device.device-timer.433369-6": "New Addition", "device.device-timer.433369-7": "Name", "device.device-timer.433369-8": "Describe", "device.device-timer.433369-9": "CRON Expression", "device.device-timer.433369-10": "Action", "device.device-timer.433369-11": "State", "device.device-timer.433369-12": "Enable", "device.device-timer.433369-14": "Check", "device.device-timer.433369-15": "Detailed Timing", "device.device-timer.433369-16": "Delete", "device.device-timer.433369-17": "Execute Once", "device.device-timer.433369-18": "Execution Time", "device.device-timer.433369-19": "Select Time", "device.device-timer.433369-20": "Select Week", "device.device-timer.433369-21": "Please Select", "device.device-timer.433369-22": "Cron Expressions ", "device.device-timer.433369-23": "Cron Execution Expression", "device.device-timer.433369-24": "Generate Expression", "device.device-timer.433369-25": "Custom Expression", "device.device-timer.433369-26": "Execute Action", "device.device-timer.433369-27": "Please Select The Type", "device.device-timer.433369-28": "Please Select The Parent Object Model", "device.device-timer.433369-29": "Value", "device.device-timer.433369-30": "Please Enter A String", "device.device-timer.433369-31": "Add Execution Action", "device.device-timer.433369-32": "New Addition", "device.device-timer.433369-33": "Modify", "device.device-timer.433369-35": "Cron Expression Generator", "device.device-timer.433369-36": "Timing Number:", "device.device-timer.433369-37": "Timed Name:", "device.device-timer.433369-38": "Timed Grouping:", "device.device-timer.433369-39": "Creation Time:", "device.device-timer.433369-40": "Concurrent:", "device.device-timer.433369-41": "Allow", "device.device-timer.433369-42": "Prohibit", "device.device-timer.433369-43": "Cron Expression:", "device.device-timer.433369-44": "Execution Strategy:", "device.device-timer.433369-45": "Default Policy", "device.device-timer.433369-46": "Execute Immediately", "device.device-timer.433369-47": "Abandon Execution", "device.device-timer.433369-48": "Next Execution Time:", "device.device-timer.433369-49": "Timed State:", "device.device-timer.433369-50": "Normal", "device.device-timer.433369-51": "Suspend", "device.device-timer.433369-52": "Execute Action:", "device.device-timer.433369-53": "Close", "device.device-timer.433369-54": "Monday", "device.device-timer.433369-55": "Tuesday", "device.device-timer.433369-56": "Wednesday", "device.device-timer.433369-57": "Thursday", "device.device-timer.433369-58": "Friday", "device.device-timer.433369-59": "Saturday", "device.device-timer.433369-60": "Sunday", "device.device-timer.433369-61": "Attribute", "device.device-timer.433369-62": "Function", "device.device-timer.433369-63": "Timed Name Cannot Be Empty", "device.device-timer.433369-64": "Deactivate", "device.device-timer.433369-65": "Are you sure you want to schedule {0}?", "device.device-timer.433369-67": "Success", "device.device-timer.433369-68": "Confirm to execute {0} immediately", "device.device-timer.433369-69": "Execution Successful", "device.device-timer.433369-70": "Add Timing", "device.device-timer.433369-71": "Modify <PERSON>", "device.device-timer.433369-72": "No Unit", "device.device-timer.433369-73": "Are you sure to delete the data item with the scheduled timing number {0}?", "device.device-timer.433369-75": "Delete Successful", "device.device-timer.433369-76": "Time", "device.device-timer.433369-77": "Every Day", "device.device-timer.433369-78": "Monday", "device.device-timer.433369-79": "Tuesday", "device.device-timer.433369-80": "Wednesday", "device.device-timer.433369-81": "Thursday", "device.device-timer.433369-82": "Friday", "device.device-timer.433369-83": "Saturday", "device.device-timer.433369-84": "Sunday", "device.device-timer.433369-85": "Custom Cron Expression", "device.device-timer.433369-86": "Nothing", "device.device-timer.433369-87": "Execution Time Cannot Be Empty", "device.device-timer.433369-88": "Please Select The Week To Be Executed", "device.device-timer.433369-89": "The cron expression cannot be empty", "device.device-timer.433369-90": "The options and values in the execution action cannot be empty", "device.device-timer.433369-91": "Modified Successfully", "device.device-timer.433369-92": "New Successfully Added", "device.import-record.086254-0": "Equipment Import Record", "device.import-record.086254-2": "Batch Task Status", "device.import-record.086254-3": "Start Date", "device.import-record.086254-4": "End Date", "device.import-record.086254-5": "Search", "device.import-record.086254-6": "Reset", "device.import-record.086254-7": "Batch Number", "device.import-record.086254-8": "Total Number Of Devices", "device.import-record.086254-9": "Successful quantity", "device.import-record.086254-10": "Number Of Failures", "device.import-record.086254-11": "Task Status", "device.import-record.086254-12": "Completion Time", "device.import-record.086254-13": "Product Name", "device.device-modbus.433390-1": "Add polling task", "device.index.105953-0": "DeviceName", "device.index.105953-1": "Please Enter DeviceName", "device.index.105953-2": "DeviceNum", "device.index.105953-3": "Please Enter Devi<PERSON>Num", "device.index.105953-4": "Device Status", "device.index.105953-5": "Please Select The Device Status", "device.index.105953-6": "My Group", "device.index.105953-7": "Please Select My Group", "device.index.105953-8": "Search", "device.index.105953-9": "Reset", "device.index.105953-10": "New Equipment Added", "device.index.105953-11": "Manual Addition", "device.index.105953-12": "Batch import", "device.index.105953-13": "Assign <PERSON><PERSON>", "device.index.105953-14": "Select Allocation", "device.index.105953-15": "Import allocation", "device.index.105953-16": "Recycling Equipment", "device.index.105953-17": "Switch Display", "device.index.105953-18": "Display Subordinate Institution Data", "device.index.105953-19": "After selection, this level can view the data of subordinates", "device.index.105953-20": "Number", "device.index.105953-21": "ProductName", "device.index.105953-22": "Protocol", "device.index.105953-23": "Communication Protocol", "device.index.105953-24": "Number Of Sub Devices", "device.index.105953-26": "Enable", "device.index.105953-27": "Disabled", "device.index.105953-28": "State", "device.index.105953-29": "signal", "device.index.105953-32": "Activation time", "device.index.105953-33": "Creation time", "device.index.105953-35": "delete", "device.index.105953-36": "check", "device.index.105953-37": "QR code", "device.index.105953-38": "Shared devices", "device.index.105953-39": "ProductName", "device.index.105953-40": "running state ", "device.index.105953-41": "There is currently no data available, please add a device", "device.index.105953-42": "Device QR code", "device.index.105953-43": "Received [Device Status] Topic:", "device.index.105953-44": "Received [Device Status] content:", "device.index.105953-45": "Are you sure to delete the data item with device number {0}?", "device.index.105953-47": "Delete successful", "device.index.105953-48": "More operations", "device.index.105953-49": "Import records", "device.index.105953-50": "Generate device number", "device.index.105953-51": "Equipment Recycling Record", "device.index.105953-52": "Equipment allocation record", "device.index.105953-53": "Alert:", "device.index.105953-54": "Generate device number", "device.index.105953-55": "Generate quantity", "device.index.105953-56": "determine", "device.index.105953-57": "close", "device.index.105953-58": "No configuration available at the moment, please configure the template configuration first!", "device.product-list.058448-0": "Select product", "device.product-list.058448-2": "Please enter the product name", "device.product-list.058448-3": "search", "device.product-list.058448-4": "Reset", "device.product-list.058448-6": "Classification name", "device.product-list.058448-7": "Tenant Name", "device.product-list.058448-8": "Authorization code", "device.product-list.058448-9": "Enable", "device.product-list.058448-10": "not enabled", "device.product-list.058448-11": "Authentication method", "device.product-list.058448-12": "Networking methods", "device.product-list.058448-13": "Creation time", "device.product-list.058448-14": "confirm", "device.product-list.058448-15": "Close", "device.realTime-status.099127-0": "Real time status of the slave", "device.realTime-status.099127-1": "Attribute reporting", "device.realTime-status.099127-2": "Time:", "device.realTime-status.099127-3": "Service distribution", "device.realTime-status.099127-4": "send", "device.realTime-status.099127-5": "Equipment instructions", "device.realTime-status.099127-6": "Service distribution:", "device.realTime-status.099127-7": "Device response:", "device.realTime-status.099127-8": "There is currently no data available", "device.realTime-status.099127-9": "Please select a device slave", "device.realTime-status.099127-10": "Gateway real-time status", "device.realTime-status.099127-11": "Device Mode", "device.realTime-status.099127-12": "Please select", "device.realTime-status.099127-13": "Please enter a string", "device.realTime-status.099127-14": "Please enter a string, unit: {0}", "device.realTime-status.099127-15": "Instruction sending", "device.realTime-status.099127-16": "Device offline status", "device.realTime-status.099127-17": "Please enter decimals", "device.realTime-status.099127-18": "please enter an integer", "device.realTime-status.099127-19": "Service invocation", "device.realTime-status.099127-20": "Data range:", "device.realTime-status.099127-21": "cancel", "device.realTime-status.099127-22": "confirm", "device.realTime-status.099127-23": "Online mode", "device.realTime-status.099127-24": "Shadow mode", "device.realTime-status.099127-25": "Offline mode", "device.realTime-status.099127-26": "Issue instructions", "device.realTime-status.099127-27": "Service call successful!", "device.realTime-status.099127-28": "Received [<PERSON><PERSON> Status - Running] Topic:", "device.realTime-status.099127-29": "Received [Device Status - Running] content:", "device.realTime-status.099127-30": "Received [Object Model] Topic 1:", "device.realTime-status.099127-31": "Received the content of the object model:", "device.recycle-record.845969-0": "Equipment recycling records", "device.recycle-record.845969-1": "Institution name", "device.recycle-record.845969-4": "search", "device.recycle-record.845969-5": "Reset", "device.recycle-record.845969-6": "Name of recycling institution", "device.recycle-record.845969-8": "Recycling time", "device.user-list.041943-0": "Select Users", "device.user-list.041943-1": "Mobile phone number", "device.user-list.041943-2": "Please enter the user's mobile phone number", "device.user-list.041943-3": "query", "device.user-list.041943-5": "User ID", "device.user-list.041943-6": "User Name", "device.user-list.041943-7": "User nickname", "device.user-list.041943-8": "Creation time", "device.user-list.041943-9": "share", "device.user-list.041943-10": "Close", "device.user-list.041943-11": "Mobile phone number cannot be empty", "device.user-list.041943-12": "The length of the mobile phone number is 11 digits", "device.user-list.041943-13": "New successfully added", "device.running-status.866086-0": "Device Mode", "device.running-status.866086-1": "OTA upgrade", "device.running-status.866086-2": "Check for updates", "device.running-status.866086-3": "Please select", "device.running-status.866086-4": "Please enter a string", "device.running-status.866086-5": "Please enter a string, unit: {0}", "device.running-status.866086-6": "Instruction sending", "device.running-status.866086-7": "Please enter decimals", "device.running-status.866086-8": "please enter an integer", "device.running-status.866086-9": "Device offline status", "device.running-status.866086-10": "Device firmware upgrade", "device.running-status.866086-11": "It is already the latest version, no need to upgrade", "device.running-status.866086-12": "Can be upgraded to the following versions", "device.running-status.866086-13": "Firmware Name", "device.running-status.866086-16": "Download address", "device.running-status.866086-17": "Firmware Description", "device.running-status.866086-18": "Upgrade", "device.running-status.866086-20": "Equipment control", "device.running-status.866086-21": "Received [<PERSON><PERSON> Status - Running] Topic:", "device.running-status.866086-22": "Received [Device Status - Running] content:", "device.running-status.866086-23": "Received [Object Model] Topic 1:", "device.running-status.866086-24": "Received the content of the object model:", "device.running-status.866086-25": "Service call successful!", "device.running-status.866086-26": "Online mode", "device.running-status.866086-27": "Shadow mode", "device.running-status.866086-28": "Offline mode", "device.running-status.866086-29": "The number of elements does not match, the number of array elements is", "device.running-status.866086-30": "Number, separated by English commas.", "device.running-status.866086-31": "Equipment upgrade", "device.sub.083943-0": "Add sub devices", "device.sub.083943-1": "Remove sub devices", "device.sub.083943-2": "Set sub device address", "device.sub.083943-3": "Sub device address", "device.sub.083943-4": "Are you sure to delete the data item with device number {0]?", "device.sub.083943-5": "Save successful", "device.sub-device-list.323213-0": "Select device", "device.sub-device-list.323213-1": "Please enter the sub device number", "device.sub-device-list.323213-2": "name", "device.sub-device-list.323213-3": "Please enter the name of the sub device", "device.sub-device-list.323213-4": "Successfully added sub device", "device.scada.789543-0": "No configuration is available", "device.variable-case.347856-0": "Types of Variables", "device.variable-case.347856-1": "Please select a variable", "device.variable-case.347856-2": "Variable Name", "device.variable-case.347856-3": "Please enter a variable name", "device.variable-case.347856-4": "Search", "device.variable-case.347856-5": "Reset", "device.variable-case.347856-6": "Variable ID", "device.variable-case.347856-7": "Types of Variables", "device.variable-case.347856-8": "Variable Name", "device.variable-case.347856-9": "Turnover Time", "device.variable-case.347856-10": "Current Value", "device.variable-case.347856-11": "Operation", "device.variable-case.347856-12": "Historical Query", "device.variable-case.347856-13": "Active collection", "device.variable-case.347856-14": "Issue instructions to actively collect variables. Do you want to continue?", "device.variable-case.347856-15": "Tip", "device.variable-case.347856-16": "Collect all", "device.inline-video.986754-0": "No video", "iot.group.index.637432-0": "Group Name", "iot.group.index.637432-1": "Please enter the group name", "iot.group.index.637432-2": "My group", "iot.group.index.637432-3": "search", "iot.group.index.637432-4": "Reset", "iot.group.index.637432-5": "newly added", "iot.group.index.637432-6": "Group sorting", "iot.group.index.637432-7": "Creation time", "iot.group.index.637432-8": "User", "iot.group.index.637432-9": "remarks", "iot.group.index.637432-10": "operation", "iot.group.index.637432-11": "View devices", "iot.group.index.637432-12": "Add device", "iot.group.index.637432-13": "see", "iot.group.index.637432-14": "delete", "iot.group.index.637432-15": "Please enter grouping sorting", "iot.group.index.637432-16": "Please enter the content", "iot.group.index.637432-17": "Modify", "iot.group.index.637432-18": "newly added", "iot.group.index.637432-19": "Cancel", "iot.group.index.637432-20": "Group name cannot be empty", "iot.group.index.637432-21": "Group sorting cannot be empty, maximum value is 99", "iot.group.index.637432-22": "Add device groups", "iot.group.index.637432-23": "Modify device grouping", "iot.group.index.637432-24": "Modified successfully", "iot.group.index.637432-25": "New successfully added", "iot.group.index.637432-26": "Are you sure to delete the data item with device group number {0}?", "iot.group.index.637432-27": "Delete successful", "iot.group.device-list.849593-0": "Select device", "iot.group.device-list.849593-1": "Device Name", "iot.group.device-list.849593-2": "Please enter the device name", "iot.group.device-list.849593-3": "search", "iot.group.device-list.849593-4": "Device Name", "iot.group.device-list.849593-5": "Equipment number", "iot.group.device-list.849593-6": "Product Name", "iot.group.device-list.849593-7": "Equipment type", "iot.group.device-list.849593-8": "share", "iot.group.device-list.849593-9": "have", "iot.group.device-list.849593-10": "Device status", "iot.group.device-list.849593-11": "determine", "iot.group.device-list.849593-12": "Cancel", "iot.group.device-list.849593-13": "Successfully updated devices under group", "device.instruction-parsing.830424-0": "Equipment number issued:", "device.instruction-parsing.830424-1": "Manual issuance", "device.instruction-parsing.830424-2": "<PERSON><PERSON> return", "device.instruction-parsing.830424-3": "Equipment reporting", "device.instruction-parsing.830424-4": "Parsing data", "device.instruction-parsing.830424-5": "Resolved", "device.instruction-parsing.830424-6": "Model Name:", "device.instruction-parsing.830424-7": "Identifier:", "device.instruction-parsing.830424-8": "Data value:", "device.instruction-parsing.830424-9": "More analysis", "device.instruction-parsing.830424-10": "Please enter the content", "device.instruction-parsing.830424-11": "Please select", "device.instruction-parsing.830424-12": "Distribute", "device.instruction-parsing.830424-13": "Reporting", "device.instruction-parsing.830424-14": "send out", "device.instruction-parsing.830424-15": "Common shortcut commands", "device.instruction-parsing.830424-16": "Instruction generation", "device.instruction-parsing.830424-17": "Add to frequently used shortcut keys", "device.instruction-parsing.830424-18": "Generate instruction data", "device.instruction-parsing.830424-19": "Slave address", "device.instruction-parsing.830424-20": "Instruction type", "device.instruction-parsing.830424-21": "Function code", "device.instruction-parsing.830424-22": "Starting register address", "device.instruction-parsing.830424-23": "Register value", "device.instruction-parsing.830424-24": "Register value", "device.instruction-parsing.830424-25": "Coil status value", "device.instruction-parsing.830424-26": "Click to generate preview", "device.instruction-parsing.830424-27": "Add to frequently used", "device.instruction-parsing.830424-28": "Copy data", "device.instruction-parsing.830424-29": "Instruction Name", "device.instruction-parsing.830424-30": "Please enter", "device.instruction-parsing.830424-31": "Instruction data", "device.instruction-parsing.830424-32": "cancel", "device.instruction-parsing.830424-33": "determine", "device.instruction-parsing.830424-34": "Delete frequently used shortcut keys", "device.instruction-parsing.830424-35": "confirm deletion", "device.instruction-parsing.830424-36": "Is it?", "device.instruction-parsing.830424-37": "cancel", "device.instruction-parsing.830424-38": "determine", "device.instruction-parsing.830424-39": "More analysis", "device.instruction-parsing.830424-40": "Number", "device.instruction-parsing.830424-41": "Model Name", "device.instruction-parsing.830424-42": "identifier ", "device.instruction-parsing.830424-43": "Data value", "device.instruction-parsing.830424-44": "Number of coils", "device.instruction-parsing.830424-45": "Number of registers", "device.instruction-parsing.830424-46": "Coil value", "device.instruction-parsing.830424-47": "Register value", "device.instruction-parsing.830424-48": "01 Read coil status", "device.instruction-parsing.830424-49": "02 Read input status", "device.instruction-parsing.830424-50": "03 Read Save Register", "device.instruction-parsing.830424-51": "04 Read input register", "device.instruction-parsing.830424-52": "05 Write to a single coil register", "device.instruction-parsing.830424-53": "06 Write to a single save register", "device.instruction-parsing.830424-54": "15 Write multiple coil states", "device.instruction-parsing.830424-55": "16 Write multiple save registers", "device.instruction-parsing.830424-56": "Distribute", "device.instruction-parsing.830424-57": "Reporting", "device.instruction-parsing.830424-58": "edit", "device.instruction-parsing.830424-59": "newly added", "device.instruction-parsing.830424-60": "Quick command successful", "device.instruction-parsing.830424-61": "Quick command failure", "device.instruction-parsing.830424-62": "Encoding failed", "device.instruction-parsing.830424-63": "Instruction cannot be empty", "device.instruction-parsing.830424-64": "fail in send", "device.instruction-parsing.830424-65": "Resolve to Empty", "device.instruction-parsing.830424-66": "Encoding failed", "device.instruction-parsing.830424-67": "edit", "device.instruction-parsing.830424-68": "delete", "device.instruction-parsing.830424-69": "Delete command successful", "device.instruction-parsing.830424-70": "Delete command failed", "device.device-modbus-task.384302-0": "Timed Name", "device.device-modbus-task.384302-1": "Please Enter The Timing Name", "device.device-modbus-task.384302-2": "Timed State", "device.device-modbus-task.384302-3": "Please Select The Scheduled Status", "device.device-modbus-task.384302-4": "Search", "device.device-modbus-task.384302-5": "Reset", "device.device-modbus-task.384302-6": "New Addition", "device.device-modbus-task.384302-7": "Name", "device.device-modbus-task.384302-8": "Describe", "device.device-modbus-task.384302-9": "CRON Expression", "device.device-modbus-task.384302-10": "Action", "device.device-modbus-task.384302-11": "Delete", "device.device-modbus-task.384302-12": "Add to frequently used shortcut keys", "device.device-modbus-task.384302-13": "Generate instruction data", "device.device-modbus-task.384302-14": "Slave address", "device.device-modbus-task.384302-15": "Function code", "device.device-modbus-task.384302-16": "Starting register address", "device.device-modbus-task.384302-17": "Register value", "device.device-modbus-task.384302-18": "Coil status value", "device.device-modbus-task.384302-19": "cycle time", "device.device-modbus-task.384302-20": "Periodic cycle", "device.device-modbus-task.384302-21": "In scenarios where the cycle rule applies, a fixed cycle of natural day polling is conducted once.", "device.device-modbus-task.384302-22": "For example: Polling once a day at 07:00 (value range: 07:00 today to 07:00 yesterday)", "device.device-modbus-task.384302-23": "each", "device.device-modbus-task.384302-24": "Poll once", "device.device-modbus-task.384302-25": "Click to generate preview", "device.device-modbus-task.384302-26": "Copy data", "device.device-modbus-task.384302-27": "cancel", "device.device-modbus-task.384302-28": "determine", "device.device-modbus-task.384302-29": "Number of coils", "device.device-modbus-task.384302-30": "Number of registers", "device.device-modbus-task.384302-31": "Coil value", "device.device-modbus-task.384302-32": "Register value", "device.device-modbus-task.384302-33": "01 Read coil status", "device.device-modbus-task.384302-34": "02 Read input status", "device.device-modbus-task.384302-35": "03 Read Save Register", "device.device-modbus-task.384302-36": "04 Read input register", "device.device-modbus-task.384302-37": "05 Write to a single coil register", "device.device-modbus-task.384302-38": "06 Write to a single save register", "device.device-modbus-task.384302-39": "15 Write multiple coil states", "device.device-modbus-task.384302-40": "16 Write multiple save registers", "device.device-modbus-task.384302-41": "Encoding failed", "device.device-modbus-task.384302-42": "Enable", "device.device-modbus-task.384302-43": "Deactivate", "device.device-modbus-task.384302-44": "Are you sure you want to schedule {0}?", "device.device-modbus-task.384302-45": "Success", "device.device-modbus-task.384302-46": "Time", "device.device-modbus-task.384302-47": "Every Day", "device.device-modbus-task.384302-48": "Monday", "device.device-modbus-task.384302-49": "Tuesday", "device.device-modbus-task.384302-50": "Wednesday", "device.device-modbus-task.384302-51": "Thursday", "device.device-modbus-task.384302-52": "Friday", "device.device-modbus-task.384302-53": "Saturday", "device.device-modbus-task.384302-54": "Sunday", "device.device-modbus-task.384302-55": "Custom Cron Expression", "device.device-modbus-task.384302-56": "Task ID", "device.device-modbus-task.384302-57": "instructions", "device.device-modbus-task.384302-58": "state", "device.device-modbus-task.384302-59": "Timed Time", "device.device-modbus-task.384302-60": "operation", "device.device-modbus-task.384302-61": "delete", "device.device-modbus-task.384302-62": "Modified successfully", "device.device-modbus-task.384302-63": "New successfully added", "device.device-modbus-task.384302-64": "Are you sure to delete the data item with column number {0} for the rotation training task?", "device.device-modbus-task.384302-65": "Delete successfully", "order.index.045965-1": "Add command permission control", "order.index.045965-2": "Modify instruction permission control", "order.index.045965-3": "Are you sure to delete the data item with instruction permission control number {0}?", "order.index.045965-4": "Directive", "order.index.045965-5": "device", "order.index.045965-6": "User", "order.index.045965-7": "number of actions", "order.index.045965-8": "Start Time", "order.index.045965-9": "End Time", "order.index.045965-10": "Select device", "order.index.045965-11": "Select User", "order.index.045965-12": "Instruction configuration", "order.index.045965-13": "Select All", "order.index.045965-14": "Number of operations", "order.index.045965-15": "start time", "order.index.045965-16": "Please choose a start time", "order.index.045965-17": "End time", "order.index.045965-18": "Please select an end time", "order.index.045965-19": "Select file", "order.index.045965-20": "picture", "order.index.045965-21": "remarks", "order.index.045965-22": "Please enter the content", "order.index.045965-23": "determine", "order.index.045965-24": "Cancel", "order.index.045965-25": "Please select a device", "order.index.045965-26": "Please select a user", "order.index.045965-27": "The number of operations cannot be empty", "order.index.045965-28": "Please choose a start time", "order.index.045965-29": "Please select an end time", "order.index.045965-30": "Select at least one instruction", "device.device-alert.309509-0": "Alarm name ", "device.device-alert.309509-1": "Please enter the alarm Name", "device.device-alert.309509-2": "Alarm level", "device.device-alert.309509-3": "Please select the alarm level", "device.device-alert.309509-4": "Processing status", "device.device-alert.309509-5": "Please select the processing status", "device.device-alert.309509-6": "search", "device.device-alert.309509-7": "Reset", "device.device-alert.309509-8": "Alarm source ", "device.device-alert.309509-9": "Alarm time", "device.device-alert.309509-10": "data", "device.device-alert.309509-11": "Processing status", "device.device-alert.309509-12": "operation", "device.device-alert.309509-13": "handle", "device.device-alert.309509-14": "Processing results", "device.device-alert.309509-15": "Please enter the content", "device.device-alert.309509-16": "determine", "device.device-alert.309509-17": "Cancel", "device.device-alert.309509-18": "The processing content cannot be empty", "device.device-alert.309509-19": "Modify device alarms", "device.device-alert.309509-20": "Modified successfully", "device.device-alert.309509-21": "New successfully added", "device.device-variable.930930-0": "Device not activated", "device.device-variable.930930-1": "The device is in a disabled state", "device.device-variable.930930-2": "The device is offline", "device.device-variable.930930-3": "Issued successfully", "device.device-variable.930930-4": "Please enter data", "device.device-variable.930930-5": "edit", "device.device-variable.930930-6": "determine", "device.device-variable.930930-7": "cancel", "device.device-variable.930930-8": "Please enter data", "device.device-variable.930930-9": "Device not activated", "device.device-variable.930930-10": "The device is in a disabled state", "device.device-variable.930930-11": "The device is offline", "device.realTime-status.845353-0": "edit", "device.realTime-status.845353-1": "Issue instructions", "device.realTime-status.845353-2": "Report time:", "device.realTime-status.845353-3": "Issue instructions", "device.realTime-status.845353-4": "cancel", "device.realTime-status.845353-5": "determine", "device.realTime-status.845353-6": "Issued successfully"}