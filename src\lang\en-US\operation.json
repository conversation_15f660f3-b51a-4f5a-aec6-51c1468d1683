{"protocol.index.111542-0": "Protocol Name", "protocol.index.111542-1": "Please enter the protocol name", "protocol.index.111542-2": "Protocol encoding", "protocol.index.111542-3": "Please enter the protocol code", "protocol.index.111542-4": "Protocol Summary", "protocol.index.111542-5": "Upload address", "protocol.index.111542-6": "Protocol type", "simulate.index.111543-0": "Please enter the device name", "simulate.index.111543-1": "choice", "simulate.index.111543-2": "Equipment name (number)", "simulate.index.111543-3": "Sub devices", "simulate.index.111543-4": "Polling status", "simulate.index.111543-5": "Modbus device simulator", "simulate.index.111543-6": "Time:", "simulate.index.111543-7": "Topic:", "simulate.index.111543-8": "Content；", "simulate.index.111543-9": "Please select Payload", "simulate.index.111543-10": "Please select QoS", "simulate.index.111543-11": "0 (maximum once)", "simulate.index.111543-12": "1 (at least once)", "simulate.index.111543-13": "2 (available and only once)", "simulate.index.111543-14": "Please select a theme", "simulate.index.111543-15": "Message Editor", "simulate.index.111543-16": "Please use the message editor to generate", "simulate.index.111543-17": "send", "simulate.index.111543-18": "Attribute reporting", "simulate.index.111543-19": "Service distribution", "simulate.index.111543-20": "There is currently no data available", "simulate.index.111543-21": "Please select a device slave", "simulate.index.111543-22": "Service invocation", "simulate.index.111543-23": "Data range:", "simulate.index.111543-24": "Please select the protocol:", "simulate.index.111543-25": "Please select the protocol", "simulate.index.111543-26": "Currently only supports the modbus protocol", "simulate.index.111543-27": "Message decoding", "simulate.index.111543-28": "Input message to be parsed", "simulate.index.111543-29": "analysis", "simulate.index.111543-30": "Read instruction generation", "simulate.index.111543-31": "Slave address", "simulate.index.111543-32": "Slave address, such as: 1", "simulate.index.111543-33": "Register Address", "simulate.index.111543-34": "Function code", "simulate.index.111543-35": "Please select the function code", "simulate.index.111543-36": "03- Read Holding Register", "simulate.index.111543-37": "04- Read Input Register", "simulate.index.111543-38": "Number of reads", "simulate.index.111543-39": "generate", "simulate.index.111543-40": "Generate and copy to send box", "simulate.index.111543-41": "Message:", "simulate.index.111543-42": "Write instruction generation", "simulate.index.111543-43": "Write value", "simulate.index.111543-44": "Input value", "simulate.index.111543-45": "CRC generation/verification", "simulate.index.111543-46": "Message to be verified", "simulate.index.111543-47": "check", "simulate.index.111543-48": "Result:", "simulate.index.111543-49": "Please select the device first", "simulate.index.111543-50": "The theme of device simulation cannot be empty", "simulate.index.111543-51": "The message content simulated by the device cannot be empty", "simulate.index.111543-52": "Command issued successfully", "simulate.index.111543-53": "Service call successful", "simulate.index.111543-54": "Enable", "simulate.index.111543-55": "Deactivate", "simulate.index.111543-56": "Success"}